/**
 * Hinglish Response Generator
 * A simple rule-based system to generate natural-sounding Hinglish responses
 * for the chat application.
 */

// Track recent responses to avoid repetition
const recentResponses = {
  'doctor': [],
  'rahul': [],
  'priya': [],
  'amit': [],
  'neha': []
};

// Maximum number of recent responses to remember per contact
const MAX_RECENT_RESPONSES = 5;

// Track response counts for each contact
const responseCounter = {
  'doctor': 0,
  'rahul': 0,
  'priya': 0,
  'amit': 0,
  'neha': 0
};

// Maximum responses before a contact goes offline
const MAX_RESPONSES_BEFORE_OFFLINE = 5;

// Track contact status
let contactStatus = {
  'doctor': 'online',
  'rahul': 'online',
  'priya': 'away',
  'amit': 'offline',
  'neha': 'online'
};

// Track conversation context
let conversationContext = {
  'doctor': {
    topic: null,
    lastMessageType: null,
    questionAsked: false,
    mentionedSubstance: false
  },
  'rahul': {
    topic: null,
    lastMessageType: null,
    questionAsked: false
  },
  'priya': {
    topic: null,
    lastMessageType: null,
    questionAsked: false
  },
  'amit': {
    topic: null,
    lastMessageType: null,
    questionAsked: false
  },
  'neha': {
    topic: null,
    lastMessageType: null,
    questionAsked: false
  }
};

// Common Hinglish phrases and expressions
const hinglishPhrases = {
  greetings: [
    "Kaise ho Arjun Patil?",
    "Hello Arjun bhai, kya chal raha hai?",
    "Namaste Arjun ji! Aap kaise hain?",
    "Arjun bhai, kya haal chaal hai?",
    "Hey Arjun! Sab theek?",
    "Arrey Arjun, kahan busy the aajkal?"
  ],

  positive: [
    "Bahut badhiya!",
    "Ekdum mast hai!",
    "That's great yaar!",
    "Kya baat hai!",
    "Awesome hai!",
    "Sahi hai ekdum!"
  ],

  negative: [
    "Koi baat nahi yaar",
    "Tension mat lo",
    "Don't worry, sab theek ho jayega",
    "Thoda patience rakho",
    "Itna stress kyun le rahe ho?",
    "Relax karo, koi problem nahi hai"
  ],

  questions: [
    "Kya soch rahe ho?",
    "Aaj kya plan hai?",
    "Weekend pe kya karne wale ho?",
    "Tumhara din kaisa gaya?",
    "College mein sab kaise chal raha hai?",
    "Kuch special baat karni hai?"
  ],

  fillers: [
    "Haan yaar",
    "Accha",
    "Theek hai",
    "Hmm",
    "Samajh gaya",
    "Bilkul",
    "Haan ji"
  ],

  substance: [
    "Ganja use karna health ke liye accha nahi hai Arjun",
    "Addiction se bahar nikalna important hai, main help karunga",
    "Yaar, tumhari health important hai, please try to quit",
    "Meditation se kaafi help milegi addiction control karne mein",
    "Yoga aur exercise try karo, it will help with cravings",
    "Family aur friends ka support bahut important hai recovery mein"
  ],

  // Colorful responses for when curse words are detected
  colorful: {
    'doctor': [
      "Arjun, aise language use karna appropriate nahi hai. Professional relationship maintain karein.",
      "Main aapka doctor hoon, aise baat karna theek nahi hai. Respect maintain karein.",
      "Medical professional ke saath aise language use karna appropriate nahi hai.",
      "Arjun, gussa control karein. Aise language se koi problem solve nahi hogi.",
      "As your doctor, main recommend karunga ki aap apna language thoda control karein.",
      "Arjun Patil, ye clinic hai. Aise language yahan acceptable nahi hai.",
      "Main samajhta hoon aap frustrated hain, lekin aise words use karna theek nahi hai.",
      "Arjun, aise language se aapki health problems solve nahi hongi.",
      "Therapy mein aise language use karna counterproductive hai.",
      "Arjun, main aapka doctor hoon, dost nahi. Professional rahein.",
      "Aise language use karne se aapki addiction recovery mein problems ho sakti hain.",
      "Gussa control karna bhi recovery ka part hai, Arjun."
    ],
    'rahul': [
      "Abbe chutiye, kya bakwaas kar raha hai tu?",
      "Bsdk, dimag kharab hai kya tera?",
      "Abe lodu, kya problem hai tere ko?",
      "Gaandu, chal nikal. Mood kharab kar diya tune.",
      "Bhosadike, itna attitude kahan se laya?",
      "Chutiya hai kya? Kuch bhi bolta rehta hai.",
      "Teri maa ki chut, kya bakchodi kar raha hai?",
      "Laude ke baal, chal party karte hain, mood theek ho jayega.",
      "Bhen ke laude, itna gussa kyun hai be?",
      "Randi ke bacche, chal beer peete hain.",
      "Maa ke laude, tu bhi na! Kuch bhi bolta rehta hai.",
      "Jhaant ke pissu, kya ukhaad lega tu?",
      "Teri maa ka bhosada, itna attitude kahan se laya?",
      "Chandan laude, chal movie dekhne chalte hain.",
      "Gandu bhagat, tu bhi na kuch bhi bolta hai.",
      "Bhikari ki aulad, paise udhar de pehle.",
      "Tere baap ki gaand mein danda, itna attitude mat dikha.",
      "Teri behen ki chut, chal party karte hain.",
      "Madarchod, tu bhi na! Kuch bhi bolta rehta hai.",
      "Bhosdike, chal daaru peete hain."
    ],
    'priya': [
      "Arjun, thoda tameez se baat kar. Bakwaas band kar.",
      "Kya bakchodi kar raha hai? Dimag theek hai?",
      "Aise baat karega to main baat nahi karungi. Chutiyapa band kar.",
      "Bhai, ye kya language hai? Thoda control kar.",
      "Arjun, ye kya bakwaas hai? Thoda respect dikha.",
      "Chutiye, assignment complete kar pehle, phir bakchodi karna.",
      "Bsdk, class mein aise baat karega to professor ke paas bhej dungi.",
      "Gadhe ke bacche, padhai pe dhyan de, ye sab bakwaas nahi.",
      "Tere jaise chutiye ko help karna hi galti thi meri.",
      "Lodu, assignment khud kar le. Main help nahi karungi.",
      "Bhosadike, aise baat karega to fail ho jayega tu.",
      "Chutiya hai kya? Professor ko bata dungi tera behavior.",
      "Gaandu, library mein chup reh. Bahar baat karenge.",
      "Bsdk, notes maangne aayega to muh pe darwaza band kar dungi.",
      "Chutiye ka bacha, padhai kar le thodi."
    ],
    'amit': [
      "Abbe chutiye, kya bakwaas kar raha hai?",
      "Bsdk, cricket match ke baare mein baat kar, ye sab bakwaas nahi.",
      "Gaandu, chal gym chalte hain, mood theek ho jayega tera.",
      "Bhosadike, itna gussa kyun hai tu?",
      "Chutiya hai kya? Chill kar bhai.",
      "Laude ke baal, match haar gaye isliye itna gussa hai kya?",
      "Maa ke laude, protein shake pee aur shaant ho ja.",
      "Bhosdiwale, gym mein saari energy nikal diyo.",
      "Teri gaand main danda, aise hi chillata rahega kya?",
      "Randi ke, chal beer peete hain, sab tension bhool jayega.",
      "Jhaatu, cricket match dekh aur chill kar.",
      "Teri maa ki chut, batting practice karne chalega?",
      "Behen ke laude, cricket match haar gaye to kya hua?",
      "Chutmarike, gym mein bench press karenge aaj.",
      "Gaand ke andhe, match dekh le pehle phir baat kar.",
      "Bhosdi wale, protein shake pee aur chill kar.",
      "Madarchod, tu bhi na! Kuch bhi bolta rehta hai.",
      "Lund fakir, cricket ke baare mein baat kar."
    ],
    'neha': [
      "Arjun, ye kya language hai? Main aise baat nahi karti.",
      "Excuse me? Thoda tameez se baat karo.",
      "Arjun Patil, ye kya tarika hai baat karne ka?",
      "Seriously? Aise baat karoge toh main jaa rahi hoon.",
      "Arjun, control your language. This is not acceptable.",
      "Chutiye, library mein aise language use mat kar.",
      "Bakchodi band kar, assignment pe focus kar.",
      "Dimag kharab hai kya? Aise baat karta hai koi?",
      "Tere jaise lodu ke saath project karna hi galti thi.",
      "Bsdk, literature ke baare mein baat kar, ye bakwaas nahi.",
      "Chutiya, assignment deadline yaad hai na?",
      "Gadhe, class notes lene aayega to muh pe darwaza band kar dungi.",
      "Lodu, aise language use karega to help nahi karungi.",
      "Bhosadike, library mein chup reh. Bahar baat karenge."
    ]
  }
};

// Hindi sentence endings
const hindiEndings = [
  "yaar",
  "bhai",
  "na",
  "ji",
  "samjhe",
  "theek hai",
  "hai na",
  "dekho"
];

// English words commonly used in Hinglish
const englishInHinglish = [
  "definitely",
  "exactly",
  "obviously",
  "seriously",
  "actually",
  "basically",
  "literally",
  "totally"
];

/**
 * Check if a message contains curse words
 * @param {string} message - The message to check
 * @return {boolean} True if the message contains curse words
 */
function containsCurseWords(message) {
  const curseWords = [
    // Original list
    'chodu', 'bhosadike', 'bsdk', 'randi', 'chutiya', 'chutiye', 'lodu', 'gaandu',
    'madarchod', 'behenchod', 'mc', 'bc', 'fuck', 'asshole', 'bitch', 'bastard',
    'lund', 'lauda', 'gand', 'chut', 'jhatu', 'bhosdi', 'bhenchod', 'lavde', 'harami',
    'saala', 'kutte', 'kamina', 'bakchod', 'bakchodi', 'alude',

    // Additional requested terms
    'laude', 'teri maa ki chut', 'teri maa ka bhosada', 'randi ka bacha', 'ganda phatike',
    'bhikari', 'randi teri maa', 'gandu bhagat', 'chandan laude',

    // Previous additions
    'tatti', 'kutta', 'suar', 'ullu ka patha', 'gadha', 'bhen ke laude', 'maa ke laude',
    'chutmarike', 'gaand mara', 'jhaatu', 'bhosdiwala', 'choot', 'choot ke baal',
    'chutad', 'dalla', 'hijra', 'kuttiya', 'maderchod', 'raand', 'saali', 'tatte',
    'bhadwa', 'bhosad pappu', 'chinaal', 'chodu bhagat', 'gaandu', 'gote', 'jhaant',
    'kaminey', 'lavdu', 'muth', 'najayaz', 'rundi', 'chut ke dhakkan', 'chut ke pasine',
    'gaand ke andhe', 'jhaant ke pissu', 'teri gaand main danda', 'teri maa ki',
    'tere baap ki', 'beti chod', 'bhoot ki choot', 'bhosdi wala', 'rand ke bacche',
    'teri jhaant', 'bawasir', 'choot ka bhoot', 'gaand ka kida', 'lundtopi', 'suwar ki aulad',

    // New additions to reach 200
    // Basic variations and combinations
    'chutiye ka bacha', 'lund fakir', 'gaand ke keede', 'chut ke dhakkan', 'lund ke baal',
    'jhaat ke baal', 'gaandu insaan', 'chutiya insaan', 'randi rona', 'chut marani',
    'lund choos', 'gaand chaat', 'maa chuda', 'baap chuda', 'behen chuda', 'chut ka maindak',
    'lund ka keeda', 'gaand ka maindak', 'chut ka pujari', 'lund ka pujari', 'gaand ka pujari',

    // More complex phrases
    'apni gaand mein ungli kar', 'apni maa ko bech', 'apni behen ko bech', 'teri maa ki aankh',
    'teri behen ki aankh', 'tere baap ki aankh', 'teri maa ka doodh', 'teri behen ka doodh',
    'tere baap ka lund', 'teri maa ki gaand', 'teri behen ki gaand', 'tere baap ki gaand',
    'teri maa ka bhosda', 'teri behen ka bhosda', 'tere baap ka bhosda', 'teri maa ka bur',
    'teri behen ka bur', 'tere baap ka bur', 'teri maa ki choot', 'teri behen ki choot',

    // Regional variations
    'panchod', 'pencho', 'paanchod', 'penchod', 'benchod', 'banchod', 'bancho', 'pehli fursat mein nikal',
    'gaand mara', 'gaand maraa', 'gaand marao', 'chull machi hai', 'bhosdi ke', 'bhonsdi ke',
    'bhosad ke', 'bhosad pappu', 'bhosad ka', 'bhosad wala', 'bhosad wale', 'bhosad waale',

    // English variations
    'motherfucker', 'motherfucking', 'motherfuckers', 'fucker', 'fuckers', 'fucking', 'fucked',
    'dickhead', 'dickheads', 'dickface', 'dickwad', 'dickweed', 'dickless', 'dicksucker',
    'cocksucker', 'cocksuckers', 'cocksucking', 'cockface', 'cockhead', 'shithead', 'shitheads',
    'shitface', 'shitbag', 'shitbrains', 'bullshit', 'bullshitter', 'horseshit', 'douchebag',
    'douchebags', 'douchey', 'asshat', 'asswipe', 'asslicker', 'assfucker', 'assmunch',

    // More Hindi variations
    'chutad', 'chutan', 'chutiya ka', 'chutiya ke', 'chutiya ho', 'chutiya hai', 'chutiyapa',
    'chutiyapanti', 'chutiyagiri', 'chutiyapan', 'chutiyapana', 'chutiyapanti kar', 'chutiyapanti mat kar',
    'lund pe chadh', 'lund pe baith', 'lund pe chad', 'lund pe char', 'lund pe charh', 'lund pe charha',
    'gaand mein ghus', 'gaand mein ghusa', 'gaand mein ghuso', 'gaand mein daal', 'gaand mein daala',
    'gaand mein daalo', 'gaand mein lele', 'gaand mein lelo', 'gaand mein le le', 'gaand mein le lo',

    // More complex combinations
    'teri maa ki chut mein', 'teri behen ki chut mein', 'tere baap ki gaand mein',
    'teri maa ki gaand mein', 'teri behen ki gaand mein', 'tere baap ke lund pe',
    'teri maa ke bhosde mein', 'teri behen ke bhosde mein', 'tere baap ke bhosde mein',
    'teri maa ke bur mein', 'teri behen ke bur mein', 'tere baap ke bur mein',
    'teri maa ke choot mein', 'teri behen ke choot mein', 'tere baap ke choot mein',

    // Slang and euphemisms
    'l*nd', 'g*nd', 'c**t', 'ch*t', 'b*c', 'm*c', 'b*dk', 'r*ndi', 'ch**iya',
    'l**d', 'g**d', 'c**t', 'ch**', 'b**c', 'm**c', 'b**k', 'r**di', 'ch***ya',
    'l***', 'g***', 'c***', 'ch***', 'b***', 'm***', 'b***', 'r***i', 'ch****a'
  ];

  // Convert message to lowercase for case-insensitive matching
  const lowerMessage = message.toLowerCase();

  // Check if any curse word is in the message
  return curseWords.some(word => lowerMessage.includes(word));
}

/**
 * Generate a Hinglish response based on input message
 * @param {string} message - The user's message
 * @param {string} contactId - The ID of the contact responding
 * @return {string} A Hinglish response or null if contact is offline
 */
function generateHinglishResponse(message, contactId) {
  // Check if contact is offline
  if (contactStatus[contactId] === 'offline') {
    console.log(`Contact ${contactId} is offline and cannot respond`);
    return null;
  }

  // Convert message to lowercase for easier matching
  const lowerMessage = message.toLowerCase();

  // Initialize context for this contact if it doesn't exist
  if (!conversationContext[contactId]) {
    conversationContext[contactId] = {
      topic: null,
      lastMessageType: null,
      questionAsked: false
    };
  }

  // Update conversation context based on message content
  updateConversationContext(lowerMessage, contactId);

  let response;

  // Check if message contains curse words
  if (containsCurseWords(message)) {
    console.log(`Curse words detected in message: "${message}"`);

    // Get colorful responses for this contact
    if (hinglishPhrases.colorful[contactId]) {
      response = getRandomItem(hinglishPhrases.colorful[contactId]);
    } else {
      // Default colorful response if none exists for this contact
      response = "Arrey yaar, ye kya language hai? Thoda tameez se baat karo.";
    }
  }
  // Check for substance-related keywords for doctor
  else if (contactId === 'doctor' && containsSubstanceKeywords(lowerMessage)) {
    conversationContext[contactId].mentionedSubstance = true;
    conversationContext[contactId].topic = 'substance';
    response = generateDoctorSubstanceResponse();
  }
  // Check for greetings
  else if (isGreeting(lowerMessage)) {
    conversationContext[contactId].topic = 'greeting';
    response = getRandomItem(hinglishPhrases.greetings);
  }
  // Check for questions
  else if (lowerMessage.includes('?')) {
    conversationContext[contactId].questionAsked = true;
    response = handleQuestion(lowerMessage, contactId);
  }
  // Generate contextual response based on message content and contact
  else {
    response = generateContextualHinglishResponse(contactId);
  }

  // Check if this response was recently used
  if (recentResponses[contactId] && recentResponses[contactId].includes(response)) {
    // Try to get a different response up to 3 times
    for (let i = 0; i < 3; i++) {
      const newResponse = generateAlternativeResponse(contactId);
      if (!recentResponses[contactId].includes(newResponse)) {
        response = newResponse;
        break;
      }
    }
  }

  // Add this response to recent responses
  addToRecentResponses(contactId, response);

  // Increment response counter
  responseCounter[contactId]++;

  // Check if contact should go offline
  if (responseCounter[contactId] >= MAX_RESPONSES_BEFORE_OFFLINE) {
    // Add a goodbye message if this is the last response
    if (responseCounter[contactId] === MAX_RESPONSES_BEFORE_OFFLINE) {
      const goodbyeMessages = {
        'doctor': "Arjun, mujhe abhi clinic jana hai. Hum baad mein baat karenge. Take care of yourself.",
        'rahul': "Bhai, battery low ho rahi hai. Baad mein baat karte hain. Chal, bye!",
        'priya': "Sorry Arjun, mujhe library jana hai abhi. Will talk later!",
        'amit': "Arrey yaar, match start hone wala hai. Baad mein baat karte hain!",
        'neha': "Arjun, class ke liye nikalna hai. Catch up later!"
      };

      response = goodbyeMessages[contactId] || "Sorry, I have to go now. Talk to you later!";

      // Set contact to offline
      contactStatus[contactId] = 'offline';

      // Notify the chat application that the contact is now offline
      if (typeof window !== 'undefined' && typeof window.setContactOffline === 'function') {
        window.setContactOffline(contactId);
      }

      console.log(`Contact ${contactId} is now offline after ${MAX_RESPONSES_BEFORE_OFFLINE} responses`);
    }
  }

  return response;
}

/**
 * Update conversation context based on message content
 */
function updateConversationContext(message, contactId) {
  // Detect topics in the message
  const topics = detectTopics(message);
  if (topics.length > 0) {
    conversationContext[contactId].topic = topics[0];
  }

  // Check if message is a question
  conversationContext[contactId].questionAsked = message.includes('?');

  // Update last message type
  if (isGreeting(message)) {
    conversationContext[contactId].lastMessageType = 'greeting';
  } else if (message.includes('?')) {
    conversationContext[contactId].lastMessageType = 'question';
  } else {
    conversationContext[contactId].lastMessageType = 'statement';
  }
}

/**
 * Detect topics in a message
 */
function detectTopics(message) {
  const topicKeywords = {
    'college': ['college', 'university', 'class', 'study', 'exam', 'professor', 'lecture', 'assignment'],
    'cricket': ['cricket', 'match', 'game', 'sport', 'team', 'player', 'score', 'bat', 'ball'],
    'movies': ['movie', 'film', 'cinema', 'actor', 'actress', 'bollywood', 'theater', 'show'],
    'health': ['health', 'doctor', 'medicine', 'hospital', 'sick', 'illness', 'disease', 'treatment'],
    'substance': ['ganja', 'weed', 'drugs', 'addiction', 'smoking', 'drink', 'alcohol', 'substance'],
    'food': ['food', 'eat', 'restaurant', 'dinner', 'lunch', 'breakfast', 'hungry', 'meal']
  };

  const detectedTopics = [];

  for (const [topic, keywords] of Object.entries(topicKeywords)) {
    if (keywords.some(keyword => message.includes(keyword))) {
      detectedTopics.push(topic);
    }
  }

  return detectedTopics;
}

/**
 * Generate an alternative response when the first one was recently used
 */
function generateAlternativeResponse(contactId) {
  // Get the current context
  const context = conversationContext[contactId];

  // Generate a response based on the current context
  if (context.questionAsked) {
    return `${getRandomItem(hinglishPhrases.fillers)}, ${getRandomItem(hinglishPhrases.questions)}`;
  } else if (context.topic) {
    // Get topic-specific responses
    const topicResponses = {
      'college': [
        "College life bhi na, kitna pressure hota hai!",
        "Assignments complete karne mein help chahiye?",
        "Professor ne kuch naya bataya class mein?",
        "Exams ke liye prepare kar rahe ho?"
      ],
      'cricket': [
        "Cricket match dekhne ka plan banate hain",
        "India ki team aajkal bahut accha perform kar rahi hai",
        "Weekend pe match dekhne milte hain?",
        "Kohli ka performance kaisa raha last match mein?"
      ],
      'movies': [
        "New movie release hui hai, dekhne chaloge?",
        "OTT pe koi acchi series recommend kar sakte ho?",
        "Weekend pe movie night rakhte hain",
        "Last movie kaun si dekhi tumne?"
      ],
      'health': [
        "Health is wealth, Arjun. Take care of yourself.",
        "Regular exercise karte ho?",
        "Healthy diet follow karna important hai",
        "Mental health bhi utni hi important hai jitni physical health"
      ],
      'substance': [
        "Addiction se bahar nikalna important hai",
        "Support groups join karne se help milegi",
        "Meditation try karo, it helps with cravings",
        "Step by step progress karoge to success milegi"
      ]
    };

    if (topicResponses[context.topic]) {
      return getRandomItem(topicResponses[context.topic]);
    }
  }

  // Default to a generic response
  return `${getRandomItem(hinglishPhrases.fillers)}, aur batao kya chal raha hai?`;
}

/**
 * Add a response to the recent responses list
 */
function addToRecentResponses(contactId, response) {
  if (!recentResponses[contactId]) {
    recentResponses[contactId] = [];
  }

  // Add the new response
  recentResponses[contactId].push(response);

  // Keep only the most recent responses
  if (recentResponses[contactId].length > MAX_RECENT_RESPONSES) {
    recentResponses[contactId].shift(); // Remove the oldest response
  }
}

/**
 * Check if message contains substance-related keywords
 */
function containsSubstanceKeywords(message) {
  const keywords = ['ganja', 'weed', 'drugs', 'addiction', 'nasha', 'high', 'joint', 'marijuana', 'cannabis'];
  return keywords.some(keyword => message.includes(keyword));
}

/**
 * Generate a doctor's response about substance use
 */
function generateDoctorSubstanceResponse() {
  // Get more detailed substance-related responses
  const substanceResponses = [
    "Ganja use karna brain development ko affect karta hai, especially young adults mein.",
    "Addiction se recovery ek journey hai, ek din mein nahi hota. Patience rakhna important hai.",
    "Withdrawal symptoms manage karne ke liye proper medical guidance zaruri hai.",
    "Regular exercise aur meditation addiction se ladne mein help karte hain.",
    "Support groups join karna beneficial ho sakta hai recovery journey mein.",
    "Family aur friends ka support bahut important hai addiction se bahar aane mein.",
    "Substance use ke alternatives dhundhna important hai, jaise ki sports ya creative activities.",
    "Triggers identify karna aur unse bachna important hai recovery ke liye."
  ];

  // Add a professional touch for the doctor
  const professionalAdditions = [
    "As your doctor, main recommend karunga ki",
    "Medical perspective se dekha jaye to",
    "Research shows ki",
    "Mere experience mein, maine dekha hai ki",
    "Health ki perspective se",
    "Scientific studies indicate ki",
    "Medical community ka consensus hai ki",
    "Aapke health ke liye important hai ki"
  ];

  // Get a response
  const response = getRandomItem(substanceResponses);

  // Check if this response was recently used
  if (recentResponses['doctor'] && recentResponses['doctor'].includes(response)) {
    // Try to get a different response
    for (let i = 0; i < substanceResponses.length; i++) {
      const newResponse = substanceResponses[i];
      if (!recentResponses['doctor'].includes(newResponse)) {
        return addDoctorProfessionalTouch(newResponse, professionalAdditions);
      }
    }
  }

  return addDoctorProfessionalTouch(response, professionalAdditions);
}

/**
 * Add a professional touch to the doctor's response
 */
function addDoctorProfessionalTouch(response, professionalAdditions) {
  // 60% chance to add a professional prefix
  if (Math.random() > 0.4) {
    return `${getRandomItem(professionalAdditions)} ${response}`;
  }

  return response;
}

/**
 * Check if a message is a greeting
 */
function isGreeting(message) {
  const greetings = ['hi', 'hello', 'hey', 'namaste', 'kaise ho', 'kem cho', 'kya haal', 'good morning', 'good afternoon', 'good evening'];
  return greetings.some(greeting => message.includes(greeting));
}

/**
 * Handle questions in Hinglish
 */
function handleQuestion(message, contactId) {
  // Get the current context
  const context = conversationContext[contactId] || {};

  // Common question responses
  const questionResponses = {
    'kaise ho': [
      "Main bilkul theek hoon, aap batao?",
      "Ekdum fit hoon yaar! Tum batao?",
      "Mast hoon, aur tum kaise ho?",
      "All good! Aap kaise hain?",
      "Bas badhiya chal raha hai. Aapka kya haal hai?",
      "Accha feel kar raha hoon aaj. Aap kaise ho?",
      "Sab kuch theek hai mere taraf se. Aapki taraf kya chal raha hai?",
      "Main bilkul fit hoon. Aap kaise feel kar rahe ho aajkal?",
      "Mast hoon yaar! Tum batao, kya chal raha hai?",
      "Ekdum badhiya! Aur tumhara kya haal hai?",
      "Theek thaak hoon. Tumhare saath kya ho raha hai?",
      "Changa si! Tusi dasso, ki haal hai?",
      "Sab changa si! Tum sunao, kya haal hai?",
      "All good! Aap batao, kya chal raha hai?",
      "Bas theek hoon. Aap kaise ho?",
      "Zindagi chal rahi hai. Aap kaise hain?",
      "Kaam chal raha hai. Aap batao, kya haal hai?",
      "Badhiya hoon main. Aap kaise ho?",
      "First class! Aap batao kya scene hai?",
      "Bindaas hoon yaar! Tu bata kya chal raha hai?"
    ],
    'kya kar rahe ho': [
      "Bas thoda kaam kar raha hoon, aur tum?",
      "Nothing much yaar, just chilling",
      "Kuch khaas nahi, just regular stuff",
      "Tumse baat kar raha hoon, obviously!",
      "Bas chill kar raha hoon. Tum batao?",
      "Kuch khaas nahi, just relaxing. Tum kya kar rahe ho?",
      "Netflix dekh raha tha. Tumhara kya scene hai?",
      "Abhi free hoon, kuch plan hai kya?",
      "Game khel raha tha. Tum batao kya kar rahe ho?",
      "College ke assignment pe kaam kar raha hoon. Tum?",
      "Bas time pass kar raha hoon. Kuch interesting batao.",
      "Cricket match dekh raha tha. Score pata hai?",
      "Phone pe scroll kar raha tha. Kuch interesting mila nahi abhi tak.",
      "Ghar pe hi hoon, chill kar raha hoon. Tum batao?",
      "Kuch nahi yaar, boring day hai. Tum kya kar rahe ho?",
      "Music sun raha tha. Koi accha gaana recommend karo.",
      "Padhai kar raha tha, break liya abhi. Tum batao?",
      "Bas abhi free hoon. Kuch plan hai?",
      "Kuch soch raha tha. Tum kya kar rahe ho?",
      "Aise hi time pass kar raha tha. Tum batao?"
    ],
    'college': [
      "College mein sab theek hai. Assignments bahut hain lekin manage kar raha hoon.",
      "Classes acchi chal rahi hain. Professor ne interesting topics cover kiye hain.",
      "College life busy hai but maza aa raha hai. Tum batao?",
      "Exams ke liye prepare kar raha hoon. Tension hai thodi.",
      "College life kaisi chal rahi hai? Assignments manage kar pa rahe ho?",
      "Classes regular attend kar rahe ho? Attendance important hoti hai.",
      "College mein konse subjects pasand hain tumhe?",
      "Exams ke liye preparation start kar di hai?",
      "College ke extra-curricular activities mein participate karte ho?",
      "Professors se regular interaction maintain karte ho?",
      "College library ka use karte ho study ke liye?",
      "Group study karte ho ya akele prefer karte ho?",
      "College projects pe kaam shuru kiya hai?",
      "College friends ke saath weekend plans kya hain?",
      "College canteen ka khana kaisa hai? Favorite kya hai?",
      "College ke events mein participate karte ho?",
      "Internship ke liye apply kiya hai kisi jagah?",
      "College ke career counseling services use karte ho?",
      "College ke baad kya plans hain? Higher studies ya job?",
      "College life enjoy kar rahe ho overall?"
    ],
    'cricket': [
      "Cricket match bahut exciting tha! Last over mein result decide hua.",
      "India ki team aajkal bahut accha perform kar rahi hai, especially batting lineup.",
      "IPL mein kaun si team support karte ho?",
      "Weekend pe match dekhne ka plan hai? Mere ghar pe aa jao.",
      "Last cricket match dekha? Kaisa laga?",
      "Favorite cricket player kaun hai tumhara?",
      "IPL mein konsi team support karte ho?",
      "Cricket khelte ho ya sirf dekhte ho?",
      "World Cup ke predictions kya hain tumhare?",
      "Test cricket ya T20, kya prefer karte ho?",
      "Cricket statistics follow karte ho regularly?",
      "Fantasy cricket khelte ho online?",
      "Local cricket tournaments attend karte ho?",
      "Cricket equipment collect karte ho?",
      "Cricket commentary kaun accha lagta hai tumhe?",
      "Cricket history mein interest hai?",
      "Cricket coaching li hai kabhi?",
      "Cricket ke rules sab pata hain tumhe?",
      "Cricket match dekhne stadium gaye ho kabhi?",
      "Cricket ke iconic moments kaunse hain tumhare hisab se?"
    ],
    'movie': [
      "Latest movie dekhi? Reviews bahut acche hain.",
      "OTT platforms pe bahut saari acchi movies aa gayi hain.",
      "Weekend pe movie night plan karte hain, friends ke saath.",
      "Bollywood ya Hollywood? Kaunsi movies pasand hain tumhe?",
      "Latest release dekhi tumne? Kaisi lagi?",
      "Favorite genre kya hai movies mein?",
      "Kaunse director ki movies sabse zyada pasand hain?",
      "Classic films dekhte ho ya sirf new releases?",
      "Movie theater mein dekhna prefer karte ho ya ghar pe?",
      "Kaunsi upcoming movie ke liye excited ho?",
      "Favorite actor/actress kaun hai tumhara?",
      "Movie reviews follow karte ho ya apne hisaab se dekhte ho?",
      "Foreign films dekhte ho with subtitles?",
      "Movie soundtracks sunte ho separately?",
      "Film festivals attend karte ho kabhi?",
      "Documentaries dekhte ho ya sirf fiction?",
      "Animated movies pasand hain?",
      "Movie collection maintain karte ho?",
      "Friends ke saath movie discussions karte ho?",
      "All-time favorite movie kaunsi hai tumhari?"
    ],
    'health': [
      "Regular exercise karte ho? Kya routine hai?",
      "Diet kaisa maintain karte ho?",
      "Sleep schedule kaisa hai tumhara?",
      "Mental health ke liye kya karte ho?",
      "Meditation try kiya hai kabhi?",
      "Water intake kitna hai daily?",
      "Health checkups regularly karate ho?",
      "Stress management ke liye kya techniques use karte ho?",
      "Work-life balance maintain kar pate ho?",
      "Health goals kya hain tumhare is saal ke?",
      "Fitness tracker use karte ho?",
      "Yoga practice karte ho regularly?",
      "Health supplements lete ho koi?",
      "Screen time limit karte ho?",
      "Outdoor activities kitni karte ho?",
      "Health related information kahan se lete ho?",
      "Family health history aware ho?",
      "Nutrition ke baare mein kitna jaante ho?",
      "Self-care routine kya hai tumhara?",
      "Health insurance hai tumhare paas?"
    ],
    'food': [
      "Favorite cuisine kya hai tumhari?",
      "Cooking karte ho ya order in prefer karte ho?",
      "Spicy food handle kar sakte ho?",
      "Street food pasand hai ya fine dining?",
      "Food experiments karte ho kabhi?",
      "Favorite restaurant kaunsa hai tumhara?",
      "Vegetarian ho ya non-vegetarian?",
      "Desserts pasand hain? Kaunse?",
      "Healthy eating follow karte ho?",
      "Food allergies hain koi?",
      "Exotic cuisines try karte ho?",
      "Cooking shows dekhte ho?",
      "Food photography karte ho?",
      "Family recipes follow karte ho?",
      "Meal planning karte ho weekly?",
      "Food festivals attend karte ho?",
      "Comfort food kya hai tumhara?",
      "Food blogs follow karte ho?",
      "Cooking classes join ki hain kabhi?",
      "Food preferences change hue hain over time?"
    ]
  };

  // Topic-specific questions based on context
  if (context.topic && questionResponses[context.topic]) {
    return getRandomItem(questionResponses[context.topic]);
  }

  // Check for specific question types
  for (const [questionType, responses] of Object.entries(questionResponses)) {
    if (message.includes(questionType)) {
      return getRandomItem(responses);
    }
  }

  // For doctor, check if substance was mentioned
  if (contactId === 'doctor' && context.mentionedSubstance) {
    const substanceQuestions = [
      "Aapne koshish ki hai ganja use kam karne ki? Koi progress hui?",
      "Meditation ya yoga try kiya hai? It can help with cravings.",
      "Aapke friends bhi substance use karte hain? Peer pressure important factor hota hai.",
      "Kya aap regularly counseling sessions attend kar rahe hain?",
      "Withdrawal symptoms experience kar rahe hain koi? Manage karne mein help chahiye?",
      "Ganja use karne ke triggers identify kiye hain aapne?",
      "Alternative stress management techniques try kiye hain?",
      "Family ko situation ke baare mein bataya hai? Support milta hai?",
      "Physical exercise routine follow kar rahe hain? Endorphins help karte hain mood mein.",
      "Sleep pattern kaisa hai aajkal? Insomnia experience kar rahe hain?",
      "Appetite changes notice kiye hain recently?",
      "Concentration problems face kar rahe hain studies mein?",
      "Support groups ke baare mein socha hai join karne ke liye?",
      "Relapse prevention strategies discuss karna chahenge?",
      "Substance use diary maintain kar rahe hain? Patterns identify karne mein help karta hai.",
      "Cravings manage karne ke liye kya techniques use kar rahe hain?",
      "Mental health symptoms notice kiye hain koi? Anxiety ya depression?",
      "Medication options discuss karna chahenge addiction management ke liye?",
      "Recovery goals set kiye hain? Short-term aur long-term dono?",
      "Progress track karne ka koi system follow kar rahe hain?"
    ];
    return getRandomItem(substanceQuestions);
  }

  // Generic question response with context awareness
  if (context.lastMessageType === 'question') {
    // If the user just asked a question, respond with a statement
    return `${getRandomItem(hinglishPhrases.fillers)}, main bhi yahi soch raha tha. Aur batao, kya chal raha hai?`;
  } else {
    // Otherwise respond with a question
    return `${getRandomItem(hinglishPhrases.fillers)}, ${getRandomItem(hinglishPhrases.questions)}`;
  }
}

/**
 * Generate a contextual Hinglish response
 */
function generateContextualHinglishResponse(contactId) {
  // Get the current context
  const context = conversationContext[contactId] || {};

  // Contact-specific responses with context awareness
  const contactResponses = {
    'doctor': [
      "Apni health ka dhyan rakhna bahut important hai Arjun",
      "Regular exercise aur healthy diet follow karo",
      "Stress management ke liye meditation try karo",
      "Apne symptoms ke baare mein aur batao",
      "Medication regularly le rahe ho na?",
      "Addiction se recovery ek process hai, patience rakhna zaroori hai",
      "Aapki progress dekh kar mujhe khushi ho rahi hai",
      "Ganja use kam karne se aapki mental clarity improve hogi",
      "Withdrawal symptoms normal hain, gradually kam ho jayenge",
      "Support group join karna consider kiya aapne?",
      "Family ko involve karna recovery mein helpful ho sakta hai",
      "Apne triggers identify kiye hain aapne?",
      "Stress management techniques practice kar rahe hain?",
      "Sleep pattern kaisa hai aajkal?",
      "Hydration aur nutrition pe dhyan de rahe hain?",
      "Physical activity se mood improvement hota hai",
      "Journaling se emotions track karna helpful ho sakta hai",
      "Mindfulness exercises try kiye hain aapne?",
      "Next appointment mein hum detailed discussion karenge",
      "Koi side effects ho to turant contact karein mujhe",
      "Alternative relaxation techniques discuss karenge next time",
      "Progress slow hogi, but consistent rahegi agar aap effort dalenge",
      "Relapse part of recovery hai, don't be too hard on yourself",
      "Aapki determination dekh kar accha lagta hai"
    ],
    'rahul': [
      "Chal bhai, movie dekhne chalte hain weekend pe",
      "Arrey yaar, cricket match dekha tune?",
      "Kya scene hai college mein?",
      "Bro, party kab de raha hai tu?",
      "Tension mat le yaar, sab theek ho jayega",
      "Naya game download kiya maine, aaja khelte hain",
      "Bhai, college ki canteen mein new item aaya hai, try karenge",
      "Weekend pe road trip plan karte hain, kya bolta hai?",
      "Assignments complete kar liya tune? Main abhi tak start nahi kiya",
      "Kal raat ki party mein tu kahan tha? Mast scene tha",
      "Bhai, new bike dekhi meri? Papa ne birthday pe di",
      "Oye, crush se baat hui teri? Progress kya hai?",
      "Exam ke liye padhai shuru ki ya nahi abhi tak?",
      "Yaar, Netflix pe new series dekhi? Ekdum mast hai",
      "Hostel mein new roommate aaya hai, ekdum weird hai",
      "Bhai, ghar se kuch khane ka laya? Mujhe bhook lagi hai",
      "Aaj shaam ko football khelne chalega ground pe?",
      "Professor ne assignment extend kar diya, party karte hain",
      "Bhai, tu bhi na! Kuch bhi karta rehta hai",
      "Oye, phone ka new model liya tune? Specs kya hain?",
      "Internship ke liye apply kiya? Mujhe referral chahiye",
      "Bhai, breakup ho gaya kya? Mood off lag raha hai tera",
      "Weekend pe club chalenge? New DJ aaya hai"
    ],
    'priya': [
      "Assignment complete kar liya?",
      "Library mein milte hain kal",
      "Notes share kar dungi don't worry",
      "Professor ne extra reading assign ki hai",
      "Group project ke liye kab milna hai?",
      "Class notes miss kiye tune? Main share kar dungi",
      "Arjun, presentation ke liye research kiya tune?",
      "Library mein new books aayi hain, check karna chahiye",
      "Mid-semester exams ke liye prepare kar raha hai?",
      "Professor ne surprise quiz ka hint diya tha, yaad hai?",
      "Assignment submission deadline extend ho gayi hai",
      "Group study session join karega library mein?",
      "Reference books mile library mein? Main check kar lungi",
      "Project ke liye data collection start kiya?",
      "Class representative election ke liye vote dega kisko?",
      "Internship application bhej di tune? Deadline kal hai",
      "College fest ke liye volunteer kar raha hai?",
      "Seminar attend kiya kal wala? Notes share kar sakta hai?",
      "Research paper submission ke liye extension mil gayi",
      "Practical exam ke liye partner chahiye? Main free hoon",
      "Thesis topic decide kiya tune? Mujhe ideas chahiye",
      "Department ki new policy dekhi? Attendance rules change hue hain",
      "Scholarship ke liye apply kiya? Forms available hain office mein"
    ],
    'amit': [
      "Gym chalega aaj?",
      "Protein shake try kiya new wala?",
      "Match dekhne aaja mere ghar pe",
      "Fitness important hai bhai",
      "Weekend pe kya plan hai?",
      "Cricket practice ke liye ground book kiya maine",
      "New gym equipment aaya hai, try karenge kal",
      "Protein diet follow kar raha hai? Results dikh rahe hain",
      "Match haar gaye yaar, but next time pakka jeetenge",
      "Sports day ke liye register kiya tune?",
      "Fitness tracker liya naya? Steps count kar raha hai?",
      "Cardio routine change kiya maine, fat burn ho raha hai",
      "Cricket tournament ke liye team join karega?",
      "Weekend pe trekking chalega? Fitness bhi ho jayegi",
      "Diet plan follow kar raha hai? Weight loss ho raha hai?",
      "Sports scholarship ke liye apply kiya tune?",
      "Gym membership renew karni hai, saath chalega?",
      "Match tickets book kar liye maine, chalega dekhne?",
      "Fitness challenge join karega? 30 days ka hai",
      "Sports quota se admission mil sakta hai, try karega?",
      "Workout routine share kar sakta hoon, muscle gain ke liye best hai",
      "Cricket bat ka new model aaya hai, dekhne chalega?",
      "Fitness YouTuber ka new video dekha? Mast exercises bataye hain"
    ],
    'neha': [
      "Literature assignment mein help chahiye?",
      "Coffee pe milte hain college ke baad",
      "Book return kardi library mein?",
      "Class notes check kiye?",
      "Project presentation ready hai?",
      "Poetry competition ke liye register kiya tune?",
      "Literature club ki meeting hai kal, aayega?",
      "Book fair ja rahe hain weekend pe, join karega?",
      "Assignment mein references add kiye tune?",
      "Library se new novel liya maine, must-read hai",
      "Class presentation ke liye slides ready hain?",
      "Study material organize kar liya exams ke liye?",
      "Research paper ka format check kiya? Citations sahi hain?",
      "Coffee shop mein new discount offer hai students ke liye",
      "Literature festival ke tickets book kar liye maine",
      "Notes organized hain tere? Exam syllabus bahut bada hai",
      "Book club join karega? Weekly meetings hoti hain",
      "Assignment submission portal pe issue hai, check kiya?",
      "Study schedule bana liya tune? Time management important hai",
      "Reference books available hain mere paas, chahiye to bata",
      "Poetry workshop attend kiya kal wala? Insights share kar",
      "Thesis topic pe research progress kaisi hai?",
      "Library hours extend ho gaye hain exam season ke liye"
    ]
  };

  // Topic-specific responses based on detected context
  const topicResponses = {
    'college': [
      "College life mein balance rakhna important hai, studies aur fun dono.",
      "Assignments deadline se pehle submit karna best hota hai, last minute stress nahi hota.",
      "Group study sessions bahut helpful hote hain, especially exams ke time.",
      "Professor se doubts clear karte rehna chahiye, baad mein mushkil hota hai.",
      "College ke extra-curricular activities join karna career ke liye beneficial hota hai.",
      "Internships apply karna start karo, experience bahut matter karta hai.",
      "Class attendance maintain karna important hai, marks mein add hota hai.",
      "Notes organized rakhna exam time pe bahut help karta hai.",
      "College library ka full use karo, bahut resources available hote hain.",
      "Seniors se guidance lena accha hota hai, experience share karte hain.",
      "College festivals mein participate karna personality development ke liye accha hai.",
      "Time management skills develop karo, future mein bahut kaam aayenge.",
      "Different study techniques try karo, har kisi ke liye alag work karti hain.",
      "College ke career counseling services ka use karo, guidance milti hai.",
      "Networking important hai college mein, connections future mein help karte hain.",
      "Balance between academics and social life important hai mental health ke liye.",
      "Regular breaks lena important hai study time mein, productivity badhti hai.",
      "College projects seriously lo, practical knowledge milti hai.",
      "Resume building start karo early, placements ke time help hogi.",
      "College ke workshops attend karo, new skills develop hoti hain."
    ],
    'cricket': [
      "Cricket match dekhne ka maza hi kuch aur hai, especially friends ke saath.",
      "India ki batting lineup world class hai, especially Kohli and Rohit.",
      "IPL mein kaun si team support karte ho? Main Mumbai Indians ka fan hoon.",
      "Weekend pe match dekhne ka plan banate hain, snacks main arrange karunga.",
      "Test cricket ka maza hi alag hai, real skills dikhti hain usme.",
      "T20 format ne cricket ko completely change kar diya hai.",
      "World Cup matches ka excitement level hi different hota hai.",
      "Cricket statistics analyze karna interesting hota hai, patterns dikhte hain.",
      "Fantasy cricket khelte ho? Strategy bahut important hai usme.",
      "Local cricket tournaments mein participate karna chahiye, skills improve hoti hain.",
      "Cricket equipment quality matter karti hai performance mein.",
      "Batting technique pe work karna important hai consistent performance ke liye.",
      "Bowling variations develop karna important hai modern cricket mein.",
      "Fielding pe focus karna chahiye, matches win karati hai acchi fielding.",
      "Cricket history study karna interesting hai, game evolution samajh aati hai.",
      "Different pitches pe khelna important hai skills develop karne ke liye.",
      "Mental strength cricket mein bahut matter karti hai, pressure handle karna seekho.",
      "Cricket coaching lena beneficial hota hai technique improve karne ke liye.",
      "Match analysis karna seekho, weaknesses aur strengths identify ho jati hain.",
      "Cricket fitness specific hoti hai, targeted exercises important hain."
    ],
    'movies': [
      "Latest movies mein se kaunsi dekhi tumne? Recommendations chahiye?",
      "OTT platforms ne cinema experience ko completely change kar diya hai.",
      "Weekend pe movie night rakhte hain, friends ko bhi bulayenge.",
      "Old classic films bhi kabhi kabhi dekhni chahiye, golden era ki filmein.",
      "Indie films mainstream se hatke hoti hain, unique storytelling hoti hai.",
      "Film festivals attend karna chahiye, diverse cinema exposure milta hai.",
      "Director ka style study karna interesting hota hai, patterns dikhte hain.",
      "Movie reviews trust karne se pehle multiple sources check karo.",
      "Home theater setup invest karna worth hai agar regular movies dekhte ho.",
      "Foreign language films subtitles ke saath dekhni chahiye, perspective milta hai.",
      "Film analysis videos dekhna start karo, cinema appreciation badhti hai.",
      "Movie marathons plan karo genre ya director based, fun experience hota hai.",
      "Behind the scenes content dekhna interesting hota hai filmmaking samajhne ke liye.",
      "Short films bhi quality content provide karti hain, time commitment kam hai.",
      "Movie soundtracks separately sunna start karo, appreciation badhti hai.",
      "Film theory books padho agar seriously interested ho cinema mein.",
      "Documentary films informative aur entertaining dono ho sakti hain.",
      "Animation films underrated hain, technical excellence hoti hai unme.",
      "Movie discussions join karo online, different perspectives milte hain.",
      "Film history study karna evolution of cinema samajhne mein help karta hai."
    ],
    'substance': [
      "Health is wealth, Arjun. Take care of yourself.",
      "Addiction se bahar nikalna difficult hai but possible hai with support.",
      "One day at a time approach best rehta hai recovery mein.",
      "Professional help lena important hai, akele struggle mat karo.",
      "Triggers identify karna first step hai management mein.",
      "Alternative coping mechanisms develop karna important hai.",
      "Support groups join karna beneficial hota hai shared experiences ke liye.",
      "Family support recovery mein crucial role play karta hai.",
      "Relapse part of recovery process ho sakta hai, don't be discouraged.",
      "Healthy routines establish karna helps in recovery.",
      "Physical exercise endorphins release karta hai, mood improve hota hai.",
      "Meditation aur mindfulness addiction cravings manage karne mein help karte hain.",
      "Proper sleep recovery ke liye essential hai, schedule maintain karo.",
      "Nutrition pe dhyan dena important hai, body healing mein help karta hai.",
      "Journaling emotions process karne mein help karta hai.",
      "Hobbies develop karo, distraction aur fulfillment dono milti hai.",
      "Stress management techniques seekhna important hai triggers avoid karne ke liye.",
      "Positive social connections maintain karo, isolation avoid karo.",
      "Small victories celebrate karo, motivation maintain hoti hai.",
      "Long-term perspective rakho, recovery gradual process hai."
    ],
    'health': [
      "Regular health checkups karate raho, prevention better than cure hai.",
      "Balanced diet follow karna overall health ke liye important hai.",
      "Exercise routine maintain karo, consistency key hai results ke liye.",
      "Mental health physical health jitni hi important hai, neglect mat karo.",
      "Hydration maintain karo, body functions ke liye essential hai.",
      "Sleep schedule regular rakho, recovery aur brain function ke liye zaroori hai.",
      "Stress management techniques practice karo, long-term health ke liye important hai.",
      "Meditation daily routine mein add karo, mental clarity improve hoti hai.",
      "Posture pe dhyan do, back problems avoid karne ke liye.",
      "Screen time limit karo, eye strain aur sleep disturbance avoid karne ke liye.",
      "Regular breaks lo work ke beech mein, productivity aur health dono maintain hoti hai.",
      "Healthy snacking options choose karo, energy levels maintain rehte hain.",
      "Social connections maintain karo, mental wellbeing ke liye important hain.",
      "Nature mein time spend karo, stress levels kam hote hain.",
      "Hobbies pursue karo, mental stimulation milti hai.",
      "Health information reliable sources se hi lo, misinformation avoid karo.",
      "Small healthy habits develop karo, long-term impact hota hai.",
      "Self-care routine establish karo, burnout avoid karne ke liye.",
      "Health goals realistic rakho, sustainable changes focus karo.",
      "Regular health monitoring karo, early detection important hai issues ki."
    ],
    'food': [
      "Home-cooked food healthiest option hota hai, jyada cook karna try karo.",
      "Meal prep weekend pe kar lo, weekdays easy ho jate hain.",
      "Balanced diet follow karo, all food groups include karo.",
      "Local aur seasonal produce fresher aur nutritious hota hai.",
      "New recipes try karna cooking skills improve karta hai.",
      "Food journal maintain karo, eating patterns track ho jate hain.",
      "Mindful eating practice karo, overeating avoid hota hai.",
      "Hydration maintain karo meals ke saath, digestion better hota hai.",
      "Portion control important hai weight management ke liye.",
      "Healthy snacking options always available rakho ghar pe.",
      "Food labels read karna seekho, informed choices kar paoge.",
      "Cooking classes join karo, new techniques seekhne ko milti hain.",
      "Different cuisines explore karo, food appreciation badhti hai.",
      "Meal planning saves time aur money dono.",
      "Food waste reduce karne ke liye leftovers creatively use karo.",
      "Dining out healthy options choose karna seekho.",
      "Comfort food occasionally enjoy karo, balance important hai.",
      "Cooking as a social activity try karo, bonding hoti hai.",
      "Nutrition basics seekho, better food choices kar paoge.",
      "Food photography try karo, appreciation badhti hai presentation ki."
    ]
  };

  let response;

  // If we have a detected topic, use topic-specific responses
  if (context.topic && topicResponses[context.topic]) {
    response = getRandomItem(topicResponses[context.topic]);
  } else {
    // Otherwise use contact-specific responses
    const responses = contactResponses[contactId] || contactResponses['rahul'];
    response = getRandomItem(responses);
  }

  // Add Hinglish flair
  response = addHinglishFlair(response);

  // Add follow-up question sometimes
  if (Math.random() > 0.7 && !response.includes('?')) {
    response += ` ${getRandomItem(hinglishPhrases.questions)}`;
  }

  return response;
}

/**
 * Add Hinglish flair to a response
 */
function addHinglishFlair(response) {
  // 30% chance to add a Hindi ending
  if (Math.random() < 0.3) {
    response = `${response}, ${getRandomItem(hindiEndings)}`;
  }

  // 20% chance to add an English word commonly used in Hinglish
  if (Math.random() < 0.2) {
    response = `${getRandomItem(englishInHinglish)}, ${response}`;
  }

  // 25% chance to add a filler
  if (Math.random() < 0.25) {
    response = `${getRandomItem(hinglishPhrases.fillers)}, ${response}`;
  }

  return response;
}

/**
 * Get a random item from an array
 */
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Export the generator function
if (typeof module !== 'undefined') {
  module.exports = { generateHinglishResponse };
} else {
  // For browser use
  window.generateHinglishResponse = generateHinglishResponse;
}
