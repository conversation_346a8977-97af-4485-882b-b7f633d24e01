// Weather page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize weather page
  initWeatherPage();
});

function initWeatherPage() {
  // Load cities list first
  loadCitiesList().then(() => {
    // Initialize city selection after cities are loaded
    initCitySelection();

    // Initialize temperature unit toggle
    initTemperatureUnitToggle();

    // Load default city (Nagpur)
    loadWeatherData('Nagpur');
  });
}

// Function to load cities from cities-data.js
function loadCitiesList() {
  return new Promise((resolve) => {
    const citiesContainer = document.getElementById('cities-container');

    try {
      // Use the getCitiesHTML function from cities-data.js
      if (typeof getCitiesHTML === 'function') {
        citiesContainer.innerHTML = getCitiesHTML();
        console.log('Cities loaded successfully from cities-data.js');

        // Update status bar
        updateStatusBar('Cities list loaded successfully', 'Connected');
      } else {
        throw new Error('getCitiesHTML function not found');
      }
    } catch (error) {
      console.error('Error loading cities:', error);
      handleCitiesLoadError(citiesContainer, error.message);
    }

    // Always resolve since we have a fallback
    resolve();
  });
}

// Helper function to handle cities load error
function handleCitiesLoadError(container, errorMessage) {
  console.error('Error loading cities:', errorMessage);

  // As a fallback, let's include a minimal set of major cities directly
  container.innerHTML = `
    <div class="error-message">
      <p>Failed to load complete cities list. Showing limited cities.</p>
    </div>
    <div class="city-item">Delhi</div>
    <div class="city-item">Mumbai</div>
    <div class="city-item active">Nagpur</div>
    <div class="city-item">Bangalore</div>
    <div class="city-item">Chennai</div>
    <div class="city-item">Kolkata</div>
    <div class="city-item">Hyderabad</div>
  `;

  // Update status bar
  updateStatusBar('Error loading cities list - showing limited cities', 'Warning');
}

// City Selection Functions
function initCitySelection() {
  const cityItems = document.querySelectorAll('.city-item');

  cityItems.forEach(item => {
    item.addEventListener('click', function() {
      // Remove active class from all city items
      cityItems.forEach(city => city.classList.remove('active'));

      // Add active class to clicked city item
      item.classList.add('active');

      // Load weather data for the selected city
      const cityName = item.textContent.trim();
      loadWeatherData(cityName);
    });
  });
}

// Temperature Unit Toggle Functions
function initTemperatureUnitToggle() {
  const unitToggle = document.querySelector('.unit-toggle');

  if (unitToggle) {
    unitToggle.addEventListener('click', function() {
      toggleTemperatureUnit();
    });
  }
}

function toggleTemperatureUnit() {
  const tempValue = document.querySelector('.temp-value');
  const unitToggle = document.querySelector('.unit-toggle');

  if (tempValue && unitToggle) {
    // Get current temperature and unit
    const tempText = tempValue.textContent;
    const isCelsius = tempText.includes('°C');

    if (isCelsius) {
      // Convert to Fahrenheit
      const celsiusValue = parseFloat(tempText);
      const fahrenheitValue = (celsiusValue * 9/5) + 32;
      tempValue.textContent = `${Math.round(fahrenheitValue)}°F`;
      unitToggle.textContent = 'Switch to Celsius';
    } else {
      // Convert to Celsius
      const fahrenheitValue = parseFloat(tempText);
      const celsiusValue = (fahrenheitValue - 32) * 5/9;
      tempValue.textContent = `${Math.round(celsiusValue)}°C`;
      unitToggle.textContent = 'Switch to Fahrenheit';
    }
  }
}

// Weather Data Functions
function loadWeatherData(city) {
  // Update status bar
  updateStatusBar('Loading weather data for ' + city, 'Connected');

  // Show loading state
  const weatherDisplay = document.querySelector('.weather-display');
  if (weatherDisplay) {
    weatherDisplay.innerHTML = `
      <div class="weather-loading">
        <div class="loading-spinner">⟳</div>
        <p>Loading weather data for ${city}...</p>
      </div>
    `;
  }

  // Simulate API call delay
  setTimeout(() => {
    // Generate mock weather data
    const weatherData = getMockWeatherData(city);

    // Display weather data
    displayWeatherData(weatherData);

    // Update status bar
    updateStatusBar('Weather data loaded for ' + city, 'Connected');
  }, 500);
}

function getMockWeatherData(city) {
  // Use a date from 2010 instead of current date
  const date = new Date(2010, 5, 15); // June 15, 2010
  const formattedDate = date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Generate random temperature between 20-35°C for Indian cities
  const temperature = Math.floor(Math.random() * 15) + 20;

  // Weather conditions based on temperature
  let condition, icon;
  if (temperature < 25) {
    condition = 'Partly Cloudy';
    icon = '⛅';
  } else if (temperature < 30) {
    condition = 'Mostly Sunny';
    icon = '🌤️';
  } else {
    condition = 'Sunny';
    icon = '☀️';
  }

  // Generate random humidity between 40-80%
  const humidity = Math.floor(Math.random() * 40) + 40;

  // Generate random wind speed between 5-20 km/h
  const windSpeed = Math.floor(Math.random() * 15) + 5;

  // Generate random pressure between 1000-1020 hPa
  const pressure = Math.floor(Math.random() * 20) + 1000;

  // Generate random visibility between 5-15 km
  const visibility = Math.floor(Math.random() * 10) + 5;

  // City descriptions
  const cityDescriptions = {
    'Mumbai': 'Mumbai, formerly Bombay, is India\'s financial center and most populous city. Located on the west coast, it\'s known for Bollywood, colonial architecture, and the iconic Gateway of India.',
    'Delhi': 'Delhi, India\'s capital territory, is a massive metropolitan area. The city is known for its historical sites, diverse culture, and being the political hub of the country.',
    'Bangalore': 'Bangalore (Bengaluru) is known as India\'s Silicon Valley, home to numerous IT companies and startups. It enjoys a pleasant climate and is famous for its gardens and vibrant nightlife.',
    'Chennai': 'Chennai, on the Bay of Bengal, is Tamil Nadu\'s capital and a major cultural and economic center in South India, known for its temples, beaches, and classical music and dance.',
    'Kolkata': 'Kolkata, formerly Calcutta, is West Bengal\'s capital and a city of art, literature, and cultural heritage. It was once the capital of British India and retains much of its colonial architecture.',
    'Hyderabad': 'Hyderabad is known for its rich history, famous Charminar monument, and delicious biryani. It\'s a major tech hub with a unique blend of Hindu and Islamic traditions.',
    'Pune': 'Pune is a sprawling city in Maharashtra, known for its educational institutions, manufacturing industries, and historical sites from the Maratha Empire.',
    'Ahmedabad': 'Ahmedabad, Gujarat\'s largest city, is known for its textile industry, Gandhi Ashram, and distinctive Indo-Islamic architecture. It\'s a rapidly growing commercial center.',
    'Jaipur': 'Jaipur, Rajasthan\'s capital, is known as the "Pink City" for its distinctive terracotta-colored buildings. It\'s famous for its majestic palaces, forts, and vibrant culture.',
    'Lucknow': 'Lucknow, the capital of Uttar Pradesh, is known for its Nawabi-era architectural marvels, exquisite cuisine, and the refined Ganga-Jamuni culture blending Hindu and Muslim traditions.',
    'Patna': 'Patna, Bihar\'s capital, is one of the oldest continuously inhabited places in the world. Located on the Ganges, it has significant historical and religious importance.',
    'Bhopal': 'Bhopal, Madhya Pradesh\'s capital, is known as the "City of Lakes" with its beautiful water bodies. It blends ancient heritage with modern development.',
    'Raipur': 'Raipur, Chhattisgarh\'s capital, is a major commercial center for steel and aluminum industries. It\'s developing rapidly while preserving its tribal cultural heritage.',
    'Bhubaneswar': 'Bhubaneswar, Odisha\'s capital, is known as the "Temple City of India" with over 700 temples. It combines ancient architecture with planned modern development.',
    'Chandigarh': 'Chandigarh, designed by Le Corbusier, is one of India\'s best-planned cities. It serves as the capital of both Punjab and Haryana states and is known for its modern architecture.',
    'Dehradun': 'Dehradun, Uttarakhand\'s capital, is nestled in the Doon Valley. Known for its pleasant climate, prestigious educational institutions, and as a gateway to popular hill stations.',
    'Gandhinagar': 'Gandhinagar, Gujarat\'s capital, is a planned city near Ahmedabad. It\'s known for its government buildings, green spaces, and cultural institutions.',
    'Shimla': 'Shimla, Himachal Pradesh\'s capital, was the summer capital during British rule. This hill station is famous for its colonial architecture, scenic beauty, and pleasant climate.',
    'Ranchi': 'Ranchi, Jharkhand\'s capital, is known as the "City of Waterfalls" surrounded by forests and hills. It has a significant tribal population and natural beauty.',
    'Thiruvananthapuram': 'Thiruvananthapuram (Trivandrum), Kerala\'s capital, is known for its British colonial architecture, beaches, and the famous Padmanabhaswamy Temple.',
    'Imphal': 'Imphal, Manipur\'s capital, is known for its unique culture, Loktak Lake (the only floating lake in the world), and as the birthplace of modern polo.',
    'Shillong': 'Shillong, Meghalaya\'s capital, is known as the "Scotland of the East" for its rolling hills, waterfalls, and pleasant climate. It has a vibrant music culture.',
    'Aizawl': 'Aizawl, Mizoram\'s capital, is built on steep hills with stunning views. It\'s known for its clean environment, vibrant Mizo culture, and Christian heritage.',
    'Kohima': 'Kohima, Nagaland\'s capital, is famous for its role in World War II\'s Battle of Kohima. It offers stunning views of the Eastern Himalayas and rich Naga tribal culture.',
    'Gangtok': 'Gangtok, Sikkim\'s capital, is a picturesque hill station with views of Kanchenjunga, the world\'s third-highest peak. It\'s known for its Buddhist monasteries and clean environment.',
    'Agartala': 'Agartala, Tripura\'s capital, sits near the Bangladesh border. It\'s known for its palaces, temples, and as a cultural center with Bengali and indigenous tribal influences.',
    'Itanagar': 'Itanagar, Arunachal Pradesh\'s capital, is nestled in the foothills of the Himalayas. It\'s known for its diverse tribal cultures, Buddhist monasteries, and natural beauty.',
    'Dispur': 'Dispur, a locality within Guwahati, is Assam\'s capital. The region is known for its tea gardens, wildlife sanctuaries, and as the gateway to Northeast India.',
    'Panaji': 'Panaji (Panjim), Goa\'s capital, sits on the banks of the Mandovi River. It\'s known for its Portuguese colonial architecture, beaches, and vibrant nightlife.',
    'Dharamshala': 'Dharamshala, in Himachal Pradesh, is the seat of the Tibetan government-in-exile and home to the Dalai Lama. It\'s known for its Tibetan culture, monasteries, and Himalayan views.',
    'Nagpur': 'Nagpur, Maharashtra\'s winter capital, is known as the "Orange City" for its citrus cultivation. It\'s located at the geographical center of India and is a major transport hub.',
    'Belgaum': 'Belgaum (Belagavi), Karnataka\'s winter capital, is known for its colonial-era buildings, forts, and as an important military installation with pleasant climate.',
    'Jammu': 'Jammu, the winter capital of Jammu & Kashmir, is known as the "City of Temples" with numerous ancient temples. It serves as a major transportation hub for the region.',
    'Srinagar': 'Srinagar, the summer capital of Jammu & Kashmir, is famous for its Dal Lake, houseboats, and Mughal gardens. It\'s surrounded by snow-capped mountains and offers stunning natural beauty.',
    'Port Blair': 'Port Blair, the capital of Andaman & Nicobar Islands, is known for the historic Cellular Jail, beautiful beaches, and as a gateway to explore the archipelago.',
    'Daman': 'Daman, part of the union territory of Dadra and Nagar Haveli and Daman and Diu, is known for its Portuguese colonial architecture, beaches, and duty-free alcohol.',
    'Kavaratti': 'Kavaratti, Lakshadweep\'s capital, is a beautiful coral island in the Arabian Sea. It\'s known for its pristine beaches, lagoons, and coral reefs ideal for snorkeling.',
    'New Delhi': 'New Delhi, India\'s national capital, is known for its wide boulevards, government buildings, diplomatic enclaves, and as the seat of all three branches of government.',
    'Puducherry': 'Puducherry (Pondicherry), a former French colony, is known for its French Quarter, beaches, and spiritual communities including Auroville. It has a unique Franco-Tamil culture.',
    'Leh': 'Leh, the joint capital of Ladakh, is a high-desert city surrounded by the Himalayas. It\'s known for its Buddhist monasteries, trekking routes, and stunning mountain landscapes.',
    'Kargil': 'Kargil, the joint capital of Ladakh, is known for the 1999 Kargil War. It\'s surrounded by the Himalayas and serves as a gateway to remote valleys and trekking routes.',
    'Bhandara': 'Bhandara, located in eastern Maharashtra, is known as the "District of Lakes" with over 137 water bodies. Famous for its brass crafts and rice production, it\'s surrounded by lush forests and wildlife sanctuaries.',
    'Muzaffarpur': 'Muzaffarpur, located in Bihar, is famous for its Shahi litchi fruit and is known as the "Litchi Kingdom". It\'s an important commercial and educational center with historical significance dating back to ancient times.',
    'Yavatmal': 'Yavatmal, situated in Maharashtra\'s Vidarbha region, is an agricultural hub known for cotton production. The city has historical significance with ancient temples and is surrounded by scenic hills and forests.',
    'Alibag': 'Alibag, a coastal town in Maharashtra\'s Konkan region, is known for its pristine beaches and historic Kolaba Fort. Often called "mini-Goa," it\'s a popular weekend getaway for Mumbai residents with its laid-back charm and seafood cuisine.',
    'Almora': 'Almora, a picturesque hill station in Uttarakhand, is known for its panoramic views of the Himalayas. This ancient town features terraced fields, colonial architecture, and the famous Kasar Devi Temple visited by spiritual seekers from around the world.',
    'Champawat': 'Champawat, a small town in Uttarakhand, was once the capital of the Chand Dynasty. Known for its ancient temples and the historic Baleshwar Temple complex, it offers stunning views of the Himalayas and is rich in folklore and traditions.',
    'Chikmagalur': 'Chikmagalur, nestled in Karnataka\'s Western Ghats, is known as the "Coffee Land of Karnataka" where coffee was first cultivated in India. With its misty hills, waterfalls, and spice plantations, it\'s a paradise for nature lovers.',
    'Hampi': 'Hampi, a UNESCO World Heritage Site in Karnataka, was once the capital of the Vijayanagara Empire. This ancient village is known for its stunning ruins, boulder-strewn landscape, and the magnificent Virupaksha Temple dating back to the 7th century.',
    'Lonar': 'Lonar, a small town in Maharashtra, is famous for the Lonar Crater Lake formed by a meteorite impact millions of years ago. This rare salt lake is surrounded by forests and ancient temples, making it a unique geological and archaeological site.',
    'Matheran': 'Matheran, Asia\'s only automobile-free hill station, is located in Maharashtra. Known for its red soil paths, viewpoints offering panoramic vistas, and colonial-era architecture, it provides a peaceful retreat with horse-drawn carriages as the main transport.',
    'Puri': 'Puri, a coastal town in Odisha, is one of the four sacred Char Dham pilgrimage sites for Hindus. Famous for the Jagannath Temple and its annual Rath Yatra festival, it also offers beautiful beaches and traditional Odissi dance performances.',
    'Ranikhet': 'Ranikhet, meaning "Queen\'s Meadow," is a serene hill station in Uttarakhand developed by the British as a retreat for their troops. Known for its pine forests, apple orchards, and the Kumaon Regimental Centre Museum, it offers spectacular Himalayan views.',
    'Tawang': 'Tawang, a remote town in Arunachal Pradesh near the Tibet border, is home to the 400-year-old Tawang Monastery, the largest Buddhist monastery in India. With its high-altitude lakes, waterfalls, and snow-capped mountains, it\'s a spiritual and natural paradise.',
    'Udvada': 'Udvada, a tiny coastal village in Gujarat, is the most sacred place for Zoroastrians in India. Home to the ancient Atash Behram fire temple where a sacred flame has been burning for over 1,200 years, it preserves the unique Parsi culture and cuisine.',
    'Varkala': 'Varkala, a coastal town in Kerala, is known for its dramatic red cliffs overlooking the Arabian Sea. Famous for its Papanasam Beach believed to wash away sins, it offers ayurvedic treatments, yoga retreats, and fresh seafood in a laid-back atmosphere.',
    'Ziro': 'Ziro, a picturesque valley in Arunachal Pradesh, is home to the Apatani tribe known for their unique agricultural techniques and facial tattoos. With its pine hills, rice fields, and bamboo groves, it hosts the famous Ziro Music Festival celebrating indigenous culture.',
    'Anantapur': 'Anantapur, located in the Rayalaseema region of Andhra Pradesh, is known for its extreme climate and historical significance. Home to the ancient Lepakshi temple with its hanging pillar and the Penukonda Fort, it\'s also a major agricultural center for groundnut cultivation.',
    'Badvel': 'Badvel, a town in Kadapa district of Andhra Pradesh, is known for its agricultural produce and the nearby Lankamalleswara Wildlife Sanctuary. The town serves as a commercial hub for surrounding villages with its weekly markets and traditional handloom industry.',
    'Chittoor': 'Chittoor, located near the borders of Tamil Nadu and Karnataka, is famous for its mangoes and religious temples. The district is home to the world-renowned Tirupati Balaji Temple and serves as a major pilgrimage center with its rich cultural heritage.',
    'Eluru': 'Eluru, formerly the capital of West Godavari district, is known for its centuries-old carpet industry and the Kolleru Lake bird sanctuary. The city has a rich cultural history dating back to the Buddhist period and is famous for its traditional handloom products.',
    'Guntur': 'Guntur, a major commercial center in Andhra Pradesh, is famous for its spicy chili peppers and tobacco production. The city is home to ancient Buddhist sites like Amaravathi and Undavalli Caves, and serves as an educational hub with numerous colleges and universities.',
    'Jaggaiahpet': 'Jaggaiahpet, a town in Krishna district, is known for its agricultural markets and limestone quarries. Located strategically between Vijayawada and Hyderabad, it serves as a transit point with historical significance dating back to the Qutb Shahi dynasty.',
    'Kadapa': 'Kadapa (formerly Cuddapah), known for its rich mineral deposits including barytes and limestone, is one of the oldest towns in Andhra Pradesh. The city is surrounded by the Palakonda and Nallamala Hills and is home to the historic Ameen Peer Dargah.',
    'Kakinada': 'Kakinada, a port city on the coast of Andhra Pradesh, is known for its deep-water port, beautiful beaches, and the famous Kakinada Khaja sweet. The city serves as a major industrial center with its fertilizer plants, natural gas fields, and rice production.',
    'Kavali': 'Kavali, a town in Nellore district, is known for its agricultural markets and the nearby Udayagiri Fort. The town has historical significance with ancient temples and serves as a commercial center for the surrounding rural areas with its textile industry.',
    'Kurnool': 'Kurnool, once the capital of Andhra State, is situated at the confluence of the Tungabhadra and Handri rivers. Known for the historic Kurnool Fort and Belum Caves (the second largest cave system in India), it has a rich heritage dating back to the Vijayanagara Empire.',
    'Narasaraopet': 'Narasaraopet, a major town in Guntur district, is known for its agricultural markets and educational institutions. The town serves as a commercial hub for surrounding villages and is famous for its traditional weaving industry and cultural festivals.',
    'Nellore': 'Nellore, known for its spicy cuisine and paddy fields, is famous for its Rottela Panduga (bread festival) and the Pennar River. The coastal district is a major producer of aquaculture products and is home to the ancient Sri Ranganathaswamy Temple.',
    'Nidadavole': 'Nidadavole, a town in West Godavari district, is situated on the banks of the Godavari River. Known for its lush paddy fields and sugar factories, the town has historical significance with ancient temples and is famous for its agricultural prosperity.',
    'Ongole': 'Ongole, known worldwide for its Ongole cattle breed, is a major agricultural and commercial center in Prakasam district. The city has historical significance with the nearby Chandravanka River and serves as an important transportation hub in the region.',
    'Proddatur': 'Proddatur, known as the "Gold City" for its gold market, is a major commercial center in Kadapa district. The town is famous for its cotton and gold trade, with the Penna River flowing nearby, and serves as an important agricultural market for the region.',
    'Puttur': 'Puttur, a town in Chittoor district near the Tamil Nadu border, is known for its dairy industry and religious temples. The town has cultural significance with its traditional arts and crafts, and serves as a commercial center for the surrounding villages.',
    'Rajahmundry': 'Rajahmundry, one of the oldest cities in India, is situated on the banks of the Godavari River. Known as the "Cultural Capital of Andhra Pradesh," it\'s famous for its ancient Godavari Bridge, paper industry, and as the birthplace of Telugu literature.',
    'Rayachoti': 'Rayachoti, a town in Kadapa district, is known for its historical temples and traditional architecture. Surrounded by hills, the town has cultural significance with ancient inscriptions and serves as an administrative center with its rich heritage.',
    'Srikakulam': 'Srikakulam, a coastal district in northern Andhra Pradesh, is known for its pristine beaches and the ancient Sri Mukhalingam Temple. The region has historical significance dating back to the Kalinga kingdom and is famous for its unique cultural traditions and cuisine.'
  };

  // Default description if city not found
  const defaultDescription = `${city} is one of India's vibrant urban centers, showcasing the country's rich cultural heritage and modern development.`;

  // Get city description or use default
  const cityDescription = cityDescriptions[city] || defaultDescription;

  // Generate 5-day forecast
  const forecast = [];
  for (let i = 1; i <= 5; i++) {
    const forecastDate = new Date(2010, 5, 15); // June 15, 2010
    forecastDate.setDate(date.getDate() + i);
    const forecastDay = forecastDate.toLocaleDateString('en-US', { weekday: 'short' });

    // Generate random temperature for forecast
    const forecastTemp = Math.floor(Math.random() * 15) + 20;

    // Weather conditions based on temperature
    let forecastCondition, forecastIcon;
    if (forecastTemp < 25) {
      forecastCondition = 'Partly Cloudy';
      forecastIcon = '⛅';
    } else if (forecastTemp < 30) {
      forecastCondition = 'Mostly Sunny';
      forecastIcon = '🌤️';
    } else {
      forecastCondition = 'Sunny';
      forecastIcon = '☀️';
    }

    forecast.push({
      day: forecastDay,
      temperature: forecastTemp,
      condition: forecastCondition,
      icon: forecastIcon
    });
  }

  return {
    city,
    date: formattedDate,
    temperature,
    condition,
    icon,
    humidity,
    windSpeed,
    pressure,
    visibility,
    forecast,
    description: cityDescription
  };
}

function displayWeatherData(data) {
  const weatherDisplay = document.querySelector('.weather-display');

  if (weatherDisplay) {
    weatherDisplay.innerHTML = `
      <div class="weather-card">
        <div class="weather-header">
          <div class="weather-icon">${data.icon}</div>
          <div class="weather-info">
            <h2>${data.city}</h2>
            <p class="date">${data.date}</p>
          </div>
        </div>

        <div class="weather-main">
          <div class="temperature">
            <span class="temp-value">${data.temperature}°C</span>
          </div>
          <div class="condition-display">
            <p class="condition">${data.condition}</p>
            <button class="unit-toggle">Switch to Fahrenheit</button>
          </div>
        </div>

        <div class="weather-details">
          <div class="weather-stats">
            <div class="stat">
              <span class="stat-label">Humidity</span>
              <span class="stat-value">${data.humidity}%</span>
            </div>
            <div class="stat">
              <span class="stat-label">Wind</span>
              <span class="stat-value">${data.windSpeed} km/h</span>
            </div>
            <div class="stat">
              <span class="stat-label">Visibility</span>
              <span class="stat-value">${data.visibility} km</span>
            </div>
            <div class="stat">
              <span class="stat-label">Pressure</span>
              <span class="stat-value">${data.pressure} hPa</span>
            </div>
          </div>

          <div class="forecast-section">
            <h3>4-Day Forecast</h3>
            <div class="forecast-container">
              ${data.forecast.slice(0, 4).map(day => `
                <div class="forecast-day">
                  <div class="forecast-date">${day.day}</div>
                  <div class="forecast-icon">${day.icon}</div>
                  <div class="forecast-temp">${day.temperature}°C</div>
                  <div class="forecast-condition">${day.condition}</div>
                </div>
              `).join('')}
            </div>
          </div>

          <div class="city-description">
            <h3>About ${data.city}</h3>
            <p>${data.description}</p>
          </div>
        </div>
      </div>
    `;

    // Reinitialize temperature unit toggle
    initTemperatureUnitToggle();
  }
}
