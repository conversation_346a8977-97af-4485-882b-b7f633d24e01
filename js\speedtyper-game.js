// Speed Typer Game functionality
let stTextToType = '';
let stUserInput = '';
let stTimer = 60;
let stTimerInterval;
let stStartTime;
let stWPM = 0;
let stAccuracy = 100;
let stGameRunning = false;
let stGameOver = false;
let stPlayerPosition = 0;
let stOpponentPosition = 0;
let stOpponentSpeed;
let stOpponentInterval;

// Text passages for typing
const stEasyTexts = [
  "The quick brown fox jumps over the lazy dog. This simple sentence contains every letter of the alphabet.",
  "Learning to type quickly and accurately is an essential skill in today's digital world.",
  "Practice makes perfect. The more you type, the faster and more accurate you will become.",
  "Typing is like playing a musical instrument. It requires rhythm, precision, and practice."
];

const stMediumTexts = [
  "The ability to type without looking at the keyboard is called touch typing. It's a skill that can significantly increase your productivity and efficiency when using a computer.",
  "Ergonomics is important when typing. Keep your wrists straight, your shoulders relaxed, and your feet flat on the floor to prevent strain and injury.",
  "The QWERTY keyboard layout was designed in the 1870s for mechanical typewriters. Despite not being the most efficient layout, it remains the standard due to widespread adoption.",
  "Typing speed is typically measured in words per minute (WPM). The average typing speed is around 40 WPM, while professional typists can reach speeds of 80 WPM or higher."
];

const stHardTexts = [
  "The development of typing technology has evolved from mechanical typewriters to electric typewriters, and finally to computer keyboards. Each innovation has changed the way we interact with text and information, ultimately leading to increased efficiency and productivity in various professional fields.",
  "Touch typing is a skill that involves typing without looking at the keyboard, using muscle memory to find the keys. This technique allows typists to achieve higher speeds and accuracy, as they can focus on the text being typed rather than searching for keys. Learning to touch type requires practice and patience, but the long-term benefits are substantial.",
  "The QWERTY keyboard layout, named after the first six letters in the top row, was designed by Christopher Latham Sholes in the 1870s for mechanical typewriters. Contrary to popular belief, it wasn't designed to slow typists down, but rather to prevent jamming by placing commonly used letter pairs far apart. Despite the development of more efficient layouts like Dvorak, QWERTY remains the standard due to its widespread adoption.",
  "Typing speed competitions have been held since the early days of typewriters. These contests measure not only speed but also accuracy, with penalties for errors. The current world record for typing speed is over 200 words per minute, achieved using a specialized stenotype keyboard that allows for typing multiple characters simultaneously."
];

// Initialize the Speed Typer game
function initSpeedTyperGame() {
  // Get DOM elements
  const typingInput = document.getElementById('typing-input');
  const restartButton = document.getElementById('restart-speedtyper-game');
  
  // Add event listeners
  if (typingInput) {
    typingInput.addEventListener('input', handleTyping);
  }
  
  if (restartButton) {
    restartButton.addEventListener('click', restartSpeedTyperGame);
  }
  
  // Reset game state
  resetSpeedTyperGame();
}

// Start the Speed Typer game
function startSpeedTyperGame() {
  if (!stGameRunning && !stGameOver) {
    stGameRunning = true;
    
    // Set difficulty based on random selection
    const difficulty = Math.floor(Math.random() * 3); // 0: easy, 1: medium, 2: hard
    
    // Get text based on difficulty
    let textList;
    switch (difficulty) {
      case 0:
        textList = stEasyTexts;
        stOpponentSpeed = 30; // WPM for opponent
        break;
      case 1:
        textList = stMediumTexts;
        stOpponentSpeed = 45; // WPM for opponent
        break;
      case 2:
        textList = stHardTexts;
        stOpponentSpeed = 60; // WPM for opponent
        break;
    }
    
    // Get random text from list
    const randomIndex = Math.floor(Math.random() * textList.length);
    stTextToType = textList[randomIndex];
    
    // Update display
    document.getElementById('text-to-type').textContent = stTextToType;
    document.getElementById('user-typing').textContent = '';
    
    // Enable input
    const typingInput = document.getElementById('typing-input');
    if (typingInput) {
      typingInput.disabled = false;
      typingInput.value = '';
      typingInput.focus();
    }
    
    // Reset positions
    stPlayerPosition = 0;
    stOpponentPosition = 0;
    updateRacerPositions();
    
    // Start timer
    stStartTime = new Date();
    startSpeedTyperTimer();
    
    // Start opponent
    startOpponent();
  } else if (stGameOver) {
    restartSpeedTyperGame();
  }
}

// Pause the Speed Typer game
function pauseSpeedTyperGame() {
  stGameRunning = false;
  clearInterval(stTimerInterval);
  clearInterval(stOpponentInterval);
}

// Restart the Speed Typer game
function restartSpeedTyperGame() {
  pauseSpeedTyperGame();
  resetSpeedTyperGame();
  startSpeedTyperGame();
}

// Reset the game state
function resetSpeedTyperGame() {
  stTextToType = '';
  stUserInput = '';
  stTimer = 60;
  stWPM = 0;
  stAccuracy = 100;
  stGameRunning = false;
  stGameOver = false;
  stPlayerPosition = 0;
  stOpponentPosition = 0;
  
  // Update displays
  updateSpeedTyperWPMDisplay();
  updateSpeedTyperAccuracyDisplay();
  updateSpeedTyperTimerDisplay();
  updateRacerPositions();
  
  // Clear text display
  document.getElementById('text-to-type').textContent = '';
  document.getElementById('user-typing').textContent = '';
  
  // Clear input
  const typingInput = document.getElementById('typing-input');
  if (typingInput) {
    typingInput.value = '';
    typingInput.disabled = true;
  }
}

// Start the timer
function startSpeedTyperTimer() {
  clearInterval(stTimerInterval);
  
  stTimerInterval = setInterval(function() {
    stTimer--;
    updateSpeedTyperTimerDisplay();
    
    // Update WPM
    calculateWPM();
    
    if (stTimer <= 0) {
      handleSpeedTyperGameOver('Time\'s up!');
    }
  }, 1000);
}

// Start the opponent
function startOpponent() {
  clearInterval(stOpponentInterval);
  
  // Calculate how often to update opponent position
  // Based on opponent WPM and text length
  const totalChars = stTextToType.length;
  const charsPerMinute = stOpponentSpeed * 5; // Assuming 5 chars per word
  const charsPerSecond = charsPerMinute / 60;
  const updateInterval = 1000 / charsPerSecond; // How many ms between each character
  
  stOpponentInterval = setInterval(function() {
    stOpponentPosition += 1;
    
    // Calculate percentage of completion
    const opponentProgress = (stOpponentPosition / totalChars) * 100;
    
    // Update opponent position
    updateRacerPositions();
    
    // Check if opponent finished
    if (stOpponentPosition >= totalChars) {
      handleSpeedTyperGameOver('Opponent wins!');
    }
  }, updateInterval);
}

// Handle typing input
function handleTyping(event) {
  if (!stGameRunning) return;
  
  const typingInput = document.getElementById('typing-input');
  if (!typingInput) return;
  
  stUserInput = typingInput.value;
  
  // Update display
  document.getElementById('user-typing').textContent = stUserInput;
  
  // Calculate accuracy
  calculateAccuracy();
  
  // Update player position
  stPlayerPosition = stUserInput.length;
  updateRacerPositions();
  
  // Check if player finished
  if (stUserInput.length >= stTextToType.length) {
    // Check if input matches text
    if (stUserInput === stTextToType) {
      handleSpeedTyperGameOver('You win!');
    } else {
      // Clear input if it doesn't match
      typingInput.value = stUserInput.substring(0, stUserInput.length - 1);
    }
  }
}

// Calculate WPM (Words Per Minute)
function calculateWPM() {
  if (!stGameRunning || stUserInput.length === 0) return;
  
  const currentTime = new Date();
  const elapsedMinutes = (currentTime - stStartTime) / 60000; // Convert to minutes
  
  // Calculate WPM (assuming 5 characters per word)
  stWPM = Math.round((stUserInput.length / 5) / elapsedMinutes);
  
  updateSpeedTyperWPMDisplay();
}

// Calculate accuracy
function calculateAccuracy() {
  if (!stGameRunning || stUserInput.length === 0) return;
  
  let correctChars = 0;
  
  // Compare each character
  for (let i = 0; i < stUserInput.length; i++) {
    if (i < stTextToType.length && stUserInput[i] === stTextToType[i]) {
      correctChars++;
    }
  }
  
  // Calculate accuracy percentage
  stAccuracy = Math.round((correctChars / stUserInput.length) * 100);
  
  updateSpeedTyperAccuracyDisplay();
}

// Update racer positions
function updateRacerPositions() {
  const playerRacer = document.getElementById('player-racer');
  const opponentRacer = document.getElementById('opponent-racer');
  
  if (!playerRacer || !opponentRacer) return;
  
  const trackWidth = document.querySelector('.racer-track').offsetWidth - 50; // Subtract racer width
  
  // Calculate positions as percentage of text length
  const playerPercentage = (stPlayerPosition / stTextToType.length) * 100;
  const opponentPercentage = (stOpponentPosition / stTextToType.length) * 100;
  
  // Update positions
  playerRacer.style.left = `${Math.min(playerPercentage, 100) * trackWidth / 100 + 10}px`;
  opponentRacer.style.left = `${Math.min(opponentPercentage, 100) * trackWidth / 100 + 10}px`;
}

// Handle game over
function handleSpeedTyperGameOver(message) {
  stGameRunning = false;
  stGameOver = true;
  clearInterval(stTimerInterval);
  clearInterval(stOpponentInterval);
  
  // Disable input
  const typingInput = document.getElementById('typing-input');
  if (typingInput) {
    typingInput.disabled = true;
  }
  
  // Show game over message
  document.getElementById('text-to-type').textContent = message;
  document.getElementById('user-typing').textContent = `Final WPM: ${stWPM} | Accuracy: ${stAccuracy}%`;
  
  // Save high score
  saveSpeedTyperHighScore(stWPM);
}

// Update WPM display
function updateSpeedTyperWPMDisplay() {
  const wpmElement = document.getElementById('typing-wpm');
  if (wpmElement) {
    wpmElement.textContent = stWPM;
  }
}

// Update accuracy display
function updateSpeedTyperAccuracyDisplay() {
  const accuracyElement = document.getElementById('typing-accuracy');
  if (accuracyElement) {
    accuracyElement.textContent = stAccuracy;
  }
}

// Update timer display
function updateSpeedTyperTimerDisplay() {
  const timerElement = document.getElementById('speedtyper-timer');
  if (timerElement) {
    timerElement.textContent = stTimer;
  }
}

// Save high score
function saveSpeedTyperHighScore(wpm) {
  const currentHighScore = localStorage.getItem('speedtyper-high-score') || 0;
  if (wpm > currentHighScore) {
    localStorage.setItem('speedtyper-high-score', wpm);
    return true;
  }
  return false;
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the game
  initSpeedTyperGame();
});
