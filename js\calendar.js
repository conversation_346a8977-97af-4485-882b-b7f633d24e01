// Calendar page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize calendar page
  initCalendarPage();
});

function initCalendarPage() {
  // Initialize calendar
  initCalendar();

  // Initialize reminders
  initReminders();

  // Update status bar
  updateStatusBar('Calendar loaded successfully', 'Connected');
}

// Calendar Functions
function initCalendar() {
  // Set initial date to May 2010 (as per user preference for 2010 dates)
  const currentDate = new Date(2010, 4, 1); // May 1, 2010
  
  // Render the calendar
  renderCalendar(currentDate);
  
  // Initialize navigation buttons
  initCalendarNavigation(currentDate);
}

function renderCalendar(date) {
  // Update the current month display
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  const currentMonthDisplay = document.getElementById('current-month-display');
  currentMonthDisplay.textContent = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
  
  // Get the first day of the month
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  
  // Get the last day of the month
  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  
  // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
  const firstDayOfWeek = firstDay.getDay();
  
  // Get the total number of days in the month
  const totalDays = lastDay.getDate();
  
  // Get the calendar days container
  const calendarDays = document.getElementById('calendar-days');
  
  // Clear the calendar days
  calendarDays.innerHTML = '';
  
  // Get the previous month's last day
  const prevMonthLastDay = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
  
  // Add days from the previous month
  for (let i = 0; i < firstDayOfWeek; i++) {
    const dayElement = document.createElement('div');
    dayElement.className = 'calendar-day other-month';
    
    const dayNumber = document.createElement('div');
    dayNumber.className = 'day-number';
    dayNumber.textContent = prevMonthLastDay - firstDayOfWeek + i + 1;
    
    dayElement.appendChild(dayNumber);
    calendarDays.appendChild(dayElement);
  }
  
  // Add days for the current month
  for (let i = 1; i <= totalDays; i++) {
    const dayElement = document.createElement('div');
    dayElement.className = 'calendar-day';
    
    // Check if this day has events
    if (hasEvents(date.getFullYear(), date.getMonth(), i)) {
      dayElement.classList.add('has-events');
    }
    
    // Check if this is today
    const today = new Date();
    if (date.getFullYear() === today.getFullYear() && 
        date.getMonth() === today.getMonth() && 
        i === today.getDate()) {
      dayElement.classList.add('today');
    }
    
    const dayNumber = document.createElement('div');
    dayNumber.className = 'day-number';
    dayNumber.textContent = i;
    
    const dayEvents = document.createElement('div');
    dayEvents.className = 'day-events';
    
    // Add event indicators if there are events on this day
    const events = getEventsForDay(date.getFullYear(), date.getMonth(), i);
    if (events.length > 0) {
      dayEvents.textContent = events.length > 1 ? `${events.length} events` : '1 event';
    }
    
    dayElement.appendChild(dayNumber);
    dayElement.appendChild(dayEvents);
    
    // Add click event to show events for this day
    dayElement.addEventListener('click', function() {
      showEventsForDay(date.getFullYear(), date.getMonth(), i);
    });
    
    calendarDays.appendChild(dayElement);
  }
  
  // Calculate how many days from the next month we need to add
  const totalCells = 42; // 6 rows x 7 columns
  const remainingCells = totalCells - (firstDayOfWeek + totalDays);
  
  // Add days from the next month
  for (let i = 1; i <= remainingCells; i++) {
    const dayElement = document.createElement('div');
    dayElement.className = 'calendar-day other-month';
    
    const dayNumber = document.createElement('div');
    dayNumber.className = 'day-number';
    dayNumber.textContent = i;
    
    dayElement.appendChild(dayNumber);
    calendarDays.appendChild(dayElement);
  }
}

function initCalendarNavigation(initialDate) {
  const prevMonthButton = document.getElementById('prev-month');
  const nextMonthButton = document.getElementById('next-month');
  
  // Store the current date in a variable that can be accessed by the event handlers
  let currentDate = new Date(initialDate);
  
  prevMonthButton.addEventListener('click', function() {
    // Go to the previous month
    currentDate.setMonth(currentDate.getMonth() - 1);
    renderCalendar(currentDate);
    
    // Update status bar
    updateStatusBar(`Viewing ${getMonthName(currentDate.getMonth())} ${currentDate.getFullYear()}`, 'Connected');
  });
  
  nextMonthButton.addEventListener('click', function() {
    // Go to the next month
    currentDate.setMonth(currentDate.getMonth() + 1);
    renderCalendar(currentDate);
    
    // Update status bar
    updateStatusBar(`Viewing ${getMonthName(currentDate.getMonth())} ${currentDate.getFullYear()}`, 'Connected');
  });
}

function getMonthName(monthIndex) {
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  return monthNames[monthIndex];
}

// Reminders Functions
function initReminders() {
  // Initialize add reminder button
  const addReminderButton = document.getElementById('add-reminder');
  
  if (addReminderButton) {
    addReminderButton.addEventListener('click', function() {
      showAddReminderDialog();
    });
  }
  
  // Initialize reminder items
  const reminderItems = document.querySelectorAll('.reminder-item');
  
  reminderItems.forEach(item => {
    item.addEventListener('click', function() {
      // Get the reminder details
      const date = item.querySelector('.reminder-date').textContent;
      const title = item.querySelector('.reminder-title').textContent;
      const time = item.querySelector('.reminder-time').textContent;
      
      // Show the reminder details
      showReminderDetails(date, title, time);
    });
  });
}

function showAddReminderDialog() {
  // In a real application, this would show a dialog to add a new reminder
  // For demo purposes, just update the status bar
  updateStatusBar('Add Reminder dialog would appear here', 'Connected');
  
  // Simulate adding a new reminder
  const remindersList = document.getElementById('reminders-list');
  
  if (remindersList) {
    const newReminder = document.createElement('div');
    newReminder.className = 'reminder-item';
    
    newReminder.innerHTML = `
      <div class="reminder-date">June 5, 2010</div>
      <div class="reminder-title">New Reminder</div>
      <div class="reminder-time">12:00 PM</div>
    `;
    
    // Add click event to the new reminder
    newReminder.addEventListener('click', function() {
      showReminderDetails('June 5, 2010', 'New Reminder', '12:00 PM');
    });
    
    // Add the new reminder to the list
    remindersList.prepend(newReminder);
    
    // Update status bar
    updateStatusBar('New reminder added', 'Connected');
  }
}

function showReminderDetails(date, title, time) {
  // In a real application, this would show a dialog with the reminder details
  // For demo purposes, just update the status bar
  updateStatusBar(`Reminder: ${title} on ${date} at ${time}`, 'Connected');
}

// Helper Functions
function hasEvents(year, month, day) {
  // Check if there are events for this day
  // For demo purposes, just return true for some specific days
  const eventDays = [5, 10, 15, 18, 20, 25, 28];
  return eventDays.includes(day);
}

function getEventsForDay(year, month, day) {
  // Get events for this day
  // For demo purposes, just return some dummy events
  const events = [];
  
  if (day === 15) {
    events.push({ title: 'Meeting with Project Team', time: '10:00 AM' });
  }
  
  if (day === 18) {
    events.push({ title: 'Quarterly Exam Preparation', time: 'All Day' });
  }
  
  if (day === 20) {
    events.push({ title: 'Doctor\'s Appointment', time: '3:30 PM' });
  }
  
  if (day === 25) {
    events.push({ title: 'Final Exam - Mathematics', time: '9:00 AM' });
  }
  
  if (day === 28) {
    events.push({ title: 'Birthday Party - Rahul', time: '6:00 PM' });
  }
  
  return events;
}

function showEventsForDay(year, month, day) {
  // Get events for this day
  const events = getEventsForDay(year, month, day);
  
  // Format the date
  const date = new Date(year, month, day);
  const formattedDate = `${getMonthName(month)} ${day}, ${year}`;
  
  // Update status bar
  if (events.length > 0) {
    updateStatusBar(`${events.length} event(s) on ${formattedDate}`, 'Connected');
  } else {
    updateStatusBar(`No events on ${formattedDate}`, 'Connected');
  }
}
