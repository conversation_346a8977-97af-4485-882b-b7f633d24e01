/* Chat Page Styles */
.chat-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F1EFE2;
}

.chat-header-bar {
  background: linear-gradient(to bottom, #0A246A, #3A6EA5);
  color: white;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-header-bar h1 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.chat-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Contacts Sidebar Styles */
.contacts-sidebar {
  width: 280px;
  background-color: #ECE9D8;
  border-right: 1px solid #ACA899;
  display: flex;
  flex-direction: column;
}

.contacts-header {
  padding: 15px;
  border-bottom: 1px solid #ACA899;
}

.user-profile {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
  border: 1px solid #ACA899;
  background-color: white;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: bold;
  font-size: 14px;
}

.user-status {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-indicator.online {
  background-color: #3ADF00;
}

.status-indicator.away {
  background-color: #FFA500;
}

.status-indicator.offline {
  background-color: #D3D3D3;
}

.search-contacts input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  background-color: white;
  font-size: 12px;
}

.contacts-list {
  flex: 1;
  overflow-y: auto;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #E5E3D6;
  transition: background-color 0.2s;
}

.contact-item:hover {
  background-color: #E3EFFF;
}

.contact-item.active {
  background-color: #CCE8FF;
}

.contact-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
  position: relative;
  border: 1px solid #ACA899;
  background-color: white;
}

.contact-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  position: absolute;
  bottom: 0;
  right: 0;
  border: 1px solid white;
}

.status-dot.online {
  background-color: #3ADF00;
}

.status-dot.away {
  background-color: #FFA500;
}

.status-dot.offline {
  background-color: #D3D3D3;
}

.status-dot.typing {
  background-color: #00AAFF;
  animation: typing-pulse 1s infinite;
}

@keyframes typing-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.contact-info {
  flex: 1;
  overflow: hidden;
}

.contact-name {
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 3px;
}

.contact-last-message {
  font-size: 13px; /* Increased from 12px */
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-time {
  font-size: 11px;
  color: #888;
  margin-left: 5px;
}

/* Chat Window Styles */
.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #F1EFE2;
}

.chat-window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  border-bottom: 1px solid #ACA899;
  background-color: #ECE9D8;
}

.chat-contact-info {
  display: flex;
  align-items: center;
}

.chat-contact-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
  position: relative;
  border: 1px solid #ACA899;
  background-color: white;
}

.chat-contact-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-contact-details {
  display: flex;
  flex-direction: column;
}

.chat-contact-name {
  font-weight: bold;
  font-size: 14px;
}

.chat-contact-status {
  font-size: 12px;
  color: #666;
}

.chat-actions {
  display: flex;
}

.chat-action-button {
  background: none;
  border: none;
  font-size: 16px;
  margin-left: 10px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.chat-action-button:hover {
  background-color: #E3EFFF;
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #F1EFE2;
  display: flex;
  flex-direction: column;
}

.message {
  max-width: 70%;
  margin-bottom: 15px;
  padding: 10px 15px;
  border-radius: 10px;
  position: relative;
  word-wrap: break-word;
  font-size: 14px; /* Increased font size from default */
}

.message.sent {
  align-self: flex-end;
  background-color: #D9F2FF;
  border: 1px solid #AED0EA;
  margin-left: auto;
}

.message.received {
  align-self: flex-start;
  background-color: white;
  border: 1px solid #D4D0C8;
  margin-right: auto;
}

.message.system {
  align-self: center;
  background-color: #F0F0F0;
  border: 1px solid #D4D0C8;
  padding: 5px 15px;
  font-size: 14px; /* Increased from 12px */
  color: #666;
  font-style: italic;
  max-width: 90%;
}

.message-text {
  font-size: 14px; /* Adjusted from 16px to be less large */
  line-height: 1.4; /* Better line spacing for readability */
}

.message-time {
  font-size: 10px;
  color: #888;
  text-align: right;
  margin-top: 5px;
}

.chat-input-area {
  padding: 15px;
  border-top: 1px solid #ACA899;
  background-color: #ECE9D8;
  position: relative;
}

.typing-indicator {
  position: absolute;
  top: -20px;
  left: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 3px 10px;
  border-radius: 10px;
  border: 1px solid #D4D0C8;
  display: none;
}

.typing-indicator.active {
  display: flex;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background-color: #888;
  border-radius: 50%;
  margin: 0 2px;
  animation: typing-animation 1.5s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-animation {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-5px);
  }
}

.chat-input-container {
  display: flex;
  align-items: center;
}

#chat-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ACA899;
  border-radius: 5px;
  font-size: 14px;
  background-color: white;
}

#send-button {
  margin-left: 10px;
  padding: 8px 15px;
  background: linear-gradient(to bottom, #EFF5FB, #E3EFFF);
  border: 1px solid #ACA899;
  border-radius: 3px;
  cursor: pointer;
  font-weight: bold;
  color: #0A246A;
}

#send-button:hover {
  background: linear-gradient(to bottom, #E3EFFF, #CCE8FF);
}

.chat-input-actions {
  display: flex;
  margin-top: 10px;
}

.input-action-button {
  background: none;
  border: none;
  font-size: 16px;
  margin-right: 10px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.input-action-button:hover {
  background-color: #E3EFFF;
}
