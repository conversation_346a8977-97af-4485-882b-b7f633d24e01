/* News Page Styles */
.news-page {
  padding: 15px;
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  background-color: #FFFFFF;
  color: #333333;
  min-height: 100%;
}

/* Header Styles */
.news-header-bar {
  margin-bottom: 15px;
  background: linear-gradient(to bottom, #0A246A, #3A6EA5);
  color: white;
  padding: 10px 15px;
  border-radius: 3px;
}

.news-header-bar h1 {
  margin: 0;
  font-size: 20px;
}

/* Breaking News Banner */
.breaking-news {
  display: flex;
  margin-bottom: 15px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  overflow: hidden;
}

.breaking-news-label {
  background: linear-gradient(to bottom, #CC0000, #990000);
  color: white;
  font-weight: bold;
  padding: 8px 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.breaking-news-content {
  flex: 1;
  background-color: #F9F9F9;
  padding: 8px 0;
  font-size: 12px;
}

.breaking-news-content marquee {
  color: #333333;
}

.news-header {
  margin-bottom: 15px;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.news-header h1 {
  color: #0A246A;
  font-size: 24px;
  margin: 0;
  font-weight: bold;
}

.news-date {
  color: #666666;
  font-size: 11px;
}

.news-categories {
  display: flex;
  border-bottom: 1px solid #ACA899;
  border-top: 1px solid #ACA899;
  background-color: #F1EFE2;
  overflow-x: auto;
  padding: 0 5px;
}

.category-item {
  padding: 5px 15px;
  font-size: 12px;
  cursor: pointer;
  border: 1px solid transparent;
  border-bottom: none;
  margin-right: 2px;
  white-space: nowrap;
}

.category-item.active {
  background-color: white;
  border-color: #ACA899;
  border-bottom: 1px solid white;
  margin-bottom: -1px;
  font-weight: bold;
}

.category-item:not(.active):hover {
  background-color: #F9F9F9;
}

.main-content {
  display: flex;
  padding: 15px 0;
  gap: 15px;
}

.left-column {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.section-title {
  color: #0A246A;
  font-size: 16px;
  margin: 0 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #ACA899;
}

.featured-news {
  border: 1px solid #ACA899;
  background: linear-gradient(to bottom, #FFFFFF, #F1EFE2);
  padding: 15px;
  border-radius: 3px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.featured-news:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: #7F9DB9;
}

.featured-news img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  margin-bottom: 15px;
  border: 1px solid #ACA899;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.featured-news:hover img {
  border-color: #7F9DB9;
}

.article-category {
  font-size: 11px;
  color: #666666;
  margin-bottom: 5px;
}

.featured-news h2 {
  color: #0A246A;
  font-size: 16px;
  margin: 5px 0 5px 0;
}

.article-date {
  font-size: 11px;
  color: #666666;
  margin-bottom: 10px;
}

.featured-news p {
  font-size: 11px;
  line-height: 1.5;
  margin: 8px 0;
}

.read-more-btn {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  color: #0A246A;
  border: 1px solid #7F9DB9;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  border-radius: 3px;
}

.read-more-btn:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.news-list-item {
  border: 1px solid #ACA899;
  padding: 10px;
  cursor: pointer;
  background-color: #F9F9F9;
  border-radius: 3px;
}

.news-list-item:hover {
  background-color: #F1EFE2;
}

.news-list-item h3 {
  color: #0A246A;
  font-size: 14px;
  margin: 5px 0;
}

.news-list-item p {
  font-size: 11px;
  line-height: 1.4;
  margin: 5px 0 0 0;
}

.weather-widget {
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 15px;
  background-color: #F9F9F9;
}

.weather-widget h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #0A246A;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.weather-info {
  display: flex;
  align-items: center;
}

.weather-icon {
  font-size: 32px;
  margin-right: 10px;
}

.weather-details {
  flex: 1;
}

.weather-location {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #0A246A;
}

.weather-temp {
  font-size: 12px;
  color: #333333;
}

.more-news {
  margin-bottom: 20px;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.news-article {
  display: flex;
  border: 1px solid #ACA899;
  cursor: pointer;
  background-color: #F9F9F9;
  border-radius: 3px;
}

.news-article:hover {
  background-color: #F1EFE2;
}

.article-image {
  width: 100px;
  min-width: 100px;
  height: 100px;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-content {
  padding: 10px;
  flex: 1;
}

.news-article h3 {
  color: #0A246A;
  font-size: 14px;
  margin: 5px 0;
}

.news-article:hover h3 {
  text-decoration: underline;
}

.news-article p {
  font-size: 11px;
  line-height: 1.4;
  margin: 5px 0 0 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .news-layout {
    flex-direction: column;
  }

  .news-sidebar {
    min-width: 100%;
  }

  .weather-forecast {
    justify-content: space-around;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .news-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .breaking-news {
    flex-direction: column;
  }

  .breaking-news-label {
    width: 100%;
    justify-content: center;
    padding: 5px;
  }

  .featured-news-image img {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .news-grid {
    grid-template-columns: 1fr;
  }

  .news-categories {
    padding-bottom: 5px;
  }

  .news-item-image img {
    height: 120px;
  }

  .subscribe-form {
    flex-direction: column;
  }

  .subscribe-button {
    width: 100%;
    margin-top: 5px;
  }
}

/* News Item Styles */
.news-item {
  border: 1px solid #ACA899;
  background: linear-gradient(to bottom, #FFFFFF, #F9F9F9);
  padding: 12px;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.news-item:hover {
  background: linear-gradient(to bottom, #F9F9F9, #F1EFE2);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  border-color: #7F9DB9;
  transform: translateY(-2px);
}

.news-item-image {
  margin-bottom: 10px;
  overflow: hidden;
  border-radius: 2px;
}

.news-item-image img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border: 1px solid #ACA899;
  transition: all 0.3s ease;
}

.news-item:hover .news-item-image img {
  border-color: #7F9DB9;
  transform: scale(1.03);
}

.news-item-content {
  padding: 5px;
}

.news-item-content h3 {
  color: #0A246A;
  font-size: 14px;
  margin: 0 0 8px 0;
  transition: all 0.2s ease;
}

.news-item:hover .news-item-content h3 {
  color: #3A6EA5;
}

.featured-news-image {
  margin-bottom: 10px;
}

.featured-news-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border: 1px solid #ACA899;
}

.featured-news-content {
  padding: 5px;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #666666;
  margin-bottom: 8px;
}

/* News Sections */
.news-section {
  display: none;
}

.news-section.active {
  display: block;
}

.news-section h2 {
  color: #0A246A;
  font-size: 16px;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding: 5px 0;
}

/* Layout Styles */
.news-layout {
  display: flex;
  gap: 20px;
}

.news-main {
  flex: 3;
}

.news-sidebar {
  flex: 1;
  min-width: 250px;
}

/* Sidebar Widgets */
.weather-widget, .popular-stories-widget, .subscribe-widget {
  background: linear-gradient(to bottom, #FFFFFF, #F1EFE2);
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.weather-widget h3, .popular-stories-widget h3, .subscribe-widget h3 {
  color: #0A246A;
  font-size: 14px;
  margin: 0 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #ACA899;
}

/* Weather Widget Styles */
.weather-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.weather-icon {
  font-size: 32px;
  margin-right: 10px;
}

.weather-details {
  flex: 1;
}

.weather-location {
  font-weight: bold;
  font-size: 14px;
  color: #0A246A;
  margin-bottom: 3px;
}

.weather-temp {
  font-size: 12px;
  color: #333333;
  margin-bottom: 3px;
}

.weather-date {
  font-size: 11px;
  color: #666666;
}

.weather-forecast {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #E5E5E5;
  padding-top: 10px;
}

.forecast-day {
  text-align: center;
  flex: 1;
}

.forecast-date {
  font-size: 11px;
  color: #666666;
  margin-bottom: 5px;
}

.forecast-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.forecast-temp {
  font-size: 12px;
  color: #333333;
}

/* Popular Stories Widget Styles */
.popular-stories-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.popular-stories-list li {
  padding: 8px 0;
  border-bottom: 1px solid #E5E5E5;
}

.popular-stories-list li:last-child {
  border-bottom: none;
}

.popular-stories-list a {
  color: #0A246A;
  text-decoration: none;
  font-size: 12px;
  display: block;
  margin-bottom: 3px;
}

.popular-stories-list a:hover {
  text-decoration: underline;
  color: #3A6EA5;
}

.story-views {
  font-size: 11px;
  color: #666666;
  display: block;
}

/* Subscribe Widget Styles */
.subscribe-widget p {
  font-size: 12px;
  color: #333333;
  margin-bottom: 10px;
}

.subscribe-form {
  display: flex;
  gap: 5px;
}

.subscribe-input {
  flex: 1;
  padding: 5px 8px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  font-size: 12px;
  color: #333333;
}

.subscribe-button {
  background: linear-gradient(to bottom, #0A246A, #3A6EA5);
  color: white;
  border: 1px solid #0A246A;
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.subscribe-button:hover {
  background: linear-gradient(to bottom, #3A6EA5, #0A246A);
}
