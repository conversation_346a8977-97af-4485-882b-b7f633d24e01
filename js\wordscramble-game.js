// Word Scramble Game functionality
let wsCurrentWord = '';
let wsScrambledWord = '';
let wsHint = '';
let wsScore = 0;
let wsLevel = 1;
let wsTimer = 60;
let wsTimerInterval;
let wsGameRunning = false;
let wsGameOver = false;

// Word lists by difficulty
const wsEasyWords = [
  { word: 'apple', hint: 'A common fruit' },
  { word: 'house', hint: 'A place to live' },
  { word: 'chair', hint: 'You sit on it' },
  { word: 'table', hint: 'You eat on it' },
  { word: 'phone', hint: 'Communication device' },
  { word: 'water', hint: 'You drink it' },
  { word: 'bread', hint: 'A basic food' },
  { word: 'smile', hint: 'A happy expression' },
  { word: 'light', hint: 'Opposite of dark' },
  { word: 'music', hint: 'You listen to it' },
  { word: 'book', hint: 'Contains written pages' },
  { word: 'tree', hint: 'Has leaves and branches' },
  { word: 'door', hint: 'Entrance to a room' },
  { word: 'bird', hint: 'Flying animal with feathers' },
  { word: 'fish', hint: 'Lives in water' },
  { word: 'game', hint: 'Activity for fun' },
  { word: 'ball', hint: 'Round object used in sports' },
  { word: 'cake', hint: 'Sweet dessert for celebrations' },
  { word: 'shoe', hint: 'Worn on feet' },
  { word: 'clock', hint: 'Tells the time' }
];

const wsMediumWords = [
  { word: 'computer', hint: 'Electronic device for processing data' },
  { word: 'elephant', hint: 'Large mammal with a trunk' },
  { word: 'mountain', hint: 'A large natural elevation' },
  { word: 'calendar', hint: 'Shows days, weeks, and months' },
  { word: 'keyboard', hint: 'Used for typing' },
  { word: 'umbrella', hint: 'Protects from rain' },
  { word: 'sandwich', hint: 'A popular lunch item' },
  { word: 'painting', hint: 'Art on canvas' },
  { word: 'birthday', hint: 'Annual celebration' },
  { word: 'treasure', hint: 'Valuable collection' },
  { word: 'bicycle', hint: 'Two-wheeled vehicle' },
  { word: 'library', hint: 'Building with books' },
  { word: 'diamond', hint: 'Precious gemstone' },
  { word: 'festival', hint: 'Celebration with events' },
  { word: 'hospital', hint: 'Medical care facility' },
  { word: 'journey', hint: 'Travel from one place to another' },
  { word: 'kitchen', hint: 'Room for cooking' },
  { word: 'mystery', hint: 'Something difficult to explain' },
  { word: 'rainbow', hint: 'Colorful arc in the sky after rain' },
  { word: 'weekend', hint: 'Saturday and Sunday' }
];

const wsHardWords = [
  { word: 'encyclopedia', hint: 'Reference work with information' },
  { word: 'photography', hint: 'Art of capturing images' },
  { word: 'mathematics', hint: 'Study of numbers and patterns' },
  { word: 'architecture', hint: 'Design of buildings' },
  { word: 'celebration', hint: 'A special event' },
  { word: 'imagination', hint: 'Mental creative ability' },
  { word: 'technology', hint: 'Application of scientific knowledge' },
  { word: 'vocabulary', hint: 'Collection of words' },
  { word: 'restaurant', hint: 'Place to eat out' },
  { word: 'experience', hint: 'Knowledge gained through participation' },
  { word: 'achievement', hint: 'Something accomplished successfully' },
  { word: 'environment', hint: 'Surroundings or conditions' },
  { word: 'government', hint: 'System for ruling a country' },
  { word: 'university', hint: 'Institution of higher education' },
  { word: 'opportunity', hint: 'Favorable time or circumstance' },
  { word: 'development', hint: 'Process of growth or progress' },
  { word: 'information', hint: 'Facts or knowledge' },
  { word: 'competition', hint: 'Contest for a prize or award' },
  { word: 'destination', hint: 'Place to which someone is going' },
  { word: 'relationship', hint: 'Connection between people' }
];

const wsExpertWords = [
  { word: 'extraordinary', hint: 'Beyond what is usual or normal' },
  { word: 'sophisticated', hint: 'Complex or refined' },
  { word: 'conscientious', hint: 'Careful and thorough' },
  { word: 'revolutionary', hint: 'Causing complete change' },
  { word: 'philosophical', hint: 'Relating to the study of knowledge' },
  { word: 'enthusiastically', hint: 'With great excitement' },
  { word: 'entrepreneurship', hint: 'Practice of starting businesses' },
  { word: 'biodiversity', hint: 'Variety of plant and animal life' },
  { word: 'collaboration', hint: 'Working together' },
  { word: 'sustainability', hint: 'Ability to maintain at a certain level' },
  { word: 'procrastination', hint: 'Delaying or postponing tasks' },
  { word: 'comprehensive', hint: 'Complete or thorough' },
  { word: 'determination', hint: 'Firmness of purpose' },
  { word: 'classification', hint: 'Arrangement into groups' },
  { word: 'controversial', hint: 'Causing disagreement or discussion' }
];

// Initialize the Word Scramble game
function initWordScrambleGame() {
  // Get DOM elements
  const wordGuessInput = document.getElementById('word-guess');
  const submitGuessButton = document.getElementById('submit-guess');
  const newWordButton = document.getElementById('new-word');

  // Add event listeners
  if (submitGuessButton) {
    submitGuessButton.addEventListener('click', checkGuess);
  }

  if (newWordButton) {
    newWordButton.addEventListener('click', getNewWord);
  }

  if (wordGuessInput) {
    wordGuessInput.addEventListener('keypress', function(event) {
      if (event.key === 'Enter') {
        checkGuess();
      }
    });
  }

  // Initialize difficulty buttons
  initWordScrambleDifficultyButtons();

  // Reset game state
  resetWordScrambleGame();
}

// Initialize difficulty buttons
function initWordScrambleDifficultyButtons() {
  const easyButton = document.getElementById('ws-easy-mode');
  const mediumButton = document.getElementById('ws-medium-mode');
  const hardButton = document.getElementById('ws-hard-mode');
  const expertButton = document.getElementById('ws-expert-mode');

  // Function to set active button
  function setActiveButton(button) {
    // Remove active class from all buttons
    document.querySelectorAll('.difficulty-button').forEach(btn => {
      btn.classList.remove('active');
    });

    // Add active class to selected button
    button.classList.add('active');
  }

  // Add event listeners
  if (easyButton) {
    easyButton.addEventListener('click', function() {
      setActiveButton(this);
      setDifficulty(1); // Level 1 (Easy)
    });
  }

  if (mediumButton) {
    mediumButton.addEventListener('click', function() {
      setActiveButton(this);
      setDifficulty(4); // Level 4 (Medium)
    });
  }

  if (hardButton) {
    hardButton.addEventListener('click', function() {
      setActiveButton(this);
      setDifficulty(7); // Level 7 (Hard)
    });
  }

  if (expertButton) {
    expertButton.addEventListener('click', function() {
      setActiveButton(this);
      setDifficulty(10); // Level 10 (Expert)
    });
  }
}

// Set difficulty level
function setDifficulty(level) {
  // Always allow changing difficulty and restart the game
  wsLevel = level;

  // Stop current game if running
  if (wsGameRunning) {
    pauseWordScrambleGame();
  }

  // Reset and restart the game
  resetWordScrambleGame();

  // Show message about difficulty
  let difficultyText = 'Easy';
  if (level === 4) difficultyText = 'Medium';
  if (level === 7) difficultyText = 'Hard';
  if (level === 10) difficultyText = 'Expert';

  showWordScrambleMessage(`Difficulty set to ${difficultyText}`);

  // Start the game with the new difficulty
  startWordScrambleGame();
}

// Start the Word Scramble game
function startWordScrambleGame() {
  if (!wsGameRunning && !wsGameOver) {
    wsGameRunning = true;

    // Start timer
    startWordScrambleTimer();

    // Get first word
    getNewWord();

    // Enable input
    const wordGuessInput = document.getElementById('word-guess');
    if (wordGuessInput) {
      wordGuessInput.disabled = false;
      wordGuessInput.focus();
    }
  } else if (wsGameOver) {
    restartWordScrambleGame();
  }
}

// Pause the Word Scramble game
function pauseWordScrambleGame() {
  wsGameRunning = false;
  clearInterval(wsTimerInterval);
}

// Restart the Word Scramble game
function restartWordScrambleGame() {
  pauseWordScrambleGame();
  resetWordScrambleGame();
  startWordScrambleGame();
}

// Reset the game state
function resetWordScrambleGame() {
  wsScore = 0;
  // wsLevel is not reset here to maintain selected difficulty
  wsGameRunning = false;
  wsGameOver = false;
  wsCurrentWord = '';
  wsScrambledWord = '';
  wsHint = '';

  // Set timer based on difficulty level
  if (wsLevel <= 3) {
    wsTimer = 60;  // Easy
  } else if (wsLevel <= 6) {
    wsTimer = 75;  // Medium
  } else if (wsLevel <= 9) {
    wsTimer = 90;  // Hard
  } else {
    wsTimer = 120;  // Expert
  }

  // Update displays
  updateWordScrambleScoreDisplay();
  updateWordScrambleLevelDisplay();
  updateWordScrambleTimerDisplay();

  // Clear word display
  document.getElementById('scrambled-word').textContent = '';
  document.getElementById('word-hint').textContent = '';

  // Clear input
  const wordGuessInput = document.getElementById('word-guess');
  if (wordGuessInput) {
    wordGuessInput.value = '';
    wordGuessInput.disabled = true;
  }

  // Update active difficulty button
  updateActiveDifficultyButton();
}

// Update active difficulty button based on current level
function updateActiveDifficultyButton() {
  // Remove active class from all buttons
  document.querySelectorAll('.difficulty-button').forEach(btn => {
    btn.classList.remove('active');
  });

  // Add active class to the appropriate button
  let buttonId = 'ws-easy-mode';
  if (wsLevel >= 4 && wsLevel < 7) {
    buttonId = 'ws-medium-mode';
  } else if (wsLevel >= 7 && wsLevel < 10) {
    buttonId = 'ws-hard-mode';
  } else if (wsLevel >= 10) {
    buttonId = 'ws-expert-mode';
  }

  const activeButton = document.getElementById(buttonId);
  if (activeButton) {
    activeButton.classList.add('active');
  }
}

// Start the timer
function startWordScrambleTimer() {
  clearInterval(wsTimerInterval);

  wsTimerInterval = setInterval(function() {
    wsTimer--;
    updateWordScrambleTimerDisplay();

    if (wsTimer <= 0) {
      handleWordScrambleGameOver();
    }
  }, 1000);
}

// Get a new word based on current level
function getNewWord() {
  let wordList;
  let difficultyText = '';

  // Select word list based on level
  if (wsLevel <= 3) {
    wordList = wsEasyWords;
    difficultyText = 'Easy';
  } else if (wsLevel <= 6) {
    wordList = wsMediumWords;
    difficultyText = 'Medium';
  } else if (wsLevel <= 9) {
    wordList = wsHardWords;
    difficultyText = 'Hard';
  } else {
    wordList = wsExpertWords;
    difficultyText = 'Expert';
  }

  // Get random word from list
  const randomIndex = Math.floor(Math.random() * wordList.length);
  const wordObj = wordList[randomIndex];

  wsCurrentWord = wordObj.word;
  wsHint = wordObj.hint;

  // Scramble the word
  wsScrambledWord = scrambleWord(wsCurrentWord);

  // Update display
  document.getElementById('scrambled-word').textContent = wsScrambledWord;

  // Show hint based on difficulty (no hints for Expert)
  const hintElement = document.getElementById('word-hint');
  if (hintElement) {
    if (wsLevel >= 10) {
      // Expert mode - no hints
      hintElement.textContent = `Difficulty: ${difficultyText} (No hints available)`;
    } else {
      // Other difficulties - show hints
      hintElement.textContent = `Hint: ${wsHint} (${difficultyText})`;
    }
  }

  // Clear input
  const wordGuessInput = document.getElementById('word-guess');
  if (wordGuessInput) {
    wordGuessInput.value = '';
    wordGuessInput.focus();
  }
}

// Scramble a word
function scrambleWord(word) {
  // Convert to array, shuffle, and join back
  const wordArray = word.split('');

  // Fisher-Yates shuffle algorithm
  for (let i = wordArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [wordArray[i], wordArray[j]] = [wordArray[j], wordArray[i]];
  }

  // Make sure the scrambled word is different from the original
  let scrambled = wordArray.join('');
  if (scrambled === word) {
    return scrambleWord(word); // Try again if same as original
  }

  return scrambled;
}

// Check the player's guess
function checkGuess() {
  if (!wsGameRunning) return;

  const wordGuessInput = document.getElementById('word-guess');
  if (!wordGuessInput) return;

  const guess = wordGuessInput.value.trim().toLowerCase();

  if (guess === wsCurrentWord) {
    // Correct guess
    const pointsEarned = calculatePoints();
    wsScore += pointsEarned;
    updateWordScrambleScoreDisplay();

    // Show success message
    showWordScrambleMessage(`Correct! +${pointsEarned} points`);

    // Check for level up
    if (wsScore >= wsLevel * 100) {
      levelUp();
    }

    // Get new word
    getNewWord();
  } else if (guess !== '') {
    // Wrong guess
    showWordScrambleMessage('Try again!');

    // Clear input
    wordGuessInput.value = '';
    wordGuessInput.focus();
  }
}

// Calculate points based on word length, difficulty, and time
function calculatePoints() {
  // Base points: word length * 10
  let points = wsCurrentWord.length * 10;

  // Determine difficulty multiplier
  let difficultyMultiplier = 1;
  if (wsLevel <= 3) {
    difficultyMultiplier = 1;  // Easy
  } else if (wsLevel <= 6) {
    difficultyMultiplier = 1.5;  // Medium
  } else if (wsLevel <= 9) {
    difficultyMultiplier = 2;  // Hard
  } else {
    difficultyMultiplier = 3;  // Expert
  }

  // Apply difficulty multiplier
  points = Math.round(points * difficultyMultiplier);

  // Bonus points for higher levels
  points += (wsLevel - 1) * 10;

  return points;
}

// Level up
function levelUp() {
  wsLevel++;
  updateWordScrambleLevelDisplay();

  // Add time bonus for leveling up based on new level
  let timeBonus = 15;

  // More time for higher difficulties
  if (wsLevel === 4) {
    timeBonus = 20;  // Starting Medium difficulty
    showWordScrambleMessage(`Level Up! Medium difficulty! +${timeBonus} seconds`);
  } else if (wsLevel === 7) {
    timeBonus = 25;  // Starting Hard difficulty
    showWordScrambleMessage(`Level Up! Hard difficulty! +${timeBonus} seconds`);
  } else if (wsLevel === 10) {
    timeBonus = 30;  // Starting Expert difficulty
    showWordScrambleMessage(`Level Up! Expert difficulty! +${timeBonus} seconds`);
  } else {
    showWordScrambleMessage(`Level Up! +${timeBonus} seconds`);
  }

  wsTimer += timeBonus;
  updateWordScrambleTimerDisplay();
}

// Show a message
function showWordScrambleMessage(message) {
  const wordHint = document.getElementById('word-hint');
  if (wordHint) {
    const originalHint = wsHint;
    wordHint.textContent = message;

    // Restore hint after 2 seconds
    setTimeout(function() {
      if (wordHint.textContent === message) {
        if (wsLevel >= 10) {
          // Expert mode - no hints
          wordHint.textContent = `Difficulty: Expert (No hints available)`;
        } else {
          // Other difficulties - show hints
          wordHint.textContent = `Hint: ${originalHint} (${getDifficultyText(wsLevel)})`;
        }
      }
    }, 2000);
  }
}

// Get difficulty text based on level
function getDifficultyText(level) {
  if (level <= 3) return 'Easy';
  if (level <= 6) return 'Medium';
  if (level <= 9) return 'Hard';
  return 'Expert';
}

// Handle game over
function handleWordScrambleGameOver() {
  wsGameRunning = false;
  wsGameOver = true;
  clearInterval(wsTimerInterval);

  // Disable input
  const wordGuessInput = document.getElementById('word-guess');
  if (wordGuessInput) {
    wordGuessInput.disabled = true;
  }

  // Show game over message
  document.getElementById('scrambled-word').textContent = 'GAME OVER';

  // Show final score and difficulty
  const difficultyText = getDifficultyText(wsLevel);
  document.getElementById('word-hint').textContent = `Final Score: ${wsScore} (${difficultyText})`;

  // Save high score
  const isHighScore = saveWordScrambleHighScore(wsScore);
  if (isHighScore) {
    // If it's a high score, update the message
    setTimeout(function() {
      document.getElementById('word-hint').textContent = `NEW HIGH SCORE: ${wsScore} (${difficultyText})`;
    }, 1000);
  }
}

// Update score display
function updateWordScrambleScoreDisplay() {
  const scoreElement = document.getElementById('wordscramble-score');
  if (scoreElement) {
    scoreElement.textContent = wsScore;
  }
}

// Update level display
function updateWordScrambleLevelDisplay() {
  const levelElement = document.getElementById('wordscramble-level');
  if (levelElement) {
    levelElement.textContent = wsLevel;
  }
}

// Update timer display
function updateWordScrambleTimerDisplay() {
  const timerElement = document.getElementById('wordscramble-timer');
  if (timerElement) {
    timerElement.textContent = wsTimer;
  }
}

// Save high score
function saveWordScrambleHighScore(score) {
  const currentHighScore = localStorage.getItem('wordscramble-high-score') || 0;
  if (score > currentHighScore) {
    localStorage.setItem('wordscramble-high-score', score);
    return true;
  }
  return false;
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the game
  initWordScrambleGame();
});
