// Script to generate preloaded article index for Bingopedia
const fs = require('fs');
const path = require('path');

// Path to the index.json file
const indexFilePath = path.join(__dirname, 'bingopedia', 'index.json');
const outputFile = path.join(__dirname, 'js', 'preloaded-article-index.js');

// Function to generate preloaded index
function generatePreloadedIndex() {
    try {
        // Read the index.json file
        const indexContent = fs.readFileSync(indexFilePath, 'utf8');
        
        // Parse the JSON
        const indexData = JSON.parse(indexContent);
        
        // Generate JavaScript file with preloaded index
        const jsContent = `// Preloaded article index for Bingopedia
// This file is auto-generated - do not edit manually
window.preloadedArticleIndex = ${JSON.stringify(indexData, null, 2)};
`;
        
        // Write to output file
        fs.writeFileSync(outputFile, jsContent, 'utf8');
        
        console.log(`Successfully generated preloaded index with ${indexData.articles.length} articles`);
        console.log(`Output file: ${outputFile}`);
    } catch (error) {
        console.error(`Error generating preloaded index: ${error.message}`);
    }
}

// Run the generation process
generatePreloadedIndex();
