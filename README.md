# Unreal Browser Website

A nostalgic browser-themed website with various pages including weather, finance, maps, calendar, and sports.

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (v12 or higher)

### Running the Website

#### Windows

1. Double-click the `start-server.bat` file
2. The website will automatically open in your default browser
3. The server will run on `0.0.0.0:8002`

#### macOS/Linux

1. Open Terminal
2. Navigate to the project directory
3. Make the script executable (first time only): `chmod +x start-server.sh`
4. Run the script: `./start-server.sh`
5. The website will automatically open in your default browser
6. The server will run on `0.0.0.0:8002`

### Accessing from Other Devices

To access the website from other devices on your network:

1. Find your computer's IP address:
   - Windows: Open Command Prompt and type `ipconfig`
   - macOS: Open Terminal and type `ifconfig`
   - Linux: Open Terminal and type `ip addr`

2. On the other device, open a browser and navigate to:
   ```
   http://YOUR_IP_ADDRESS:8002
   ```
   (Replace YOUR_IP_ADDRESS with your actual IP address)

## Features

- Home page with search and quick links
- Weather page with city selection and forecasts
- Finance page with stock market information
- Maps page with navigation features
- Calendar page with events and reminders
- Sports page with live scores and news for various sports

## Stopping the Server

- Windows: Close the command prompt window or press Ctrl+C
- macOS/Linux: Press Ctrl+C in the terminal

## Troubleshooting

If the browser doesn't open automatically:
1. Make sure the server is running (check the command prompt/terminal)
2. Open your browser manually and navigate to `http://localhost:8000`

If you can't access the website from other devices:
1. Make sure both devices are on the same network
2. Check if your firewall is blocking port 8000
3. Verify you're using the correct IP address
