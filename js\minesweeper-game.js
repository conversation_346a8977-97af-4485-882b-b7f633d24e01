// Minesweeper Game functionality
let msCanvas, msCtx;
let msBoard = [];
let msRevealed = [];
let msFlags = [];
let msMines = [];
let msGameRunning = false;
let msGameOver = false;
let msWin = false;
let msCellSize = 30;
let msRows = 10;
let msColumns = 10;
let msMineCount = 10;
let msFlagsLeft = 10;
let msTimer = 0;
let msTimerInterval;
let msDifficulty = 'easy';

// Colors for numbers
const msNumberColors = [
  null,       // 0 - no number
  '#0000FF',  // 1 - blue
  '#008000',  // 2 - green
  '#FF0000',  // 3 - red
  '#000080',  // 4 - dark blue
  '#800000',  // 5 - maroon
  '#008080',  // 6 - teal
  '#000000',  // 7 - black
  '#808080'   // 8 - gray
];

// Initialize the Minesweeper game
function initMinesweeperGame() {
  msCanvas = document.getElementById('minesweeper-canvas');
  msCtx = msCanvas.getContext('2d');
  
  // Add mouse event listeners
  msCanvas.addEventListener('mousedown', handleMinesweeperClick);
  msCanvas.addEventListener('contextmenu', function(e) {
    e.preventDefault(); // Prevent context menu from appearing
  });
  
  // Initialize difficulty buttons
  initDifficultyButtons();
  
  // Reset game with current difficulty
  resetMinesweeperGame();
}

// Initialize difficulty buttons
function initDifficultyButtons() {
  const easyButton = document.getElementById('easy-mode');
  const mediumButton = document.getElementById('medium-mode');
  const hardButton = document.getElementById('hard-mode');
  
  if (easyButton) {
    easyButton.addEventListener('click', function() {
      setDifficulty('easy');
    });
  }
  
  if (mediumButton) {
    mediumButton.addEventListener('click', function() {
      setDifficulty('medium');
    });
  }
  
  if (hardButton) {
    hardButton.addEventListener('click', function() {
      setDifficulty('hard');
    });
  }
}

// Set difficulty
function setDifficulty(difficulty) {
  // Update active button
  document.querySelectorAll('.difficulty-button').forEach(button => {
    button.classList.remove('active');
  });
  document.getElementById(`${difficulty}-mode`).classList.add('active');
  
  // Set game parameters based on difficulty
  msDifficulty = difficulty;
  
  switch (difficulty) {
    case 'easy':
      msRows = 10;
      msColumns = 10;
      msMineCount = 10;
      msCellSize = 30;
      break;
    case 'medium':
      msRows = 16;
      msColumns = 16;
      msMineCount = 40;
      msCellSize = 25;
      break;
    case 'hard':
      msRows = 16;
      msColumns = 30;
      msMineCount = 99;
      msCellSize = 20;
      break;
  }
  
  // Reset the game with new settings
  resetMinesweeperGame();
}

// Start the Minesweeper game
function startMinesweeperGame() {
  if (!msGameRunning && !msGameOver) {
    msGameRunning = true;
    startTimer();
  }
}

// Pause the Minesweeper game
function pauseMinesweeperGame() {
  msGameRunning = false;
  stopTimer();
}

// Restart the Minesweeper game
function restartMinesweeperGame() {
  pauseMinesweeperGame();
  resetMinesweeperGame();
}

// Reset the game state
function resetMinesweeperGame() {
  // Adjust canvas size based on board dimensions
  msCanvas.width = msColumns * msCellSize;
  msCanvas.height = msRows * msCellSize;
  
  // Initialize empty board
  msBoard = Array(msRows).fill().map(() => Array(msColumns).fill(0));
  msRevealed = Array(msRows).fill().map(() => Array(msColumns).fill(false));
  msFlags = Array(msRows).fill().map(() => Array(msColumns).fill(false));
  msMines = [];
  
  // Reset game state
  msGameRunning = false;
  msGameOver = false;
  msWin = false;
  msFlagsLeft = msMineCount;
  msTimer = 0;
  
  // Update display
  updateMinesLeftDisplay();
  updateTimerDisplay();
  
  // Draw initial board
  drawMinesweeperBoard();
}

// Generate mines (only after first click)
function generateMines(firstRow, firstCol) {
  // Clear any existing mines
  msBoard = Array(msRows).fill().map(() => Array(msColumns).fill(0));
  msMines = [];
  
  // Generate random mines
  let minesPlaced = 0;
  while (minesPlaced < msMineCount) {
    const row = Math.floor(Math.random() * msRows);
    const col = Math.floor(Math.random() * msColumns);
    
    // Don't place mine on first click or where a mine already exists
    if ((row !== firstRow || col !== firstCol) && msBoard[row][col] !== -1) {
      msBoard[row][col] = -1; // -1 represents a mine
      msMines.push({ row, col });
      minesPlaced++;
    }
  }
  
  // Calculate numbers for adjacent cells
  for (let row = 0; row < msRows; row++) {
    for (let col = 0; col < msColumns; col++) {
      // Skip cells with mines
      if (msBoard[row][col] === -1) continue;
      
      // Count adjacent mines
      let count = 0;
      for (let r = Math.max(0, row - 1); r <= Math.min(msRows - 1, row + 1); r++) {
        for (let c = Math.max(0, col - 1); c <= Math.min(msColumns - 1, col + 1); c++) {
          if (msBoard[r][c] === -1) count++;
        }
      }
      
      msBoard[row][col] = count;
    }
  }
}

// Handle mouse click
function handleMinesweeperClick(event) {
  if (msGameOver) return;
  
  // Get mouse position relative to canvas
  const rect = msCanvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  
  // Convert to board coordinates
  const col = Math.floor(x / msCellSize);
  const row = Math.floor(y / msCellSize);
  
  // Check if coordinates are valid
  if (row < 0 || row >= msRows || col < 0 || col >= msColumns) return;
  
  // Right click - toggle flag
  if (event.button === 2) {
    toggleFlag(row, col);
    return;
  }
  
  // Left click - reveal cell
  if (event.button === 0) {
    // First click - generate mines
    if (!msGameRunning) {
      generateMines(row, col);
      startMinesweeperGame();
    }
    
    // Can't reveal flagged cells
    if (msFlags[row][col]) return;
    
    // Reveal the cell
    revealCell(row, col);
  }
}

// Toggle flag on a cell
function toggleFlag(row, col) {
  // Can't flag revealed cells
  if (msRevealed[row][col]) return;
  
  // Toggle flag
  msFlags[row][col] = !msFlags[row][col];
  
  // Update flags left count
  msFlagsLeft += msFlags[row][col] ? -1 : 1;
  updateMinesLeftDisplay();
  
  // Redraw board
  drawMinesweeperBoard();
  
  // Check for win
  checkForWin();
}

// Reveal a cell
function revealCell(row, col) {
  // Can't reveal flagged cells or already revealed cells
  if (msFlags[row][col] || msRevealed[row][col]) return;
  
  // Reveal the cell
  msRevealed[row][col] = true;
  
  // Check if it's a mine
  if (msBoard[row][col] === -1) {
    handleMinesweeperGameOver(false);
    return;
  }
  
  // If it's an empty cell, reveal adjacent cells
  if (msBoard[row][col] === 0) {
    for (let r = Math.max(0, row - 1); r <= Math.min(msRows - 1, row + 1); r++) {
      for (let c = Math.max(0, col - 1); c <= Math.min(msColumns - 1, col + 1); c++) {
        if (r !== row || c !== col) {
          revealCell(r, c);
        }
      }
    }
  }
  
  // Redraw board
  drawMinesweeperBoard();
  
  // Check for win
  checkForWin();
}

// Check if player has won
function checkForWin() {
  // Count revealed cells
  let revealedCount = 0;
  for (let row = 0; row < msRows; row++) {
    for (let col = 0; col < msColumns; col++) {
      if (msRevealed[row][col]) revealedCount++;
    }
  }
  
  // Win condition: all non-mine cells are revealed
  if (revealedCount === (msRows * msColumns) - msMineCount) {
    handleMinesweeperGameOver(true);
  }
}

// Handle game over
function handleMinesweeperGameOver(win) {
  msGameRunning = false;
  msGameOver = true;
  msWin = win;
  stopTimer();
  
  // Reveal all mines
  for (const mine of msMines) {
    msRevealed[mine.row][mine.col] = true;
  }
  
  // Draw final board state
  drawMinesweeperBoard();
  
  // Show game over message
  drawGameOverMessage(win);
  
  // Save high score if win
  if (win) {
    saveMinesweeperHighScore(msDifficulty, msTimer);
  }
}

// Draw the Minesweeper board
function drawMinesweeperBoard() {
  // Clear canvas
  msCtx.fillStyle = '#C0C0C0';
  msCtx.fillRect(0, 0, msCanvas.width, msCanvas.height);
  
  // Draw grid
  msCtx.strokeStyle = '#808080';
  msCtx.lineWidth = 1;
  
  for (let row = 0; row < msRows; row++) {
    for (let col = 0; col < msColumns; col++) {
      const x = col * msCellSize;
      const y = row * msCellSize;
      
      // Draw cell background
      if (msRevealed[row][col]) {
        msCtx.fillStyle = '#E0E0E0';
      } else {
        msCtx.fillStyle = '#C0C0C0';
      }
      msCtx.fillRect(x, y, msCellSize, msCellSize);
      
      // Draw cell border
      msCtx.strokeRect(x, y, msCellSize, msCellSize);
      
      // Draw 3D effect for unrevealed cells
      if (!msRevealed[row][col]) {
        msCtx.fillStyle = '#FFFFFF';
        msCtx.beginPath();
        msCtx.moveTo(x, y);
        msCtx.lineTo(x + msCellSize, y);
        msCtx.lineTo(x, y + msCellSize);
        msCtx.closePath();
        msCtx.fill();
        
        msCtx.fillStyle = '#808080';
        msCtx.beginPath();
        msCtx.moveTo(x + msCellSize, y);
        msCtx.lineTo(x + msCellSize, y + msCellSize);
        msCtx.lineTo(x, y + msCellSize);
        msCtx.closePath();
        msCtx.fill();
      }
      
      // Draw flag
      if (msFlags[row][col]) {
        drawFlag(x, y);
      }
      
      // Draw mine (if revealed)
      if (msRevealed[row][col] && msBoard[row][col] === -1) {
        drawMine(x, y);
      }
      
      // Draw number (if revealed and not a mine)
      if (msRevealed[row][col] && msBoard[row][col] > 0) {
        drawNumber(x, y, msBoard[row][col]);
      }
    }
  }
}

// Draw a flag
function drawFlag(x, y) {
  const centerX = x + msCellSize / 2;
  const centerY = y + msCellSize / 2;
  
  // Flag pole
  msCtx.strokeStyle = '#000000';
  msCtx.lineWidth = 1;
  msCtx.beginPath();
  msCtx.moveTo(centerX, centerY + msCellSize * 0.25);
  msCtx.lineTo(centerX, centerY - msCellSize * 0.25);
  msCtx.stroke();
  
  // Flag
  msCtx.fillStyle = '#FF0000';
  msCtx.beginPath();
  msCtx.moveTo(centerX, centerY - msCellSize * 0.25);
  msCtx.lineTo(centerX + msCellSize * 0.2, centerY - msCellSize * 0.15);
  msCtx.lineTo(centerX, centerY - msCellSize * 0.05);
  msCtx.closePath();
  msCtx.fill();
}

// Draw a mine
function drawMine(x, y) {
  const centerX = x + msCellSize / 2;
  const centerY = y + msCellSize / 2;
  const radius = msCellSize * 0.3;
  
  // Mine body
  msCtx.fillStyle = '#000000';
  msCtx.beginPath();
  msCtx.arc(centerX, centerY, radius, 0, Math.PI * 2);
  msCtx.fill();
  
  // Mine spikes
  msCtx.strokeStyle = '#000000';
  msCtx.lineWidth = 1;
  for (let i = 0; i < 8; i++) {
    const angle = i * Math.PI / 4;
    msCtx.beginPath();
    msCtx.moveTo(centerX + Math.cos(angle) * radius, centerY + Math.sin(angle) * radius);
    msCtx.lineTo(centerX + Math.cos(angle) * radius * 1.5, centerY + Math.sin(angle) * radius * 1.5);
    msCtx.stroke();
  }
  
  // Mine highlight
  msCtx.fillStyle = '#FFFFFF';
  msCtx.beginPath();
  msCtx.arc(centerX - radius * 0.3, centerY - radius * 0.3, radius * 0.2, 0, Math.PI * 2);
  msCtx.fill();
}

// Draw a number
function drawNumber(x, y, number) {
  const centerX = x + msCellSize / 2;
  const centerY = y + msCellSize / 2;
  
  msCtx.fillStyle = msNumberColors[number];
  msCtx.font = `bold ${msCellSize * 0.6}px Arial`;
  msCtx.textAlign = 'center';
  msCtx.textBaseline = 'middle';
  msCtx.fillText(number.toString(), centerX, centerY);
}

// Draw game over message
function drawGameOverMessage(win) {
  const centerX = msCanvas.width / 2;
  const centerY = msCanvas.height / 2;
  
  // Semi-transparent overlay
  msCtx.fillStyle = 'rgba(0, 0, 0, 0.5)';
  msCtx.fillRect(0, 0, msCanvas.width, msCanvas.height);
  
  // Message
  msCtx.fillStyle = win ? '#00FF00' : '#FF0000';
  msCtx.font = 'bold 24px Arial';
  msCtx.textAlign = 'center';
  msCtx.textBaseline = 'middle';
  msCtx.fillText(win ? 'You Win!' : 'Game Over', centerX, centerY);
}

// Start timer
function startTimer() {
  stopTimer(); // Clear any existing timer
  msTimer = 0;
  updateTimerDisplay();
  msTimerInterval = setInterval(function() {
    msTimer++;
    updateTimerDisplay();
  }, 1000);
}

// Stop timer
function stopTimer() {
  if (msTimerInterval) {
    clearInterval(msTimerInterval);
    msTimerInterval = null;
  }
}

// Update mines left display
function updateMinesLeftDisplay() {
  const minesLeftElement = document.getElementById('mines-left');
  if (minesLeftElement) {
    minesLeftElement.textContent = msFlagsLeft;
  }
}

// Update timer display
function updateTimerDisplay() {
  const timerElement = document.getElementById('minesweeper-timer');
  if (timerElement) {
    timerElement.textContent = msTimer;
  }
}

// Save high score
function saveMinesweeperHighScore(difficulty, time) {
  const highScoreKey = `minesweeper-${difficulty}-highscore`;
  const currentHighScore = localStorage.getItem(highScoreKey) || Infinity;
  
  if (time < currentHighScore) {
    localStorage.setItem(highScoreKey, time);
    return true;
  }
  
  return false;
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the game
  initMinesweeperGame();
});
