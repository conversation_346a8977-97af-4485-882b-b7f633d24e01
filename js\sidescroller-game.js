// Adventure Run Game functionality
let ssCanvas, ssCtx;
let ssGameRunning = false;
let ssGameOver = false;
let ssScore = 0;
let ssLives = 3;
let ssLevel = 1;
let ssGameLoop;
let ssFrameCount = 0;
let ssMessage = null; // For displaying messages to the player

// Game objects
let ssPlayer = {
  x: 100,
  y: 300,
  width: 40,
  height: 50, // Reduced height to allow passing under blocks
  speed: 5,
  jumpForce: 20,
  velocityY: 0,
  isJumping: false,
  isMovingLeft: false,
  isMovingRight: false,
  direction: 'right'
};

let ssPlatforms = [];
let ssCoins = [];
let ssEnemies = [];
let ssBlocks = [];
let ssBackground = {
  x: 0,
  clouds: [],
  hills: []
};

// Physics constants
const ssGravity = 0.8;
const ssGroundLevel = 340;
const ssMaxVelocityY = 15;

// Initialize the Adventure Run game
function initSideScrollerGame() {
  ssCanvas = document.getElementById('sidescroller-canvas');
  ssCtx = ssCanvas.getContext('2d');

  // Add keyboard event listeners
  document.addEventListener('keydown', handleSideScrollerKeyDown);
  document.addEventListener('keyup', handleSideScrollerKeyUp);

  // Reset game state
  resetSideScrollerGame();
}

// Start the Adventure Run game
function startSideScrollerGame() {
  if (!ssGameRunning && !ssGameOver) {
    ssGameRunning = true;
    ssGameLoop = setInterval(sideScrollerGameLoop, 1000 / 60); // 60 FPS
  } else if (ssGameOver) {
    restartSideScrollerGame();
  }
}

// Pause the Adventure Run game
function pauseSideScrollerGame() {
  ssGameRunning = false;
  clearInterval(ssGameLoop);
}

// Restart the Adventure Run game
function restartSideScrollerGame() {
  pauseSideScrollerGame();
  resetSideScrollerGame();
  startSideScrollerGame();
}

// Reset the game state
function resetSideScrollerGame() {
  // Reset player
  ssPlayer.x = 100;
  ssPlayer.y = 300;
  ssPlayer.velocityY = 0;
  ssPlayer.isJumping = false;
  ssPlayer.isMovingLeft = false;
  ssPlayer.isMovingRight = false;
  ssPlayer.direction = 'right';

  // Reset game state
  ssScore = 0;
  ssLives = 3;
  ssLevel = 1;
  ssGameRunning = false;
  ssGameOver = false;
  ssFrameCount = 0;

  // Reset background
  ssBackground.x = 0;
  ssBackground.clouds = [
    { x: 100, y: 80, width: 80, height: 40 },
    { x: 300, y: 60, width: 100, height: 50 },
    { x: 600, y: 100, width: 90, height: 45 }
  ];
  ssBackground.hills = [
    { x: 0, y: 350, width: 300, height: 100 },
    { x: 250, y: 350, width: 350, height: 120 },
    { x: 550, y: 350, width: 280, height: 90 }
  ];

  // Create initial level
  createLevel(ssLevel);

  // Update displays
  updateSideScrollerScoreDisplay();
  updateSideScrollerLivesDisplay();
  updateSideScrollerLevelDisplay();

  // Draw initial state
  drawSideScrollerGame();
}

// Create a level
function createLevel(level) {
  // Clear existing objects
  ssPlatforms = [];
  ssCoins = [];
  ssEnemies = [];
  ssBlocks = [];

  // Create platforms
  const platformCount = 5 + level * 2;
  for (let i = 0; i < platformCount; i++) {
    const width = 80 + Math.random() * 120;
    const x = 300 + i * 300 + Math.random() * 200;
    // Lower the platforms to make them more accessible
    const y = 250 + Math.random() * 80;

    ssPlatforms.push({
      x: x,
      y: y,
      width: width,
      height: 20
    });

    // Add coins on some platforms
    if (Math.random() > 0.3) {
      const coinCount = 1 + Math.floor(Math.random() * 3);
      for (let j = 0; j < coinCount; j++) {
        ssCoins.push({
          x: x + j * 30 + 20,
          // Place coins just above the platform
          y: y - 25,
          width: 20,
          height: 20,
          collected: false
        });
      }
    }
  }

  // Create enemies
  const enemyCount = level * 2;
  for (let i = 0; i < enemyCount; i++) {
    ssEnemies.push({
      x: 500 + i * 400 + Math.random() * 200,
      y: ssGroundLevel - 30,
      width: 40,
      height: 30,
      speed: 2 + Math.random() * level,
      direction: Math.random() > 0.5 ? 'left' : 'right',
      range: 100 + Math.random() * 100,
      startX: 500 + i * 400 + Math.random() * 200
    });
  }

  // Create blocks - make them more accessible
  const blockCount = level * 3;
  for (let i = 0; i < blockCount; i++) {
    const type = Math.random() > 0.7 ? 'question' : 'brick';

    // Try to find a valid position for the block
    let validPosition = false;
    let attempts = 0;
    let blockX, blockY;

    while (!validPosition && attempts < 10) {
      blockX = 400 + i * 200 + Math.random() * 100;
      // Place blocks higher to ensure player can pass underneath
      // Minimum height is 140 (ground level - player height - some clearance)
      blockY = 140 + Math.random() * 60;
      validPosition = true;

      // Check collision with platforms
      for (const platform of ssPlatforms) {
        if (blockX + 30 > platform.x &&
            blockX < platform.x + platform.width &&
            blockY + 30 > platform.y - 10 &&
            blockY < platform.y + platform.height) {
          validPosition = false;
          break;
        }
      }

      // Check collision with other blocks
      for (const block of ssBlocks) {
        if (blockX + 30 > block.x &&
            blockX < block.x + block.width &&
            blockY + 30 > block.y &&
            blockY < block.y + block.height) {
          validPosition = false;
          break;
        }
      }

      attempts++;
    }

    // Only add the block if we found a valid position
    if (validPosition) {
      ssBlocks.push({
        x: blockX,
        y: blockY,
        width: 30,
        height: 30,
        type: type,
        hit: false
      });
    }
  }

  // Add some fixed blocks for the first level to ensure player can reach them
  if (level === 1) {
    // Define staircase blocks - placed higher to allow player to pass underneath
    const staircaseBlocks = [
      { x: 200, y: 240, type: 'brick' },
      { x: 230, y: 200, type: 'brick' },
      { x: 260, y: 160, type: 'question' }
    ];

    // Check each staircase block for collisions with platforms
    for (const blockData of staircaseBlocks) {
      let validPosition = true;

      // Check collision with platforms
      for (const platform of ssPlatforms) {
        if (blockData.x + 30 > platform.x &&
            blockData.x < platform.x + platform.width &&
            blockData.y + 30 > platform.y - 10 &&
            blockData.y < platform.y + platform.height) {
          validPosition = false;
          break;
        }
      }

      // Only add if position is valid
      if (validPosition) {
        ssBlocks.push({
          x: blockData.x,
          y: blockData.y,
          width: 30,
          height: 30,
          type: blockData.type,
          hit: false
        });

        // Add coin above this block
        ssCoins.push({
          x: blockData.x,
          y: blockData.y - 30,
          width: 20,
          height: 20,
          collected: false
        });
      }
    }

    // Add some guaranteed coins at accessible locations
    ssCoins.push({
      x: 150,
      y: ssGroundLevel - 50,
      width: 20,
      height: 20,
      collected: false
    });

    ssCoins.push({
      x: 180,
      y: ssGroundLevel - 50,
      width: 20,
      height: 20,
      collected: false
    });

    ssCoins.push({
      x: 210,
      y: ssGroundLevel - 50,
      width: 20,
      height: 20,
      collected: false
    });
  }
}

// Main game loop
function sideScrollerGameLoop() {
  if (!ssGameRunning) return;

  // Increment frame count
  ssFrameCount++;

  // Update player
  updatePlayer();

  // Update enemies
  updateEnemies();

  // Check collisions
  checkCollisions();

  // Update background
  updateBackground();

  // Draw everything
  drawSideScrollerGame();

  // Check for level completion
  checkLevelCompletion();
}

// Update player position and state
function updatePlayer() {
  // Apply gravity
  ssPlayer.velocityY += ssGravity;

  // Limit falling speed
  if (ssPlayer.velocityY > ssMaxVelocityY) {
    ssPlayer.velocityY = ssMaxVelocityY;
  }

  // Update vertical position
  ssPlayer.y += ssPlayer.velocityY;

  // Check ground collision
  if (ssPlayer.y + ssPlayer.height > ssGroundLevel) {
    ssPlayer.y = ssGroundLevel - ssPlayer.height;
    ssPlayer.velocityY = 0;
    ssPlayer.isJumping = false;
  }

  // Handle horizontal movement
  if (ssPlayer.isMovingLeft) {
    ssPlayer.x -= ssPlayer.speed;
    ssPlayer.direction = 'left';
  }

  if (ssPlayer.isMovingRight) {
    ssPlayer.x += ssPlayer.speed;
    ssPlayer.direction = 'right';
  }

  // Keep player within canvas bounds
  if (ssPlayer.x < 0) {
    ssPlayer.x = 0;
  }

  if (ssPlayer.x + ssPlayer.width > ssCanvas.width) {
    ssPlayer.x = ssCanvas.width - ssPlayer.width;
  }
}

// Update enemies
function updateEnemies() {
  ssEnemies.forEach(enemy => {
    // Move enemy
    if (enemy.direction === 'left') {
      enemy.x -= enemy.speed;
      if (enemy.x < enemy.startX - enemy.range) {
        enemy.direction = 'right';
      }
    } else {
      enemy.x += enemy.speed;
      if (enemy.x > enemy.startX + enemy.range) {
        enemy.direction = 'left';
      }
    }
  });
}

// Update background elements
function updateBackground() {
  // Move clouds slowly
  ssBackground.clouds.forEach(cloud => {
    cloud.x -= 0.5;
    if (cloud.x + cloud.width < 0) {
      cloud.x = ssCanvas.width;
      cloud.y = 40 + Math.random() * 100;
    }
  });
}

// Check collisions
function checkCollisions() {
  // Check platform collisions
  let onPlatform = false;
  ssPlatforms.forEach(platform => {
    // Check if player is above platform and falling
    if (ssPlayer.velocityY > 0 &&
        ssPlayer.y + ssPlayer.height <= platform.y + 10 &&
        ssPlayer.y + ssPlayer.height + ssPlayer.velocityY >= platform.y &&
        ssPlayer.x + ssPlayer.width > platform.x &&
        ssPlayer.x < platform.x + platform.width) {

      ssPlayer.y = platform.y - ssPlayer.height;
      ssPlayer.velocityY = 0;
      ssPlayer.isJumping = false;
      onPlatform = true;
    }
  });

  // Check block collisions for standing on top
  ssBlocks.forEach(block => {
    // Check if player is above block and falling
    if (ssPlayer.velocityY > 0 &&
        ssPlayer.y + ssPlayer.height <= block.y + 10 &&
        ssPlayer.y + ssPlayer.height + ssPlayer.velocityY >= block.y &&
        ssPlayer.x + ssPlayer.width > block.x &&
        ssPlayer.x < block.x + block.width) {

      ssPlayer.y = block.y - ssPlayer.height;
      ssPlayer.velocityY = 0;
      ssPlayer.isJumping = false;
      onPlatform = true;
    }

    // Check if player hits block from below
    if (ssPlayer.velocityY < 0 &&
        ssPlayer.y > block.y + block.height / 2 &&
        ssPlayer.y + ssPlayer.velocityY <= block.y + block.height &&
        ssPlayer.x + ssPlayer.width > block.x &&
        ssPlayer.x < block.x + block.width) {

      ssPlayer.velocityY = 0;
      ssPlayer.y = block.y + block.height;

      // Handle block hit
      if (!block.hit && block.type === 'question') {
        block.hit = true;

        // Create a coin above the block
        ssCoins.push({
          x: block.x,
          y: block.y - 40,
          width: 20,
          height: 20,
          collected: false
        });
      }
    }

    // Check for side collisions with blocks
    if (ssPlayer.y + ssPlayer.height > block.y + 5 &&
        ssPlayer.y < block.y + block.height - 5) {

      // Hitting block from the right
      if (ssPlayer.x <= block.x + block.width &&
          ssPlayer.x + ssPlayer.width > block.x + block.width) {
        ssPlayer.x = block.x + block.width;
      }

      // Hitting block from the left
      if (ssPlayer.x + ssPlayer.width >= block.x &&
          ssPlayer.x < block.x) {
        ssPlayer.x = block.x - ssPlayer.width;
      }
    }
  });

  // Check coin collisions
  ssCoins.forEach(coin => {
    if (!coin.collected &&
        ssPlayer.x + ssPlayer.width > coin.x &&
        ssPlayer.x < coin.x + coin.width &&
        ssPlayer.y + ssPlayer.height > coin.y &&
        ssPlayer.y < coin.y + coin.height) {

      coin.collected = true;
      ssScore += 100;
      updateSideScrollerScoreDisplay();
    }
  });

  // Check enemy collisions
  ssEnemies.forEach(enemy => {
    if (ssPlayer.x + ssPlayer.width > enemy.x &&
        ssPlayer.x < enemy.x + enemy.width &&
        ssPlayer.y + ssPlayer.height > enemy.y &&
        ssPlayer.y < enemy.y + enemy.height) {

      // Check if player is jumping on enemy
      if (ssPlayer.velocityY > 0 && ssPlayer.y + ssPlayer.height < enemy.y + enemy.height / 2) {
        // Bounce off enemy
        ssPlayer.velocityY = -ssPlayer.jumpForce / 1.5;

        // Remove enemy
        const index = ssEnemies.indexOf(enemy);
        if (index > -1) {
          ssEnemies.splice(index, 1);
        }

        // Add score
        ssScore += 200;
        updateSideScrollerScoreDisplay();
      } else {
        // Player hit by enemy
        handlePlayerHit();
      }
    }
  });
}

// Handle player being hit
function handlePlayerHit() {
  ssLives--;
  updateSideScrollerLivesDisplay();

  // Check game over
  if (ssLives <= 0) {
    handleSideScrollerGameOver();
    return;
  }

  // Reset player position
  ssPlayer.x = 100;
  ssPlayer.y = 300;
  ssPlayer.velocityY = 0;
}

// Check if level is complete
function checkLevelCompletion() {
  // Check if player reaches the end
  if (ssPlayer.x > ssCanvas.width - 100) {
    // Count collected coins for bonus points
    const collectedCoins = ssCoins.filter(coin => coin.collected).length;
    const coinBonus = collectedCoins * 50; // 50 points per coin

    // Advance to next level
    ssLevel++;
    updateSideScrollerLevelDisplay();

    // Create new level
    createLevel(ssLevel);

    // Reset player position
    ssPlayer.x = 100;
    ssPlayer.y = 300;
    ssPlayer.velocityY = 0;

    // Add bonus points
    ssScore += 1000 + coinBonus;
    updateSideScrollerScoreDisplay();

    // Show level complete message
    showMessage(`Level ${ssLevel-1} Complete! +${1000 + coinBonus} points`);
  }
}

// Show a temporary message on screen
let messageTimeout;
function showMessage(text) {
  // Clear any existing timeout
  if (messageTimeout) {
    clearTimeout(messageTimeout);
  }

  // Set message text
  ssMessage = text;

  // Clear message after 3 seconds
  messageTimeout = setTimeout(() => {
    ssMessage = null;
  }, 3000);
}

// Handle game over
function handleSideScrollerGameOver() {
  ssGameRunning = false;
  ssGameOver = true;
  clearInterval(ssGameLoop);

  // Draw game over screen
  drawSideScrollerGameOver();

  // Save high score
  saveSideScrollerHighScore(ssScore);
}

// Draw the game
function drawSideScrollerGame() {
  // Clear canvas
  ssCtx.fillStyle = '#87CEEB'; // Sky blue
  ssCtx.fillRect(0, 0, ssCanvas.width, ssCanvas.height);

  // Draw background
  drawBackground();

  // Draw ground
  ssCtx.fillStyle = '#8B4513'; // Brown
  ssCtx.fillRect(0, ssGroundLevel, ssCanvas.width, ssCanvas.height - ssGroundLevel);

  // Draw grass
  ssCtx.fillStyle = '#7CFC00'; // Lawn green
  ssCtx.fillRect(0, ssGroundLevel, ssCanvas.width, 10);

  // Draw platforms
  ssPlatforms.forEach(platform => {
    ssCtx.fillStyle = '#8B4513'; // Brown
    ssCtx.fillRect(platform.x, platform.y, platform.width, platform.height);

    // Platform top
    ssCtx.fillStyle = '#A0522D'; // Sienna
    ssCtx.fillRect(platform.x, platform.y, platform.width, 5);
  });

  // Draw blocks
  ssBlocks.forEach(block => {
    if (block.type === 'question') {
      ssCtx.fillStyle = block.hit ? '#A0522D' : '#FFD700'; // Gold or brown
    } else {
      ssCtx.fillStyle = '#B87333'; // Copper
    }

    ssCtx.fillRect(block.x, block.y, block.width, block.height);
    ssCtx.strokeStyle = '#8B4513';
    ssCtx.lineWidth = 2;
    ssCtx.strokeRect(block.x, block.y, block.width, block.height);

    // Draw question mark
    if (block.type === 'question' && !block.hit) {
      ssCtx.fillStyle = '#8B4513';
      ssCtx.font = '20px Arial';
      ssCtx.textAlign = 'center';
      ssCtx.textBaseline = 'middle';
      ssCtx.fillText('?', block.x + block.width / 2, block.y + block.height / 2);
    }
  });

  // Draw coins
  ssCoins.forEach(coin => {
    if (!coin.collected) {
      ssCtx.fillStyle = '#FFD700'; // Gold
      ssCtx.beginPath();
      ssCtx.arc(coin.x + coin.width / 2, coin.y + coin.height / 2, coin.width / 2, 0, Math.PI * 2);
      ssCtx.fill();
      ssCtx.strokeStyle = '#FF8C00'; // Dark orange
      ssCtx.lineWidth = 1;
      ssCtx.stroke();
    }
  });

  // Draw enemies
  ssEnemies.forEach(enemy => {
    // Body
    ssCtx.fillStyle = '#8B0000'; // Dark red
    ssCtx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);

    // Head
    ssCtx.fillStyle = '#A52A2A'; // Brown
    ssCtx.beginPath();
    ssCtx.arc(enemy.x + enemy.width / 2, enemy.y - 5, 10, 0, Math.PI * 2);
    ssCtx.fill();

    // Eyes
    ssCtx.fillStyle = '#FFFFFF';
    ssCtx.beginPath();
    ssCtx.arc(enemy.x + enemy.width / 2 - 5, enemy.y - 8, 2, 0, Math.PI * 2);
    ssCtx.arc(enemy.x + enemy.width / 2 + 5, enemy.y - 8, 2, 0, Math.PI * 2);
    ssCtx.fill();

    ssCtx.fillStyle = '#000000';
    ssCtx.beginPath();
    ssCtx.arc(enemy.x + enemy.width / 2 - 5, enemy.y - 8, 1, 0, Math.PI * 2);
    ssCtx.arc(enemy.x + enemy.width / 2 + 5, enemy.y - 8, 1, 0, Math.PI * 2);
    ssCtx.fill();
  });

  // Draw player
  drawPlayer();

  // Draw message if exists
  if (ssMessage) {
    // Semi-transparent background for message
    ssCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ssCtx.fillRect(ssCanvas.width / 2 - 200, 50, 400, 50);

    // Message text
    ssCtx.fillStyle = '#FFFFFF';
    ssCtx.font = 'bold 18px Arial';
    ssCtx.textAlign = 'center';
    ssCtx.textBaseline = 'middle';
    ssCtx.fillText(ssMessage, ssCanvas.width / 2, 75);
  }

  // Draw coin counter (optional collection)
  const collectedCoins = ssCoins.filter(coin => coin.collected).length;
  const totalCoins = ssCoins.length;
  ssCtx.fillStyle = '#FFFFFF';
  ssCtx.font = 'bold 16px Arial';
  ssCtx.textAlign = 'left';
  ssCtx.textBaseline = 'top';
  ssCtx.fillText(`Coins: ${collectedCoins}/${totalCoins} (Bonus: +${collectedCoins * 50})`, 20, 20);
}

// Draw background elements
function drawBackground() {
  // Draw hills
  ssBackground.hills.forEach(hill => {
    ssCtx.fillStyle = '#228B22'; // Forest green
    ssCtx.beginPath();
    ssCtx.moveTo(hill.x, hill.y);
    ssCtx.quadraticCurveTo(hill.x + hill.width / 2, hill.y - hill.height, hill.x + hill.width, hill.y);
    ssCtx.fill();
  });

  // Draw clouds
  ssBackground.clouds.forEach(cloud => {
    ssCtx.fillStyle = '#FFFFFF';
    ssCtx.beginPath();
    ssCtx.ellipse(cloud.x, cloud.y, cloud.width / 2, cloud.height / 2, 0, 0, Math.PI * 2);
    ssCtx.ellipse(cloud.x + 20, cloud.y - 5, cloud.width / 3, cloud.height / 3, 0, 0, Math.PI * 2);
    ssCtx.ellipse(cloud.x - 20, cloud.y, cloud.width / 3, cloud.height / 3, 0, 0, Math.PI * 2);
    ssCtx.fill();
  });
}

// Draw player
function drawPlayer() {
  // Body - adjusted for shorter height
  ssCtx.fillStyle = '#FF0000'; // Red
  ssCtx.fillRect(ssPlayer.x, ssPlayer.y + 15, ssPlayer.width, ssPlayer.height - 15);

  // Head
  ssCtx.fillStyle = '#FFE4B5'; // Moccasin
  ssCtx.beginPath();
  ssCtx.arc(ssPlayer.x + ssPlayer.width / 2, ssPlayer.y + 8, 8, 0, Math.PI * 2);
  ssCtx.fill();

  // Hat
  ssCtx.fillStyle = '#FF0000'; // Red
  ssCtx.fillRect(ssPlayer.x + 5, ssPlayer.y, ssPlayer.width - 10, 4);
  ssCtx.fillRect(ssPlayer.x + 8, ssPlayer.y - 4, ssPlayer.width - 16, 4);

  // Eyes
  ssCtx.fillStyle = '#000000';
  if (ssPlayer.direction === 'right') {
    ssCtx.beginPath();
    ssCtx.arc(ssPlayer.x + ssPlayer.width / 2 + 3, ssPlayer.y + 7, 2, 0, Math.PI * 2);
    ssCtx.fill();
  } else {
    ssCtx.beginPath();
    ssCtx.arc(ssPlayer.x + ssPlayer.width / 2 - 3, ssPlayer.y + 7, 2, 0, Math.PI * 2);
    ssCtx.fill();
  }

  // Arms
  ssCtx.fillStyle = '#FFE4B5'; // Moccasin
  ssCtx.fillRect(ssPlayer.x - 5, ssPlayer.y + 20, 5, 12);
  ssCtx.fillRect(ssPlayer.x + ssPlayer.width, ssPlayer.y + 20, 5, 12);

  // Legs
  ssCtx.fillStyle = '#0000FF'; // Blue
  ssCtx.fillRect(ssPlayer.x + 8, ssPlayer.y + ssPlayer.height - 10, 8, 10);
  ssCtx.fillRect(ssPlayer.x + ssPlayer.width - 16, ssPlayer.y + ssPlayer.height - 10, 8, 10);

  // Shoes
  ssCtx.fillStyle = '#8B4513'; // Brown
  ssCtx.fillRect(ssPlayer.x + 8, ssPlayer.y + ssPlayer.height, 8, 3);
  ssCtx.fillRect(ssPlayer.x + ssPlayer.width - 16, ssPlayer.y + ssPlayer.height, 8, 3);
}

// Draw game over screen
function drawSideScrollerGameOver() {
  // Semi-transparent overlay
  ssCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
  ssCtx.fillRect(0, 0, ssCanvas.width, ssCanvas.height);

  // Game over text
  ssCtx.fillStyle = '#FFFFFF';
  ssCtx.font = 'bold 40px Arial';
  ssCtx.textAlign = 'center';
  ssCtx.textBaseline = 'middle';
  ssCtx.fillText('Game Over', ssCanvas.width / 2, ssCanvas.height / 2 - 40);

  // Score text
  ssCtx.font = '24px Arial';
  ssCtx.fillText(`Final Score: ${ssScore}`, ssCanvas.width / 2, ssCanvas.height / 2 + 10);

  // Restart instruction
  ssCtx.font = '18px Arial';
  ssCtx.fillText('Press the Restart button to play again', ssCanvas.width / 2, ssCanvas.height / 2 + 50);
}

// Handle keydown events
function handleSideScrollerKeyDown(event) {
  if (!ssGameRunning) return;

  switch (event.key) {
    case 'ArrowLeft':
      ssPlayer.isMovingLeft = true;
      break;
    case 'ArrowRight':
      ssPlayer.isMovingRight = true;
      break;
    case 'ArrowUp':
    case ' ': // Space bar
      if (!ssPlayer.isJumping) {
        ssPlayer.velocityY = -ssPlayer.jumpForce;
        ssPlayer.isJumping = true;
      }
      break;
  }

  // Prevent default behavior for arrow keys (scrolling)
  if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' '].includes(event.key)) {
    event.preventDefault();
  }
}

// Handle keyup events
function handleSideScrollerKeyUp(event) {
  switch (event.key) {
    case 'ArrowLeft':
      ssPlayer.isMovingLeft = false;
      break;
    case 'ArrowRight':
      ssPlayer.isMovingRight = false;
      break;
  }
}

// Update score display
function updateSideScrollerScoreDisplay() {
  const scoreElement = document.getElementById('sidescroller-score');
  if (scoreElement) {
    scoreElement.textContent = ssScore;
  }
}

// Update lives display
function updateSideScrollerLivesDisplay() {
  const livesElement = document.getElementById('sidescroller-lives');
  if (livesElement) {
    livesElement.textContent = ssLives;
  }
}

// Update level display
function updateSideScrollerLevelDisplay() {
  const levelElement = document.getElementById('sidescroller-level');
  if (levelElement) {
    levelElement.textContent = ssLevel;
  }
}

// Save high score
function saveSideScrollerHighScore(score) {
  const highScore = localStorage.getItem('sidescroller-high-score') || 0;

  if (score > highScore) {
    localStorage.setItem('sidescroller-high-score', score);
    return true;
  }

  return false;
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the game
  initSideScrollerGame();
});
