<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingo Calendar</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/calendar.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="calendar-page">
        <div class="calendar-header-bar">
          <h1>Bingo Calendar</h1>
        </div>

        <div class="calendar-content">
          <div class="calendar-container">
            <div class="calendar-navigation">
              <button id="prev-month" class="calendar-nav-button">◀ Previous</button>
              <div id="current-month-display" class="current-month">May 2010</div>
              <button id="next-month" class="calendar-nav-button">Next ▶</button>
            </div>
            
            <div class="calendar-grid">
              <div class="calendar-weekdays">
                <div class="weekday">Sun</div>
                <div class="weekday">Mon</div>
                <div class="weekday">Tue</div>
                <div class="weekday">Wed</div>
                <div class="weekday">Thu</div>
                <div class="weekday">Fri</div>
                <div class="weekday">Sat</div>
              </div>
              <div id="calendar-days" class="calendar-days">
                <!-- Calendar days will be populated by JavaScript -->
              </div>
            </div>
          </div>

          <div class="reminders-container">
            <div class="reminders-header">
              <h2>Upcoming Reminders</h2>
              <button id="add-reminder" class="add-reminder-button">Add Reminder</button>
            </div>
            <div id="reminders-list" class="reminders-list">
              <div class="reminder-item">
                <div class="reminder-date">May 15, 2010</div>
                <div class="reminder-title">Meeting with Project Team</div>
                <div class="reminder-time">10:00 AM</div>
              </div>
              <div class="reminder-item">
                <div class="reminder-date">May 18, 2010</div>
                <div class="reminder-title">Quarterly Exam Preparation</div>
                <div class="reminder-time">All Day</div>
              </div>
              <div class="reminder-item">
                <div class="reminder-date">May 20, 2010</div>
                <div class="reminder-title">Doctor's Appointment</div>
                <div class="reminder-time">3:30 PM</div>
              </div>
              <div class="reminder-item">
                <div class="reminder-date">May 25, 2010</div>
                <div class="reminder-title">Final Exam - Mathematics</div>
                <div class="reminder-time">9:00 AM</div>
              </div>
              <div class="reminder-item">
                <div class="reminder-date">May 28, 2010</div>
                <div class="reminder-title">Birthday Party - Rahul</div>
                <div class="reminder-time">6:00 PM</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/calendar.js"></script>
</body>
</html>
