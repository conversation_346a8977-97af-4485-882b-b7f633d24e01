// Snake Game functionality
let canvas, ctx;
let snake = [];
let food = {};
let direction = 'right';
let newDirection = 'right';
let gameSpeed = 100; // milliseconds
let gameInterval;
let score = 0;
let gameRunning = false;
let gameOver = false;
let gridSize = 20;
let canvasSize = 400;

// Initialize the snake game
function initSnakeGame() {
  canvas = document.getElementById('snake-canvas');
  ctx = canvas.getContext('2d');

  // Reset game state
  resetGame();

  // Add keyboard event listener
  document.addEventListener('keydown', changeDirection);
}

// Start the snake game
function startSnakeGame() {
  if (!gameRunning && !gameOver) {
    gameRunning = true;
    lastFrameTime = performance.now();
    requestAnimationFrame(gameLoop);
  } else if (gameOver) {
    restartSnakeGame();
  }
}

// Pause the snake game
function pauseSnakeGame() {
  gameRunning = false;
  // No need to clear interval as we're using requestAnimationFrame
}

// Restart the snake game
function restartSnakeGame() {
  pauseSnakeGame();
  resetGame();
  startSnakeGame();
}

// Reset the game state
function resetGame() {
  // Initialize snake
  snake = [
    { x: 5, y: 10 },
    { x: 4, y: 10 },
    { x: 3, y: 10 }
  ];

  // Initialize direction
  direction = 'right';
  newDirection = 'right';

  // Initialize score
  score = 0;
  updateScore(0);

  // Create initial food
  createFood();

  // Reset game state
  gameRunning = false;
  gameOver = false;

  // Draw initial state
  draw();
}

// Main game loop with frame limiting
let lastFrameTime = 0;
const frameInterval = 1000 / 15; // Limit to 15 FPS for better performance

function gameLoop(timestamp) {
  if (!gameRunning) return;

  // Calculate time since last frame
  if (!timestamp) timestamp = performance.now();
  const elapsed = timestamp - lastFrameTime;

  // Only update if enough time has passed
  if (elapsed > frameInterval) {
    lastFrameTime = timestamp - (elapsed % frameInterval);

    // Update direction
    direction = newDirection;

    // Move snake
    moveSnake();

    // Check collisions
    if (checkCollision()) {
      handleGameOver();
      return;
    }

    // Check if snake eats food
    if (snake[0].x === food.x && snake[0].y === food.y) {
      // Increase score
      updateScore(score + 1);

      // Create new food
      createFood();

      // Don't remove tail (snake grows)
    } else {
      // Remove tail
      snake.pop();
    }

    // Draw everything
    draw();
  }

  // Request next frame
  if (gameRunning) {
    requestAnimationFrame(gameLoop);
  }
}

// Move the snake
function moveSnake() {
  // Create new head based on direction
  const head = { x: snake[0].x, y: snake[0].y };

  switch (direction) {
    case 'up':
      head.y--;
      break;
    case 'down':
      head.y++;
      break;
    case 'left':
      head.x--;
      break;
    case 'right':
      head.x++;
      break;
  }

  // Add new head to the beginning of snake array
  snake.unshift(head);
}

// Check for collisions
function checkCollision() {
  const head = snake[0];

  // Check wall collision
  if (
    head.x < 0 ||
    head.x >= canvasSize / gridSize ||
    head.y < 0 ||
    head.y >= canvasSize / gridSize
  ) {
    return true;
  }

  // Check self collision (skip the head)
  for (let i = 1; i < snake.length; i++) {
    if (head.x === snake[i].x && head.y === snake[i].y) {
      return true;
    }
  }

  return false;
}

// Create food at random position
function createFood() {
  const maxPos = canvasSize / gridSize - 1;

  // Generate random position
  let foodX = Math.floor(Math.random() * maxPos);
  let foodY = Math.floor(Math.random() * maxPos);

  // Make sure food doesn't appear on snake
  let foodOnSnake = true;
  while (foodOnSnake) {
    foodOnSnake = false;
    for (let i = 0; i < snake.length; i++) {
      if (snake[i].x === foodX && snake[i].y === foodY) {
        foodOnSnake = true;
        foodX = Math.floor(Math.random() * maxPos);
        foodY = Math.floor(Math.random() * maxPos);
        break;
      }
    }
  }

  food = { x: foodX, y: foodY };
}

// Handle game over
function handleGameOver() {
  gameRunning = false;
  gameOver = true;
  clearInterval(gameInterval);

  // Check if it's a high score
  const isHighScore = saveHighScore(score);

  // Draw game over screen
  drawGameOver(isHighScore);
}

// Draw everything
function draw() {
  // Clear canvas
  ctx.fillStyle = '#F1EFE2';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // Draw snake
  for (let i = 0; i < snake.length; i++) {
    // Head is a different color
    if (i === 0) {
      ctx.fillStyle = '#0A246A';
    } else {
      ctx.fillStyle = '#3A6EA5';
    }

    ctx.fillRect(
      snake[i].x * gridSize,
      snake[i].y * gridSize,
      gridSize - 1,
      gridSize - 1
    );
  }

  // Draw food
  ctx.fillStyle = '#CC0000';
  ctx.fillRect(
    food.x * gridSize,
    food.y * gridSize,
    gridSize - 1,
    gridSize - 1
  );

  // Draw grid (optional)
  drawGrid();
}

// Draw grid - cached for better performance
let gridCache;

function drawGrid() {
  // Create cached grid if it doesn't exist
  if (!gridCache) {
    gridCache = document.createElement('canvas');
    gridCache.width = canvasSize;
    gridCache.height = canvasSize;
    const gridCtx = gridCache.getContext('2d');

    gridCtx.strokeStyle = '#E5E5E5';
    gridCtx.lineWidth = 0.5;

    // Draw vertical lines
    for (let x = 0; x <= canvasSize; x += gridSize) {
      gridCtx.beginPath();
      gridCtx.moveTo(x, 0);
      gridCtx.lineTo(x, canvasSize);
      gridCtx.stroke();
    }

    // Draw horizontal lines
    for (let y = 0; y <= canvasSize; y += gridSize) {
      gridCtx.beginPath();
      gridCtx.moveTo(0, y);
      gridCtx.lineTo(canvasSize, y);
      gridCtx.stroke();
    }
  }

  // Draw the cached grid
  ctx.drawImage(gridCache, 0, 0);
}

// Draw game over screen
function drawGameOver(isHighScore) {
  // Semi-transparent overlay
  ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // Game over text
  ctx.fillStyle = '#FFFFFF';
  ctx.font = 'bold 30px Tahoma';
  ctx.textAlign = 'center';
  ctx.fillText('Game Over', canvas.width / 2, canvas.height / 2 - 30);

  // Score text
  ctx.font = '20px Tahoma';
  ctx.fillText(`Score: ${score}`, canvas.width / 2, canvas.height / 2 + 10);

  // High score text
  if (isHighScore) {
    ctx.fillStyle = '#FFFF00';
    ctx.fillText('New High Score!', canvas.width / 2, canvas.height / 2 + 40);
  }

  // Restart instruction
  ctx.fillStyle = '#FFFFFF';
  ctx.font = '16px Tahoma';
  ctx.fillText('Press the Restart button to play again', canvas.width / 2, canvas.height / 2 + 70);
}

// Change direction based on key press
function changeDirection(event) {
  // Ignore if game is not running
  if (!gameRunning) return;

  // Get key code
  const key = event.key;

  // Prevent reversing direction
  if (key === 'ArrowUp' && direction !== 'down') {
    newDirection = 'up';
  } else if (key === 'ArrowDown' && direction !== 'up') {
    newDirection = 'down';
  } else if (key === 'ArrowLeft' && direction !== 'right') {
    newDirection = 'left';
  } else if (key === 'ArrowRight' && direction !== 'left') {
    newDirection = 'right';
  }

  // Prevent default behavior for arrow keys (scrolling)
  if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
    event.preventDefault();
  }
}

// Update score display
function updateScore(newScore) {
  score = newScore;
  const scoreElement = document.getElementById('current-score');
  if (scoreElement) {
    scoreElement.textContent = score;
  }
}

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  initSnakeGame();
});
