// Shopping Page Functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the shopping page
  initShoppingPage();
});

// Cart data structure
let cart = {
  items: [],
  total: 0
};

// Product data
const products = [
  // Electronics
  {
    id: 'e1',
    name: 'TechPro Laptop X5',
    price: 45999,
    rating: 4.5,
    category: 'electronics',
    image: '../img/products/laptop.svg',
    description: 'Powerful laptop with 16GB RAM and 512GB SSD'
  },
  {
    id: 'e2',
    name: 'SoundWave Wireless Headphones',
    price: 2499,
    rating: 4.3,
    category: 'electronics',
    image: '../img/products/headphones.svg',
    description: 'Premium wireless headphones with noise cancellation'
  },
  {
    id: 'e3',
    name: 'PixelMax Smartphone',
    price: 29999,
    rating: 4.7,
    category: 'electronics',
    image: '../img/products/smartphone.svg',
    description: 'Latest smartphone with 108MP camera and 5G'
  },
  {
    id: 'e4',
    name: 'SmartView 4K TV',
    price: 35999,
    rating: 4.4,
    category: 'electronics',
    image: '../img/products/tv.svg',
    description: '55-inch 4K Smart TV with HDR'
  },
  {
    id: 'e5',
    name: 'PowerTab Pro Tablet',
    price: 18999,
    rating: 4.6,
    category: 'electronics',
    image: '../img/products/laptop.svg',
    description: '10-inch tablet with stylus support and 128GB storage'
  },
  {
    id: 'e6',
    name: 'GameStation 5',
    price: 39999,
    rating: 4.9,
    category: 'electronics',
    image: '../img/products/tv.svg',
    description: 'Next-gen gaming console with 1TB SSD and 4K gaming'
  },
  {
    id: 'e7',
    name: 'SmartWatch Elite',
    price: 12999,
    rating: 4.2,
    category: 'electronics',
    image: '../img/products/smartphone.svg',
    description: 'Fitness tracker with heart rate monitor and GPS'
  },
  {
    id: 'e8',
    name: 'AudioPro Bluetooth Speaker',
    price: 4999,
    rating: 4.5,
    category: 'electronics',
    image: '../img/products/headphones.svg',
    description: 'Portable speaker with 20-hour battery life and water resistance'
  },
  {
    id: 'e9',
    name: 'PhotoMaster DSLR Camera',
    price: 58999,
    rating: 4.8,
    category: 'electronics',
    image: '../img/products/smartphone.svg',
    description: '24MP DSLR camera with 4K video recording and Wi-Fi'
  },

  // Clothing
  {
    id: 'c1',
    name: 'Classic Denim Jacket',
    price: 1999,
    rating: 4.2,
    category: 'clothing',
    image: '../img/products/jacket.svg',
    description: 'Stylish denim jacket for all seasons'
  },
  {
    id: 'c2',
    name: 'Comfort Fit T-Shirt',
    price: 599,
    rating: 4.0,
    category: 'clothing',
    image: '../img/products/tshirt.svg',
    description: 'Cotton t-shirt with comfortable fit'
  },
  {
    id: 'c3',
    name: 'Premium Leather Shoes',
    price: 2999,
    rating: 4.6,
    category: 'clothing',
    image: '../img/products/shoes.svg',
    description: 'Genuine leather shoes with durable sole'
  },
  {
    id: 'c4',
    name: 'Designer Silk Saree',
    price: 4999,
    rating: 4.8,
    category: 'clothing',
    image: '../img/products/saree.svg',
    description: 'Elegant silk saree with intricate designs'
  },
  {
    id: 'c5',
    name: 'Formal Business Suit',
    price: 7999,
    rating: 4.7,
    category: 'clothing',
    image: '../img/products/jacket.svg',
    description: 'Premium tailored suit for professional settings'
  },
  {
    id: 'c6',
    name: 'Winter Parka Jacket',
    price: 3499,
    rating: 4.5,
    category: 'clothing',
    image: '../img/products/jacket.svg',
    description: 'Insulated winter jacket with fur-lined hood'
  },
  {
    id: 'c7',
    name: 'Designer Handbag',
    price: 2499,
    rating: 4.3,
    category: 'clothing',
    image: '../img/products/tshirt.svg',
    description: 'Stylish handbag with multiple compartments'
  },
  {
    id: 'c8',
    name: 'Running Shoes',
    price: 3299,
    rating: 4.6,
    category: 'clothing',
    image: '../img/products/shoes.svg',
    description: 'Lightweight running shoes with cushioned soles'
  },
  {
    id: 'c9',
    name: 'Traditional Kurta Set',
    price: 2799,
    rating: 4.4,
    category: 'clothing',
    image: '../img/products/saree.svg',
    description: 'Embroidered kurta set for festive occasions'
  },

  // Home & Kitchen
  {
    id: 'h1',
    name: 'Non-Stick Cookware Set',
    price: 3499,
    rating: 4.3,
    category: 'home',
    image: '../img/products/cookware.svg',
    description: 'Complete set of non-stick cookware for your kitchen'
  },
  {
    id: 'h2',
    name: 'Smart Home Assistant',
    price: 4999,
    rating: 4.5,
    category: 'home',
    image: '../img/products/assistant.svg',
    description: 'Voice-controlled smart home assistant'
  },
  {
    id: 'h3',
    name: 'Luxury Bedding Set',
    price: 2999,
    rating: 4.4,
    category: 'home',
    image: '../img/products/bedding.svg',
    description: 'Premium cotton bedding set with 4 pillowcases'
  },
  {
    id: 'h4',
    name: 'Automatic Coffee Maker',
    price: 5999,
    rating: 4.7,
    category: 'home',
    image: '../img/products/coffeemaker.svg',
    description: 'Programmable coffee maker with built-in grinder'
  },
  {
    id: 'h5',
    name: 'Robot Vacuum Cleaner',
    price: 14999,
    rating: 4.6,
    category: 'home',
    image: '../img/products/assistant.svg',
    description: 'Smart robot vacuum with mapping technology'
  },
  {
    id: 'h6',
    name: 'Air Purifier',
    price: 8999,
    rating: 4.5,
    category: 'home',
    image: '../img/products/assistant.svg',
    description: 'HEPA air purifier for cleaner indoor air'
  },
  {
    id: 'h7',
    name: 'Ergonomic Office Chair',
    price: 7499,
    rating: 4.4,
    category: 'home',
    image: '../img/products/bedding.svg',
    description: 'Adjustable office chair with lumbar support'
  },
  {
    id: 'h8',
    name: 'Stainless Steel Knife Set',
    price: 2499,
    rating: 4.8,
    category: 'home',
    image: '../img/products/cookware.svg',
    description: 'Professional-grade knife set with wooden block'
  },
  {
    id: 'h9',
    name: 'Smart LED Lighting Kit',
    price: 3999,
    rating: 4.3,
    category: 'home',
    image: '../img/products/coffeemaker.svg',
    description: 'Color-changing smart lights with app control'
  },

  // Books
  {
    id: 'b1',
    name: 'The Hidden Mystery',
    price: 399,
    rating: 4.6,
    category: 'books',
    image: '../img/products/book1.svg',
    description: 'Bestselling mystery novel by renowned author'
  },
  {
    id: 'b2',
    name: 'Cooking Masterclass',
    price: 799,
    rating: 4.8,
    category: 'books',
    image: '../img/products/book2.svg',
    description: 'Comprehensive cookbook with 500+ recipes'
  },
  {
    id: 'b3',
    name: 'Financial Freedom Guide',
    price: 499,
    rating: 4.5,
    category: 'books',
    image: '../img/products/book3.svg',
    description: 'Step-by-step guide to achieving financial independence'
  },
  {
    id: 'b4',
    name: 'Science for Beginners',
    price: 349,
    rating: 4.3,
    category: 'books',
    image: '../img/products/book4.svg',
    description: 'Illustrated science book for all ages'
  },
  {
    id: 'b5',
    name: 'The Art of Meditation',
    price: 450,
    rating: 4.7,
    category: 'books',
    image: '../img/products/book1.svg',
    description: 'Guide to mindfulness and meditation practices'
  },
  {
    id: 'b6',
    name: 'World History Encyclopedia',
    price: 1299,
    rating: 4.9,
    category: 'books',
    image: '../img/products/book2.svg',
    description: 'Comprehensive history book with illustrations'
  },
  {
    id: 'b7',
    name: 'The Future of Technology',
    price: 599,
    rating: 4.4,
    category: 'books',
    image: '../img/products/book3.svg',
    description: 'Insights into emerging technologies and trends'
  },
  {
    id: 'b8',
    name: 'Classic Literature Collection',
    price: 899,
    rating: 4.6,
    category: 'books',
    image: '../img/products/book4.svg',
    description: 'Set of 5 classic novels in hardcover'
  },
  {
    id: 'b9',
    name: 'Photography Essentials',
    price: 699,
    rating: 4.5,
    category: 'books',
    image: '../img/products/book1.svg',
    description: 'Guide to mastering photography techniques'
  },

  // Toys & Games
  {
    id: 't1',
    name: 'Building Blocks Set',
    price: 1299,
    rating: 4.7,
    category: 'toys',
    image: '../img/products/blocks.svg',
    description: '250-piece building blocks set for creative play'
  },
  {
    id: 't2',
    name: 'Remote Control Car',
    price: 1999,
    rating: 4.4,
    category: 'toys',
    image: '../img/products/car.svg',
    description: 'High-speed remote control car with rechargeable battery'
  },
  {
    id: 't3',
    name: 'Strategy Board Game',
    price: 899,
    rating: 4.6,
    category: 'toys',
    image: '../img/products/boardgame.svg',
    description: 'Classic strategy board game for family fun'
  },
  {
    id: 't4',
    name: 'Plush Teddy Bear',
    price: 699,
    rating: 4.5,
    category: 'toys',
    image: '../img/products/teddybear.svg',
    description: 'Soft and cuddly teddy bear, perfect for all ages'
  },
  {
    id: 't5',
    name: 'Educational Science Kit',
    price: 1499,
    rating: 4.8,
    category: 'toys',
    image: '../img/products/blocks.svg',
    description: 'Hands-on science experiments for kids'
  },
  {
    id: 't6',
    name: 'Drone with Camera',
    price: 5999,
    rating: 4.3,
    category: 'toys',
    image: '../img/products/car.svg',
    description: 'Mini drone with HD camera and easy controls'
  },
  {
    id: 't7',
    name: 'Puzzle Set (1000 pieces)',
    price: 799,
    rating: 4.5,
    category: 'toys',
    image: '../img/products/boardgame.svg',
    description: 'Challenging puzzle with beautiful landscape image'
  },
  {
    id: 't8',
    name: 'Art and Craft Kit',
    price: 899,
    rating: 4.7,
    category: 'toys',
    image: '../img/products/blocks.svg',
    description: 'Complete art supplies kit for creative children'
  },
  {
    id: 't9',
    name: 'Interactive Robot Toy',
    price: 2499,
    rating: 4.6,
    category: 'toys',
    image: '../img/products/teddybear.svg',
    description: 'Programmable robot that responds to voice commands'
  },

  // Sports & Fitness
  {
    id: 's1',
    name: 'Yoga Mat Premium',
    price: 1299,
    rating: 4.7,
    category: 'sports',
    image: '../img/products/bedding.svg',
    description: 'Non-slip yoga mat with carrying strap'
  },
  {
    id: 's2',
    name: 'Adjustable Dumbbells Set',
    price: 4999,
    rating: 4.8,
    category: 'sports',
    image: '../img/products/cookware.svg',
    description: 'Space-saving adjustable weights from 2-20kg'
  },
  {
    id: 's3',
    name: 'Basketball (Official Size)',
    price: 899,
    rating: 4.5,
    category: 'sports',
    image: '../img/products/teddybear.svg',
    description: 'Professional basketball with superior grip'
  },
  {
    id: 's4',
    name: 'Tennis Racket Pro',
    price: 2499,
    rating: 4.6,
    category: 'sports',
    image: '../img/products/car.svg',
    description: 'Lightweight tennis racket with carbon fiber frame'
  },
  {
    id: 's5',
    name: 'Fitness Tracker Band',
    price: 1999,
    rating: 4.4,
    category: 'sports',
    image: '../img/products/smartphone.svg',
    description: 'Waterproof fitness tracker with heart rate monitor'
  },
  {
    id: 's6',
    name: 'Camping Tent (4-Person)',
    price: 3999,
    rating: 4.3,
    category: 'sports',
    image: '../img/products/bedding.svg',
    description: 'Waterproof tent with easy setup mechanism'
  },
  {
    id: 's7',
    name: 'Mountain Bike',
    price: 12999,
    rating: 4.7,
    category: 'sports',
    image: '../img/products/car.svg',
    description: '21-speed mountain bike with front suspension'
  },
  {
    id: 's8',
    name: 'Swimming Goggles',
    price: 599,
    rating: 4.2,
    category: 'sports',
    image: '../img/products/headphones.svg',
    description: 'Anti-fog swimming goggles with UV protection'
  },
  {
    id: 's9',
    name: 'Cricket Bat Set',
    price: 1899,
    rating: 4.5,
    category: 'sports',
    image: '../img/products/car.svg',
    description: 'Professional cricket bat with ball and stumps'
  },

  // Beauty & Personal Care
  {
    id: 'p1',
    name: 'Skincare Gift Set',
    price: 1499,
    rating: 4.8,
    category: 'beauty',
    image: '../img/products/coffeemaker.svg',
    description: 'Complete skincare routine with natural ingredients'
  },
  {
    id: 'p2',
    name: 'Hair Dryer Professional',
    price: 1999,
    rating: 4.6,
    category: 'beauty',
    image: '../img/products/assistant.svg',
    description: 'Salon-quality hair dryer with multiple heat settings'
  },
  {
    id: 'p3',
    name: 'Electric Shaver',
    price: 2499,
    rating: 4.5,
    category: 'beauty',
    image: '../img/products/smartphone.svg',
    description: 'Waterproof electric shaver with precision trimmer'
  },
  {
    id: 'p4',
    name: 'Makeup Brush Set',
    price: 999,
    rating: 4.7,
    category: 'beauty',
    image: '../img/products/blocks.svg',
    description: '12-piece professional makeup brush set with case'
  },
  {
    id: 'p5',
    name: 'Perfume Collection',
    price: 3499,
    rating: 4.9,
    category: 'beauty',
    image: '../img/products/coffeemaker.svg',
    description: 'Set of 3 luxury fragrances for all occasions'
  },
  {
    id: 'p6',
    name: 'Facial Cleansing Device',
    price: 1799,
    rating: 4.4,
    category: 'beauty',
    image: '../img/products/assistant.svg',
    description: 'Electric facial cleansing brush with multiple modes'
  },
  {
    id: 'p7',
    name: 'Manicure & Pedicure Kit',
    price: 899,
    rating: 4.3,
    category: 'beauty',
    image: '../img/products/blocks.svg',
    description: 'Complete nail care set with 15 tools'
  },
  {
    id: 'p8',
    name: 'Hair Straightener',
    price: 1599,
    rating: 4.6,
    category: 'beauty',
    image: '../img/products/assistant.svg',
    description: 'Ceramic hair straightener with temperature control'
  },
  {
    id: 'p9',
    name: 'Luxury Bath Set',
    price: 1299,
    rating: 4.7,
    category: 'beauty',
    image: '../img/products/coffeemaker.svg',
    description: 'Bath bombs, soaps, and lotions gift set'
  },

  // Groceries & Gourmet
  {
    id: 'g1',
    name: 'Premium Tea Collection',
    price: 899,
    rating: 4.7,
    category: 'groceries',
    image: '../img/products/coffeemaker.svg',
    description: 'Assortment of 10 exotic tea varieties'
  },
  {
    id: 'g2',
    name: 'Organic Honey Set',
    price: 1299,
    rating: 4.8,
    category: 'groceries',
    image: '../img/products/cookware.svg',
    description: 'Set of 3 organic honey varieties from different regions'
  },
  {
    id: 'g3',
    name: 'Gourmet Chocolate Box',
    price: 999,
    rating: 4.9,
    category: 'groceries',
    image: '../img/products/book2.svg',
    description: 'Assorted premium chocolates in elegant gift box'
  },
  {
    id: 'g4',
    name: 'Exotic Spice Collection',
    price: 799,
    rating: 4.6,
    category: 'groceries',
    image: '../img/products/cookware.svg',
    description: '12 essential spices in glass jars with wooden rack'
  },
  {
    id: 'g5',
    name: 'Cold-Pressed Olive Oil',
    price: 699,
    rating: 4.5,
    category: 'groceries',
    image: '../img/products/coffeemaker.svg',
    description: 'Extra virgin olive oil from Mediterranean olives'
  },
  {
    id: 'g6',
    name: 'Artisanal Cheese Selection',
    price: 1499,
    rating: 4.7,
    category: 'groceries',
    image: '../img/products/cookware.svg',
    description: 'Selection of 5 premium cheeses with crackers'
  },
  {
    id: 'g7',
    name: 'Dry Fruits Gift Box',
    price: 1199,
    rating: 4.6,
    category: 'groceries',
    image: '../img/products/book2.svg',
    description: 'Assorted premium dry fruits in wooden gift box'
  },
  {
    id: 'g8',
    name: 'Specialty Coffee Beans',
    price: 599,
    rating: 4.8,
    category: 'groceries',
    image: '../img/products/coffeemaker.svg',
    description: 'Freshly roasted single-origin coffee beans (250g)'
  },
  {
    id: 'g9',
    name: 'Organic Fruit Basket',
    price: 899,
    rating: 4.4,
    category: 'groceries',
    image: '../img/products/book2.svg',
    description: 'Seasonal organic fruits in handcrafted basket'
  }
];

function initShoppingPage() {
  // Display products
  displayProducts('all');

  // Initialize category navigation
  initCategoryNav();

  // Initialize sort functionality
  initSortFunctionality();

  // Initialize cart functionality
  initCartFunctionality();

  // Update status bar
  updateStatusBar('Shopping page loaded', 'Connected');
}

// Display products based on category
function displayProducts(category) {
  const productsGrid = document.getElementById('products-grid');

  // Clear existing products
  productsGrid.innerHTML = '';

  // Filter products by category
  let filteredProducts = products;
  if (category !== 'all') {
    filteredProducts = products.filter(product => product.category === category);
  }

  // Display products
  filteredProducts.forEach(product => {
    const productCard = createProductCard(product);
    productsGrid.appendChild(productCard);
  });

  // Update products header
  updateProductsHeader(category);
}

// Create product card element
function createProductCard(product) {
  const productCard = document.createElement('div');
  productCard.className = 'product-card';
  productCard.dataset.id = product.id;

  // Generate star rating HTML
  const ratingStars = generateRatingStars(product.rating);

  productCard.innerHTML = `
    <div class="product-image">
      <img src="${product.image}" alt="${product.name}">
    </div>
    <div class="product-details">
      <div class="product-name">${product.name}</div>
      <div class="product-price">₹${product.price.toFixed(2)}</div>
      <div class="product-rating">${ratingStars}</div>
      <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
    </div>
  `;

  return productCard;
}

// Generate star rating HTML
function generateRatingStars(rating) {
  const fullStars = Math.floor(rating);
  const halfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

  let starsHTML = '';

  // Add full stars
  for (let i = 0; i < fullStars; i++) {
    starsHTML += '★';
  }

  // Add half star if needed
  if (halfStar) {
    starsHTML += '★';
  }

  // Add empty stars
  for (let i = 0; i < emptyStars; i++) {
    starsHTML += '☆';
  }

  return starsHTML;
}

// Update products header based on category
function updateProductsHeader(category) {
  const productsHeader = document.querySelector('.products-header h2');

  let categoryName = 'All Products';

  switch(category) {
    case 'electronics':
      categoryName = 'Electronics';
      break;
    case 'clothing':
      categoryName = 'Clothing';
      break;
    case 'home':
      categoryName = 'Home & Kitchen';
      break;
    case 'books':
      categoryName = 'Books';
      break;
    case 'toys':
      categoryName = 'Toys & Games';
      break;
    case 'sports':
      categoryName = 'Sports & Fitness';
      break;
    case 'beauty':
      categoryName = 'Beauty & Personal Care';
      break;
    case 'groceries':
      categoryName = 'Groceries & Gourmet';
      break;
  }

  productsHeader.textContent = categoryName;
}

// Initialize category navigation
function initCategoryNav() {
  const categoryItems = document.querySelectorAll('.category-item');

  categoryItems.forEach(item => {
    item.addEventListener('click', function() {
      // Remove active class from all items
      categoryItems.forEach(item => item.classList.remove('active'));

      // Add active class to clicked item
      this.classList.add('active');

      // Get category and display products
      const category = this.getAttribute('data-category');
      displayProducts(category);

      // Update status bar
      updateStatusBar(`Browsing ${category} products`, 'Connected');
    });
  });
}

// Initialize sort functionality
function initSortFunctionality() {
  const sortSelect = document.getElementById('sort-select');

  sortSelect.addEventListener('change', function() {
    const sortValue = this.value;
    const activeCategory = document.querySelector('.category-item.active').getAttribute('data-category');

    // Sort products and redisplay
    sortAndDisplayProducts(activeCategory, sortValue);

    // Update status bar
    updateStatusBar(`Sorted products by ${sortValue}`, 'Connected');
  });
}

// Sort and display products
function sortAndDisplayProducts(category, sortBy) {
  const productsGrid = document.getElementById('products-grid');

  // Clear existing products
  productsGrid.innerHTML = '';

  // Filter products by category
  let filteredProducts = products;
  if (category !== 'all') {
    filteredProducts = products.filter(product => product.category === category);
  }

  // Sort products
  switch(sortBy) {
    case 'price-low':
      filteredProducts.sort((a, b) => a.price - b.price);
      break;
    case 'price-high':
      filteredProducts.sort((a, b) => b.price - a.price);
      break;
    case 'rating':
      filteredProducts.sort((a, b) => b.rating - a.rating);
      break;
    default:
      // 'featured' - no sorting needed
      break;
  }

  // Display products
  filteredProducts.forEach(product => {
    const productCard = createProductCard(product);
    productsGrid.appendChild(productCard);
  });
}

// Initialize cart functionality
function initCartFunctionality() {
  // Add event delegation for "Add to Cart" buttons
  document.addEventListener('click', function(event) {
    if (event.target.classList.contains('add-to-cart')) {
      const productId = event.target.getAttribute('data-id');
      addToCart(productId);
    }
  });

  // Cart icon click event
  const cartIcon = document.getElementById('cart-icon');
  const cartPopup = document.getElementById('cart-popup');
  const overlay = document.getElementById('overlay');

  cartIcon.addEventListener('click', function() {
    cartPopup.style.display = 'flex';
    overlay.style.display = 'block';
  });

  // Close cart button
  const closeCart = document.getElementById('close-cart');
  closeCart.addEventListener('click', function() {
    cartPopup.style.display = 'none';
    overlay.style.display = 'none';
  });

  // Overlay click to close cart
  overlay.addEventListener('click', function() {
    cartPopup.style.display = 'none';
    overlay.style.display = 'none';
  });

  // Checkout button
  const checkoutButton = document.getElementById('checkout-button');
  checkoutButton.addEventListener('click', function() {
    if (cart.items.length === 0) {
      alert('Your cart is empty. Add some products first!');
      return;
    }

    // Store cart data in localStorage
    const paymentDetails = {
      shopping: {
        items: cart.items.map(item => ({
          name: item.name,
          price: item.price,
          quantity: item.quantity
        })),
        subtotal: cart.total,
        shipping: 100,
        tax: Math.round(cart.total * 0.05),
        total: cart.total + 100 + Math.round(cart.total * 0.05)
      }
    };

    localStorage.setItem('paymentType', 'shopping');
    localStorage.setItem('paymentDetails', JSON.stringify(paymentDetails));

    // Navigate to payment page
    window.location.href = 'payment.html';
  });
}

// Add product to cart
function addToCart(productId) {
  // Find product
  const product = products.find(p => p.id === productId);

  if (!product) return;

  // Check if product is already in cart
  const existingItem = cart.items.find(item => item.id === productId);

  if (existingItem) {
    // Increment quantity
    existingItem.quantity += 1;
    existingItem.total = existingItem.price * existingItem.quantity;
  } else {
    // Add new item to cart
    cart.items.push({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1,
      total: product.price
    });
  }

  // Update cart total
  updateCartTotal();

  // Update cart UI
  updateCartUI();

  // Update status bar
  updateStatusBar(`Added ${product.name} to cart`, 'Connected');
}

// Update cart total
function updateCartTotal() {
  cart.total = cart.items.reduce((total, item) => total + item.total, 0);
}

// Update cart UI
function updateCartUI() {
  // Update cart count
  const cartCount = document.getElementById('cart-count');
  const itemCount = cart.items.reduce((count, item) => count + item.quantity, 0);
  cartCount.textContent = itemCount;

  // Update cart items
  const cartItems = document.getElementById('cart-items');

  if (cart.items.length === 0) {
    cartItems.innerHTML = '<div class="empty-cart-message">Your cart is empty</div>';
  } else {
    cartItems.innerHTML = '';

    cart.items.forEach(item => {
      const cartItem = document.createElement('div');
      cartItem.className = 'cart-item';

      cartItem.innerHTML = `
        <div class="cart-item-image">
          <img src="${item.image}" alt="${item.name}">
        </div>
        <div class="cart-item-details">
          <div class="cart-item-name">${item.name}</div>
          <div class="cart-item-price">₹${item.price.toFixed(2)}</div>
          <div class="cart-item-quantity">
            <button class="quantity-btn minus" data-id="${item.id}">-</button>
            <span>${item.quantity}</span>
            <button class="quantity-btn plus" data-id="${item.id}">+</button>
          </div>
        </div>
        <div class="cart-item-remove" data-id="${item.id}">×</div>
      `;

      cartItems.appendChild(cartItem);
    });

    // Add event listeners for quantity buttons and remove buttons
    addCartItemEventListeners();
  }

  // Update cart total
  const cartTotalAmount = document.getElementById('cart-total-amount');
  cartTotalAmount.textContent = `₹${cart.total.toFixed(2)}`;
}

// Add event listeners for cart item buttons
function addCartItemEventListeners() {
  // Quantity minus buttons
  const minusButtons = document.querySelectorAll('.quantity-btn.minus');
  minusButtons.forEach(button => {
    button.addEventListener('click', function() {
      const itemId = this.getAttribute('data-id');
      decrementItemQuantity(itemId);
    });
  });

  // Quantity plus buttons
  const plusButtons = document.querySelectorAll('.quantity-btn.plus');
  plusButtons.forEach(button => {
    button.addEventListener('click', function() {
      const itemId = this.getAttribute('data-id');
      incrementItemQuantity(itemId);
    });
  });

  // Remove buttons
  const removeButtons = document.querySelectorAll('.cart-item-remove');
  removeButtons.forEach(button => {
    button.addEventListener('click', function() {
      const itemId = this.getAttribute('data-id');
      removeItemFromCart(itemId);
    });
  });
}

// Decrement item quantity
function decrementItemQuantity(itemId) {
  const item = cart.items.find(item => item.id === itemId);

  if (item) {
    if (item.quantity > 1) {
      item.quantity -= 1;
      item.total = item.price * item.quantity;
    } else {
      // Remove item if quantity becomes 0
      removeItemFromCart(itemId);
      return;
    }

    // Update cart total
    updateCartTotal();

    // Update cart UI
    updateCartUI();
  }
}

// Increment item quantity
function incrementItemQuantity(itemId) {
  const item = cart.items.find(item => item.id === itemId);

  if (item) {
    item.quantity += 1;
    item.total = item.price * item.quantity;

    // Update cart total
    updateCartTotal();

    // Update cart UI
    updateCartUI();
  }
}

// Remove item from cart
function removeItemFromCart(itemId) {
  cart.items = cart.items.filter(item => item.id !== itemId);

  // Update cart total
  updateCartTotal();

  // Update cart UI
  updateCartUI();
}
