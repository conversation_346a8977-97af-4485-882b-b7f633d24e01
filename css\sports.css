/* Sports Page Styles */
.sports-page {
  font-family: '<PERSON><PERSON><PERSON>', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 30px;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.sports-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 25px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.sports-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.sports-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 15px;
  min-height: 0; /* Ensures flex items can shrink below their content size */
  height: 100%; /* Fill available height */
}

/* Sports Categories Styles */
.sports-categories {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 8px;
  margin-bottom: 15px;
  display: flex;
  flex-wrap: wrap;
}

.category-item {
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  border-radius: 3px;
  border: 1px solid #ACA899;
  transition: all 0.2s ease;
  background-color: #F1EFE2;
  display: inline-block;
  margin-right: 3px;
}

.category-item:hover {
  background-color: #E5F1FB;
  border-color: #A6CAF0;
}

.category-item.active {
  background-color: #0A246A;
  color: white;
  font-weight: bold;
  border-color: #0A246A;
}

/* Sports Sections Styles */
.sports-section {
  display: none;
  flex-direction: column;
  gap: 25px;
  width: 100%;
}

.sports-section.active {
  display: flex;
}

.sports-section h2 {
  color: #0A246A;
  font-size: 20px;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #0A246A;
}

.sports-section h3 {
  color: #0A246A;
  font-size: 16px;
  margin: 0 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #ACA899;
}

/* Live Matches, Recent Results, Upcoming Matches Styles */
.live-matches, .recent-results, .upcoming-matches {
  margin-bottom: 25px;
}

.match-card {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 12px;
  margin-bottom: 15px;
  transition: all 0.2s ease;
}

.match-card:hover {
  background-color: #F1EFE2;
  border-color: #0A246A;
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.match-tournament {
  font-size: 12px;
  color: #666666;
  font-weight: bold;
}

.match-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
}

.match-status.live {
  background-color: #CC0000;
  color: white;
}

.match-status.upcoming {
  background-color: #0A246A;
  color: white;
}

.match-status.completed {
  background-color: #008800;
  color: white;
}

.match-teams {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.team {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 45%;
}



.vs {
  font-size: 14px;
  font-weight: bold;
  color: #666666;
}

.team-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #0A246A;
}

.team-score {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.match-info {
  font-size: 12px;
  color: #333333;
  text-align: center;
  padding-top: 10px;
  border-top: 1px dashed #ACA899;
}

/* Sports News Styles */
.sports-news {
  margin-bottom: 25px;
}

.news-grid {
  display: flex;
  flex-direction: column;
}

.news-item {
  display: flex;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 3px;
  overflow: hidden;
  transition: all 0.2s ease;
  margin-bottom: 10px;
}

.news-item:hover {
  background-color: #F1EFE2;
  border-color: #0A246A;
}



.news-content {
  padding: 12px;
  flex: 1;
}

.news-content h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #0A246A;
}

.news-content p {
  font-size: 12px;
  line-height: 1.4;
  color: #333333;
  margin: 0 0 8px 0;
}

.read-more {
  font-size: 12px;
  color: #0A246A;
  text-decoration: none;
  cursor: pointer;
}

.read-more:hover {
  text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .match-card {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .sports-content {
    flex-direction: column;
  }

  .sports-categories {
    overflow-x: auto;
    flex-wrap: nowrap;
    padding: 10px;
    margin-bottom: 15px;
  }

  .category-item {
    white-space: nowrap;
  }

  .news-item {
    flex-direction: column;
  }

  .stats-container {
    flex-direction: column;
    gap: 15px;
  }

  .stats-table {
    font-size: 11px;
  }

  .stats-table th, .stats-table td {
    padding: 4px 6px;
  }
}

@media (max-width: 480px) {
  .sports-page {
    padding: 15px;
  }

  .match-teams {
    flex-direction: column;
    gap: 10px;
  }

  .team {
    width: 100%;
  }

  .vs {
    margin: 5px 0;
  }
}


.current-sport {
  margin-bottom: 10px;
}

.current-sport h1 {
  color: #0A246A;
  font-size: 24px;
  margin: 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #0A246A;
  text-transform: capitalize;
}

.sports-main h2 {
  color: #0A246A;
  font-size: 18px;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ACA899;
}

.match-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.match-type {
  font-size: 12px;
  color: #666666;
  font-weight: bold;
}

.team-overs {
  font-size: 11px;
  color: #666666;
  margin-bottom: 3px;
}

/* Upcoming Matches Styles */
.match-table-container {
  overflow-x: auto;
}

.match-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.match-table th {
  background-color: #E1E1E1;
  border: 1px solid #ACA899;
  padding: 8px 12px;
  text-align: left;
  font-weight: bold;
  color: #0A246A;
}

.match-table td {
  border: 1px solid #ACA899;
  padding: 8px 12px;
}

.match-table tr:nth-child(even) {
  background-color: #F1EFE2;
}

.match-table tr:hover {
  background-color: #E5F1FB;
}

/* Sports News Styles */
.sports-news {
  margin-bottom: 25px;
}

.news-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.news-item {
  display: flex;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.news-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.news-image {
  width: 150px;
  min-width: 150px;
  height: 120px;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content {
  padding: 15px;
  flex: 1;
}

.news-content h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #0A246A;
}

.news-date {
  font-size: 11px;
  color: #666666;
  margin-bottom: 8px;
}

.news-content p {
  font-size: 12px;
  line-height: 1.4;
  color: #333333;
  margin: 0 0 8px 0;
}

.read-more {
  font-size: 12px;
  color: #0A246A;
  text-decoration: none;
  cursor: pointer;
}

.read-more:hover {
  text-decoration: underline;
}

/* Player Stats Styles */
.stats-tabs {
  display: flex;
  border-bottom: 1px solid #ACA899;
  margin-bottom: 15px;
}

.stats-tab {
  padding: 8px 15px;
  font-size: 12px;
  cursor: pointer;
  border: 1px solid transparent;
  border-bottom: none;
  margin-right: 5px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.stats-tab.active {
  background-color: #FFFFFF;
  border-color: #ACA899;
  border-bottom: 1px solid #FFFFFF;
  margin-bottom: -1px;
  font-weight: bold;
}

.stats-tab:not(.active):hover {
  background-color: #F1EFE2;
}

.stats-content {
  display: none;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
}

.stats-content.active {
  display: block;
}

.stats-category {
  margin-bottom: 20px;
}

.stats-category h3 {
  color: #0A246A;
  font-size: 14px;
  margin: 0 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px dashed #ACA899;
}

.stats-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.stats-table th {
  background-color: #E1E1E1;
  border: 1px solid #ACA899;
  padding: 6px 10px;
  text-align: left;
  font-weight: bold;
  color: #0A246A;
}

.stats-table td {
  border: 1px solid #ACA899;
  padding: 6px 10px;
}

.stats-table tr:nth-child(even) {
  background-color: #F1EFE2;
}

.stats-table tr:hover {
  background-color: #E5F1FB;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .match-card {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .sports-content {
    flex-direction: column;
  }

  .sports-categories {
    overflow-x: auto;
    flex-wrap: nowrap;
    padding: 10px;
    margin-bottom: 15px;
  }

  .category-item {
    white-space: nowrap;
  }

  .news-item {
    flex-direction: column;
  }

  .news-image {
    width: 100%;
    height: 180px;
  }

  .match-card {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .sports-page {
    padding: 15px;
  }

  .match-teams {
    flex-direction: column;
    gap: 10px;
  }

  .team {
    width: 100%;
  }

  .vs {
    margin: 5px 0;
  }
}

/* Quick Scores Styles */
.quick-scores {
  margin-bottom: 25px;
}

.score-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
}

.score-item {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 10px;
  transition: all 0.2s ease;
  width: calc(50% - 5px);
}

.score-item:hover {
  background-color: #F1EFE2;
  border-color: #0A246A;
}

.score-teams {
  font-weight: bold;
  color: #0A246A;
  margin-bottom: 5px;
  font-size: 13px;
}

.score-result {
  font-size: 12px;
  margin-bottom: 3px;
}

.score-info {
  font-size: 11px;
  color: #666666;
}

/* Utility Classes */
.positive {
  color: #008800;
}

.negative {
  color: #CC0000;
}

/* Loading Spinner */
.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3A6EA5;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 40px;
}
