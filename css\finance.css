/* Finance Page Styles */
.finance-page {
  padding: 15px;
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  background-color: #ECE9D8;
  color: #333333;
  min-height: 100%;
}

/* Header Styles */
.finance-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 25px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.finance-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.finance-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

/* Market Overview Section */
.market-overview-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.market-overview-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.market-indices {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.market-index {
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  padding: 12px;
  border-radius: 3px;
  transition: background-color 0.2s;
  cursor: pointer;
}

.market-index:hover {
  background-color: #E5F1FB;
}

.index-name {
  font-size: 14px;
  font-weight: bold;
  color: #0A246A;
  margin-bottom: 5px;
}

.index-value {
  font-size: 18px;
  font-weight: bold;
}

.index-change {
  font-size: 14px;
  margin-top: 5px;
}

.positive {
  color: #008800;
}

.negative {
  color: #CC0000;
}

/* Watchlist Section */
.watchlist-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.watchlist-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.watchlist-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.refresh-button {
  background: linear-gradient(to bottom, #F0F0F0, #E1E1E1);
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
}

.refresh-button:hover {
  background: linear-gradient(to bottom, #E5F1FB, #D9E7F4);
}

.watchlist-select {
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 5px;
  font-size: 12px;
  background-color: #FFFFFF;
}

.stock-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.stock-table th {
  background-color: #E1E1E1;
  border: 1px solid #ACA899;
  padding: 8px 10px;
  text-align: left;
  font-weight: bold;
  color: #0A246A;
}

.stock-table td {
  border: 1px solid #ACA899;
  padding: 8px 10px;
}

.stock-table tr:nth-child(even) {
  background-color: #F1EFE2;
}

.stock-table tr:hover {
  background-color: #E5F1FB;
  cursor: pointer;
}

/* Currency Converter Section */
.currency-converter-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.currency-converter-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.converter-form {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 15px;
}

.converter-input-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.converter-input-group label {
  font-size: 13px;
  color: #0A246A;
}

.converter-input, .converter-select {
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 8px;
  font-size: 13px;
  background-color: #FFFFFF;
}

.convert-button {
  background: linear-gradient(to bottom, #F0F0F0, #E1E1E1);
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 8px 15px;
  font-size: 13px;
  cursor: pointer;
  margin-top: 24px;
  height: 36px;
  align-self: flex-end;
}

.convert-button:hover {
  background: linear-gradient(to bottom, #E5F1FB, #D9E7F4);
}

.conversion-result {
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 10px;
  font-size: 14px;
  min-height: 50px;
}

/* Finance News Section */
.finance-news-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.finance-news-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.news-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.news-item {
  border: 1px solid #ACA899;
  padding: 12px;
  background-color: #F1EFE2;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.news-item:hover {
  background-color: #E5F1FB;
}

.news-title {
  color: #0A246A;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}

.news-date {
  font-size: 12px;
  color: #666666;
  margin-bottom: 5px;
}

.news-summary {
  font-size: 13px;
  line-height: 1.4;
}

/* IPO Calendar Section */
.ipo-calendar-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.ipo-calendar-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.ipo-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.ipo-table th {
  background-color: #E1E1E1;
  border: 1px solid #ACA899;
  padding: 8px 10px;
  text-align: left;
  font-weight: bold;
  color: #0A246A;
}

.ipo-table td {
  border: 1px solid #ACA899;
  padding: 8px 10px;
}

.ipo-table tr:nth-child(even) {
  background-color: #F1EFE2;
}

.ipo-table tr:hover {
  background-color: #E5F1FB;
  cursor: pointer;
}

/* Market Movers Section */
.market-movers-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.market-movers-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.movers-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.top-gainers, .top-losers {
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 12px;
}

.top-gainers h3, .top-losers h3 {
  color: #0A246A;
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dashed #ACA899;
}

.movers-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.movers-table th {
  background-color: #E1E1E1;
  border: 1px solid #ACA899;
  padding: 6px 8px;
  text-align: left;
  font-weight: bold;
  color: #0A246A;
}

.movers-table td {
  border: 1px solid #ACA899;
  padding: 6px 8px;
}

.movers-table tr:nth-child(even) {
  background-color: #FFFFFF;
}

.movers-table tr:hover {
  background-color: #E5F1FB;
  cursor: pointer;
}

/* Commodity Prices Section */
.commodity-prices-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.commodity-prices-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.commodity-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.commodity-item {
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  padding: 12px;
  border-radius: 3px;
  transition: background-color 0.2s;
  cursor: pointer;
}

.commodity-item:hover {
  background-color: #E5F1FB;
}

.commodity-name {
  font-size: 14px;
  font-weight: bold;
  color: #0A246A;
  margin-bottom: 5px;
}

.commodity-price {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.commodity-change {
  font-size: 14px;
}

/* Mutual Funds Section */
.mutual-funds-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.mutual-funds-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.mutual-funds-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.mutual-funds-table th {
  background-color: #E1E1E1;
  border: 1px solid #ACA899;
  padding: 8px 10px;
  text-align: left;
  font-weight: bold;
  color: #0A246A;
}

.mutual-funds-table td {
  border: 1px solid #ACA899;
  padding: 8px 10px;
}

.mutual-funds-table tr:nth-child(even) {
  background-color: #F1EFE2;
}

.mutual-funds-table tr:hover {
  background-color: #E5F1FB;
  cursor: pointer;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .finance-content {
    grid-template-columns: 1fr;
  }

  .watchlist-section, .finance-news-section, .ipo-calendar-section,
  .market-movers-section, .mutual-funds-section, .market-overview-section,
  .currency-converter-section, .commodity-prices-section {
    grid-column: span 1;
  }

  .news-list, .commodity-list {
    grid-template-columns: 1fr;
  }

  .movers-container {
    grid-template-columns: 1fr;
  }
}
