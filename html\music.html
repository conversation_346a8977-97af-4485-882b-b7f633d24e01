<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingo Music</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/music.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="music-page">
        <div class="music-header-bar">
          <h1>Bingo Music</h1>
        </div>

        <div class="music-content">
          <!-- Left Sidebar - Playlists and Navigation -->
          <div class="music-sidebar">
            <div class="sidebar-section">
              <h3>Library</h3>
              <ul class="sidebar-menu">
                <li class="sidebar-menu-item active" data-view="home">Home</li>
                <li class="sidebar-menu-item" data-view="browse">Browse</li>
                <li class="sidebar-menu-item" data-view="radio">Radio</li>
              </ul>
            </div>

            <div class="sidebar-section">
              <h3>Your Playlists</h3>
              <ul class="sidebar-menu">
                <li class="sidebar-menu-item" data-playlist="favorites">Favorites</li>
                <li class="sidebar-menu-item" data-playlist="recently-played">Recently Played</li>
                <li class="sidebar-menu-item" data-playlist="90s-hits">90s Hits</li>
                <li class="sidebar-menu-item" data-playlist="rock-classics">Rock Classics</li>
                <li class="sidebar-menu-item" data-playlist="pop-mix">Pop Mix</li>
              </ul>
            </div>
          </div>

          <!-- Main Content Area -->
          <div class="music-main">
            <!-- Home View (default) -->
            <div class="music-view active" id="home-view">
              <div class="featured-section">
                <h2>Featured Albums</h2>
                <div class="album-grid">
                  <div class="album-card" data-album="album1">
                    <div class="album-image">
                      <img src="../img/album1.svg" alt="Album 1">
                      <div class="play-overlay">▶</div>
                    </div>
                    <div class="album-info">
                      <div class="album-title">NCS Releases</div>
                      <div class="album-artist">No Copyright Sounds</div>
                    </div>
                  </div>
                  <div class="album-card" data-album="album2">
                    <div class="album-image">
                      <img src="../img/album2.svg" alt="Album 2">
                      <div class="play-overlay">▶</div>
                    </div>
                    <div class="album-info">
                      <div class="album-title">NCS Classics</div>
                      <div class="album-artist">No Copyright Sounds</div>
                    </div>
                  </div>
                  <div class="album-card" data-album="album3">
                    <div class="album-image">
                      <img src="../img/album3.svg" alt="Album 3">
                      <div class="play-overlay">▶</div>
                    </div>
                    <div class="album-info">
                      <div class="album-title">When We All Fall Asleep...</div>
                      <div class="album-artist">Billie Eilish</div>
                    </div>
                  </div>
                  <div class="album-card" data-album="album4">
                    <div class="album-image">
                      <img src="../img/album4.svg" alt="Album 4">
                      <div class="play-overlay">▶</div>
                    </div>
                    <div class="album-info">
                      <div class="album-title">Fine Line</div>
                      <div class="album-artist">Harry Styles</div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="recently-played-section">
                <h2>Recently Played</h2>
                <div class="song-list">
                  <div class="song-item" data-song="song1">
                    <div class="song-play-button">▶</div>
                    <div class="song-info">
                      <div class="song-title">Royalty</div>
                      <div class="song-artist">No Copyright Sounds</div>
                    </div>
                    <div class="song-album">NCS Releases</div>
                    <div class="song-duration">3:30</div>
                  </div>
                  <div class="song-item" data-song="song2">
                    <div class="song-play-button">▶</div>
                    <div class="song-info">
                      <div class="song-title">Mortals</div>
                      <div class="song-artist">No Copyright Sounds</div>
                    </div>
                    <div class="song-album">NCS Releases</div>
                    <div class="song-duration">3:48</div>
                  </div>
                  <div class="song-item" data-song="song3">
                    <div class="song-play-button">▶</div>
                    <div class="song-info">
                      <div class="song-title">Dance Monkey</div>
                      <div class="song-artist">Tones and I</div>
                    </div>
                    <div class="song-album">The Kids Are Coming</div>
                    <div class="song-duration">3:29</div>
                  </div>
                  <div class="song-item" data-song="song4">
                    <div class="song-play-button">▶</div>
                    <div class="song-info">
                      <div class="song-title">Someone You Loved</div>
                      <div class="song-artist">Lewis Capaldi</div>
                    </div>
                    <div class="song-album">Divinely Uninspired...</div>
                    <div class="song-duration">3:02</div>
                  </div>
                  <div class="song-item" data-song="song5">
                    <div class="song-play-button">▶</div>
                    <div class="song-info">
                      <div class="song-title">Bad Guy</div>
                      <div class="song-artist">Billie Eilish</div>
                    </div>
                    <div class="song-album">When We All Fall Asleep...</div>
                    <div class="song-duration">3:14</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Browse View (hidden by default) -->
            <div class="music-view" id="browse-view">
              <h2>Browse Music</h2>
              <div class="browse-categories">
                <div class="category-card">
                  <div class="category-icon">🎸</div>
                  <div class="category-name">Rock</div>
                </div>
                <div class="category-card">
                  <div class="category-icon">🎵</div>
                  <div class="category-name">Pop</div>
                </div>
                <div class="category-card">
                  <div class="category-icon">🎷</div>
                  <div class="category-name">Jazz</div>
                </div>
                <div class="category-card">
                  <div class="category-icon">🎤</div>
                  <div class="category-name">Hip Hop</div>
                </div>
                <div class="category-card">
                  <div class="category-icon">🎻</div>
                  <div class="category-name">Classical</div>
                </div>
                <div class="category-card">
                  <div class="category-icon">🎹</div>
                  <div class="category-name">Electronic</div>
                </div>
              </div>
            </div>

            <!-- Radio View (hidden by default) -->
            <div class="music-view" id="radio-view">
              <h2>Radio Stations</h2>
              <div class="radio-stations">
                <div class="radio-station">
                  <div class="radio-icon">📻</div>
                  <div class="radio-info">
                    <div class="radio-name">Retro Hits FM</div>
                    <div class="radio-genre">80s & 90s</div>
                  </div>
                  <div class="radio-play-button">▶</div>
                </div>
                <div class="radio-station">
                  <div class="radio-icon">📻</div>
                  <div class="radio-info">
                    <div class="radio-name">Classic Rock Radio</div>
                    <div class="radio-genre">Rock</div>
                  </div>
                  <div class="radio-play-button">▶</div>
                </div>
                <div class="radio-station">
                  <div class="radio-icon">📻</div>
                  <div class="radio-info">
                    <div class="radio-name">Smooth Jazz</div>
                    <div class="radio-genre">Jazz</div>
                  </div>
                  <div class="radio-play-button">▶</div>
                </div>
                <div class="radio-station">
                  <div class="radio-icon">📻</div>
                  <div class="radio-info">
                    <div class="radio-name">Pop Hits Now</div>
                    <div class="radio-genre">Pop</div>
                  </div>
                  <div class="radio-play-button">▶</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Music Player Controls -->
        <div class="music-player">
          <div class="now-playing">
            <div class="now-playing-image">
              <img src="../img/album1.svg" alt="Now Playing" id="now-playing-img">
            </div>
            <div class="now-playing-info">
              <div class="now-playing-title" id="now-playing-title">Royalty</div>
              <div class="now-playing-artist" id="now-playing-artist">No Copyright Sounds</div>
            </div>
          </div>
          <div class="player-controls">
            <div class="control-buttons">
              <button class="control-button" id="prev-button">⏮</button>
              <button class="control-button play-pause" id="play-pause-button">▶</button>
              <button class="control-button" id="next-button">⏭</button>
            </div>
            <div class="progress-container" onclick="handleProgressClick(event)">
              <div class="current-time" id="current-time">0:00</div>
              <div class="progress-bar" onclick="handleProgressClick(event)">
                <div class="progress" id="progress" onclick="handleProgressClick(event)"></div>
              </div>
              <div class="total-time" id="total-time">4:12</div>
            </div>
          </div>
          <div class="player-options">
            <button class="option-button" id="shuffle-button">🔀</button>
            <button class="option-button" id="repeat-button">🔁</button>
            <button class="option-button" id="debug-button" title="Debug Audio" onclick="toggleDebugMode()">🔧</button>
            <div class="volume-control">
              <button class="volume-button" id="volume-button">🔊</button>
              <div class="volume-slider-container">
                <input type="range" min="0" max="100" value="80" class="volume-slider" id="volume-slider">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <!-- Audio Element (hidden) -->
  <audio id="audio-player" src="../audio/song1.mp3" preload="auto" controls style="display: none;"></audio>

  <!-- Fallback audio script -->
  <script src="../audio/beep.js"></script>

  <!-- Debug info for audio playback (hidden) -->
  <div id="audio-debug" style="display: none; position: fixed; bottom: 0; right: 0; background: rgba(0,0,0,0.7); color: white; padding: 5px; font-size: 10px; z-index: 9999;"></div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/music.js"></script>
</body>
</html>
