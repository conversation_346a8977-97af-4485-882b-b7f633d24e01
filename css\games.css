/* Games Page Styles */
.games-page {
  font-family: '<PERSON><PERSON><PERSON>', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 30px;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.games-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 25px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.games-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.games-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
  flex: 1;
  min-height: 0; /* Ensures flex items can shrink below their content size */
}

/* Featured Game Styles */
.featured-game {
  display: flex;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 350px;
}

.featured-game-image {
  width: 300px;
  min-width: 300px;
  height: 300px;
  background-color: #F1EFE2;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #ACA899;
}

.featured-game-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 15px;
}

.featured-game-info {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.game-info-content {
  flex: 1;
}

.button-container {
  padding: 20px 0;
  text-align: left;
}

.featured-game-info h2 {
  color: #0A246A;
  font-size: 20px;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ACA899;
}

.game-description {
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.6;
  color: #333333;
}

.game-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 25px;
  border-top: 1px solid #E5E5E5;
  padding-top: 15px;
}

.game-detail {
  display: flex;
  font-size: 12px;
}

.detail-label {
  font-weight: bold;
  width: 100px;
  color: #0A246A;
}

.detail-value {
  color: #333333;
}

.play-button {
  padding: 12px 30px;
  background: linear-gradient(to bottom, #7EB2F7, #0A246A);
  border: 1px solid #003C74;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  display: inline-block;
  text-align: center;
  min-width: 180px;
}

.play-button:hover {
  background: linear-gradient(to bottom, #97C4FF, #0D3A89);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.play-button:active {
  background: linear-gradient(to bottom, #0D3A89, #97C4FF);
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.play-button:disabled {
  background: linear-gradient(to bottom, #CCCCCC, #999999);
  border-color: #888888;
  color: #DDDDDD;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Game Categories Styles - Removed */

/* Popular Games Styles */
.popular-games {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.popular-games h2 {
  color: #0A246A;
  font-size: 18px;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ACA899;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.game-card {
  background-color: #F9F9F9;
  border: 1px solid #ACA899;
  border-radius: 5px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 15px;
  width: 100%;
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.game-image {
  height: 220px;
  width: 100%;
  overflow: hidden;
  background-color: #F1EFE2;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ACA899;
}

.game-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 10px;
}

.game-info {
  padding: 15px;
  text-align: center;
  width: 90%;
}

.game-info h3 {
  color: #0A246A;
  font-size: 16px;
  margin: 0 0 10px 0;
}

.game-info p {
  font-size: 13px;
  color: #333333;
  margin-bottom: 15px;
  line-height: 1.4;
  min-height: 36px;
}

.game-info .play-button {
  width: 80%;
  padding: 8px 0;
  font-size: 14px;
  margin: 0 auto;
}

/* Game Popup Styles */
.game-popup {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  overflow: auto;
}

.game-popup-content {
  position: relative;
  background-color: #ECE9D8;
  margin: 50px auto;
  padding: 0;
  width: 80%;
  max-width: 700px; /* Increased from 600px to accommodate the bubble shooter game */
  border: 1px solid #0A246A;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: popupFadeIn 0.3s ease;
}

@keyframes popupFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.game-popup-header {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  border-bottom: 1px solid #0A246A;
  border-radius: 5px 5px 0 0;
}

.game-popup-header h2 {
  margin: 0;
  font-size: 16px;
}

.close-popup {
  color: white;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
}

.close-popup:hover {
  color: #F1EFE2;
}

.game-popup-body {
  padding: 20px;
}

.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

#snake-canvas, #blockdrop-canvas, #minesweeper-canvas, #sidescroller-canvas {
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  box-shadow: inset 1px 1px 3px rgba(0, 0, 0, 0.1);
}

/* Word Scramble Game Styles */
.wordscramble-game-area {
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
}

.wordscramble-word-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.scrambled-word {
  font-size: 32px;
  font-weight: bold;
  letter-spacing: 5px;
  color: #0A246A;
  text-align: center;
  min-height: 40px;
}

.word-hint {
  font-size: 14px;
  color: #666666;
  font-style: italic;
  text-align: center;
  min-height: 20px;
}

.wordscramble-input-container {
  display: flex;
  gap: 10px;
  margin: 10px 0;
}

.word-guess {
  flex: 1;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  outline: none;
}

.word-guess:focus {
  border-color: #0A246A;
  box-shadow: 0 0 5px rgba(10, 36, 106, 0.3);
}

.submit-button, .new-word-button {
  padding: 10px 15px;
  background: linear-gradient(to bottom, #E5F1FB, #C2E0FF);
  border: 1px solid #0A246A;
  border-radius: 3px;
  color: #0A246A;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s ease;
}

.submit-button:hover, .new-word-button:hover {
  background: linear-gradient(to bottom, #C2E0FF, #A6CAF0);
}

/* Speed Typer Game Styles */
.speedtyper-game-area {
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
}

.speedtyper-text-display {
  padding: 15px;
  background-color: #F9F9F9;
  border: 1px solid #ACA899;
  border-radius: 3px;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}

.text-to-type {
  font-size: 16px;
  line-height: 1.6;
  color: #666666;
  margin-bottom: 10px;
}

.user-typing {
  font-size: 16px;
  line-height: 1.6;
  color: #0A246A;
  min-height: 20px;
}

.speedtyper-input-container {
  width: 100%;
}

.typing-input {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  outline: none;
}

.typing-input:focus {
  border-color: #0A246A;
  box-shadow: 0 0 5px rgba(10, 36, 106, 0.3);
}

.speedtyper-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.game-wpm, .game-accuracy {
  font-size: 16px;
  font-weight: bold;
  color: #0A246A;
}

.speedtyper-progress {
  width: 100%;
  margin: 10px 0;
}

.racer-track {
  height: 40px;
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
}

.player-racer, .opponent-racer {
  position: absolute;
  top: 5px;
  left: 10px;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  transition: left 0.3s ease;
}

.player-racer {
  background-color: #3A6EA5;
  z-index: 2;
}

.opponent-racer {
  background-color: #CC0000;
  z-index: 1;
}

/* Quick Math Game Styles */
.quickmath-game-area {
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
}

.quickmath-problem-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.math-problem {
  font-size: 36px;
  font-weight: bold;
  color: #0A246A;
  text-align: center;
  min-height: 50px;
  padding: 10px;
  background-color: #F9F9F9;
  border: 1px solid #ACA899;
  border-radius: 5px;
  width: 100%;
  max-width: 300px;
}

.math-result {
  font-size: 18px;
  color: #333333;
  text-align: center;
  min-height: 30px;
  margin-top: -5px;
}

.quickmath-input-container {
  display: flex;
  gap: 10px;
  margin: 10px 0;
}

.math-answer {
  flex: 1;
  padding: 10px;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  border: 1px solid #ACA899;
  border-radius: 3px;
  outline: none;
}

.math-answer:focus {
  border-color: #0A246A;
  box-shadow: 0 0 5px rgba(10, 36, 106, 0.3);
}

.game-streak {
  font-size: 16px;
  font-weight: bold;
  color: #0A246A;
}

#sidescroller-canvas {
  max-width: 100%;
}

/* Memory Match Game Styles */
.memorymatch-game-area {
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
}

.memorymatch-board {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  margin: 0 auto;
  width: 100%;
  max-width: 500px;
}

.memory-card {
  aspect-ratio: 1 / 1;
  background: linear-gradient(to bottom, #3A6EA5, #0A246A);
  border: 2px solid #FFFFFF;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0;
  color: transparent;
  transition: transform 0.3s ease, background 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.memory-card:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.memory-card.flipped {
  background: linear-gradient(to bottom, #F1EFE2, #ECE9D8);
  font-size: 24px;
  color: #0A246A;
  transform: rotateY(180deg);
}

.memory-card.matched {
  background: linear-gradient(to bottom, #D4E7F7, #C2E0FF);
  border-color: #0A246A;
  cursor: default;
}

.memory-card.flipped::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  z-index: 1;
}

.memory-card .card-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transform: rotateY(180deg);
  backface-visibility: hidden;
}

.memory-card.flipped .card-content {
  transform: rotateY(0);
}

.game-moves {
  font-size: 16px;
  font-weight: bold;
  color: #0A246A;
}

/* Bubble Shooter Game Styles */
.bubbleshooter-game-area {
  width: 100%;
  max-width: 650px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
}


#bubbleshooter-canvas {
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  box-shadow: inset 1px 1px 3px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  display: block;
  max-width: 100%;
  height: auto;
}

.game-bubbles {
  font-size: 16px;
  font-weight: bold;
  color: #0A246A;
}

.game-moves {
  font-size: 16px;
  font-weight: bold;
  color: #0A246A;
}

.hint-button {
  padding: 8px 15px;
  background: linear-gradient(to bottom, #E5F1FB, #C2E0FF);
  border: 1px solid #0A246A;
  border-radius: 3px;
  color: #0A246A;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s ease;
  margin-left: 10px;
}

.hint-button:hover {
  background: linear-gradient(to bottom, #C2E0FF, #A6CAF0);
}

.game-instructions kbd {
  background-color: #F1F1F1;
  border: 1px solid #ACA899;
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  padding: 2px 5px;
  font-family: monospace;
  font-size: 12px;
}

.game-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  max-width: 800px;
}

.game-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.game-score, .game-lives, .game-level, .game-mines-left, .game-timer {
  font-size: 16px;
  font-weight: bold;
  color: #0A246A;
}

.game-instructions {
  margin: 10px 0;
  padding: 10px;
  background-color: #F9F9F9;
  border: 1px solid #ACA899;
  border-radius: 3px;
  font-size: 14px;
  color: #333333;
  text-align: center;
}

.difficulty-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 10px 0;
}

.difficulty-button {
  padding: 5px 10px;
  background: linear-gradient(to bottom, #F9F9F9, #E5E5E5);
  border: 1px solid #ACA899;
  border-radius: 3px;
  color: #333333;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.difficulty-button:hover {
  background: linear-gradient(to bottom, #E5E5E5, #D5D5D5);
}

.difficulty-button.active {
  background: linear-gradient(to bottom, #E5F1FB, #C2E0FF);
  border-color: #0A246A;
  color: #0A246A;
  font-weight: bold;
}

.restart-button {
  padding: 6px 15px;
  background: linear-gradient(to bottom, #E5F1FB, #C2E0FF);
  border: 1px solid #0A246A;
  border-radius: 3px;
  color: #0A246A;
  font-weight: bold;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.restart-button:hover {
  background: linear-gradient(to bottom, #C2E0FF, #A6CAF0);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .featured-game {
    flex-direction: column;
    min-height: auto;
  }

  .featured-game-image {
    width: 100%;
    height: 250px;
  }

  .featured-game-info {
    padding: 20px;
  }

  .button-container {
    text-align: center;
    padding: 10px 0;
  }

  .play-button {
    width: 80%;
    max-width: 300px;
    margin: 0 auto;
  }

  .games-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .game-popup-content {
    width: 95%;
    margin: 30px auto;
  }
}

@media (max-width: 480px) {
  .games-page {
    padding: 15px;
  }

  .featured-game-image {
    height: 200px;
  }

  .featured-game-info h2 {
    font-size: 18px;
    text-align: center;
  }

  .game-description {
    text-align: center;
  }

  .button-container {
    padding: 15px 0;
  }

  .play-button {
    width: 100%;
    font-size: 16px;
    padding: 10px 20px;
  }

  .category-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .games-grid {
    grid-template-columns: 1fr;
  }
}
