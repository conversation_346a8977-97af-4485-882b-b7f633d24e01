<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingo Payment</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/payment.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="payment-page">
        <div class="payment-header-bar">
          <h1>Bingo Payment</h1>
        </div>

        <div class="payment-content">
          <div class="payment-form-section">
            <h2>Order Summary</h2>
            <div id="payment-summary">
              <!-- This will be populated dynamically based on context -->
            </div>
            <div class="payment-form">
              <div class="form-group">
                <label for="card-name">Name on Card</label>
                <input type="text" id="card-name" class="form-input" placeholder="John Doe">
              </div>
              <div class="form-group">
                <label for="card-number">Card Number</label>
                <input type="text" id="card-number" class="form-input" placeholder="1234 5678 9012 3456">
              </div>
              <div class="form-row">
                <div class="form-group half">
                  <label for="expiry-date">Expiry Date</label>
                  <input type="text" id="expiry-date" class="form-input" placeholder="MM/YY">
                </div>
                <div class="form-group half">
                  <label for="cvv">CVV</label>
                  <input type="text" id="cvv" class="form-input" placeholder="123">
                </div>
              </div>
              <div class="form-group">
                <label for="billing-address">Billing Address</label>
                <textarea id="billing-address" class="form-input" rows="3" placeholder="Enter your billing address"></textarea>
              </div>
            </div>
          </div>

          <div class="payment-summary-section">
            <h2>Payment Summary</h2>
            <div class="payment-summary">
              <!-- This will be populated dynamically -->
            </div>
            <div class="payment-actions">
              <button id="process-payment-btn" class="process-payment-btn">Process Payment</button>
              <button id="cancel-payment-btn" class="cancel-payment-btn">Cancel</button>
            </div>
          </div>
        </div>

        <!-- Payment Confirmation Section (initially hidden) -->
        <div id="payment-confirmation-section" class="payment-confirmation-section" style="display: none;">
          <div class="payment-confirmation">
            <div class="confirmation-icon">✓</div>
            <h2>Payment Successful!</h2>
            <div id="confirmation-message" class="confirmation-message">
              Your payment has been processed successfully.
            </div>
            <div id="confirmation-details" class="confirmation-details">
              <!-- This will be populated dynamically -->
            </div>
            <div class="confirmation-reference">
              <p>Transaction ID: <span id="transaction-id">TXN123456789</span></p>
              <p>Date & Time: <span id="transaction-date">May 15, 2010, 10:30 AM</span></p>
            </div>
            <div class="confirmation-actions">
              <button id="return-btn" class="return-btn">Return to Home</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/payment.js"></script>
</body>
</html>
