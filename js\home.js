// Home page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize home page
  initHomePage();
});

function initHomePage() {
  // Initialize search bar
  initSearchBar();

  // Initialize quick links
  initQuickLinks();

  // Initialize news articles
  initNewsArticles();
}

// Search Bar Functions
function initSearchBar() {
  const searchForm = document.querySelector('.xp-search-bar');
  const searchInput = document.querySelector('.xp-search-input');

  if (searchForm && searchInput) {
    searchForm.addEventListener('submit', function(event) {
      event.preventDefault();
      handleSearch(searchInput.value);
    });

    searchInput.addEventListener('keypress', function(event) {
      if (event.key === 'Enter') {
        event.preventDefault();
        handleSearch(searchInput.value);
      }
    });
  }
}

function handleSearch(query) {
  console.log('Search query:', query);

  // Show search animation
  updateStatusBar('Searching for: ' + query, 'Connected');

  // Simulate search delay
  setTimeout(() => {
    // Create a Windows XP-style alert
    alert('Bingo Search\n\nNo results found for: ' + query);
    updateStatusBar('Search complete', 'Connected');
  }, 800);
}

// Quick Links Functions
function initQuickLinks() {
  const quickLinks = document.querySelectorAll('.quick-link');

  quickLinks.forEach(link => {
    link.addEventListener('click', function() {
      const url = link.getAttribute('data-url');
      if (url) {
        handleQuickLinkClick(url);
      }
    });
  });
}

function handleQuickLinkClick(url) {
  console.log('Quick link clicked:', url);

  // Update status bar
  updateStatusBar('Navigating to: ' + url, 'Connected');

  // Navigate to the URL
  if (url === 'https://bingo.com/weather' || url === 'weather.html') {
    window.location.href = 'weather.html';
  } else if (url === 'https://bingo.com/finance' || url === 'finance.html') {
    window.location.href = 'finance.html';
  } else if (url === 'https://bingo.com/news' || url === 'news.html') {
    window.location.href = 'news.html';
  } else if (url === 'https://bingo.com/maps' || url === 'maps.html') {
    window.location.href = 'maps.html';
  } else if (url === 'https://bingo.com/calendar' || url === 'calendar.html') {
    window.location.href = 'calendar.html';
  } else if (url === 'https://bingo.com/sports' || url === 'sports.html') {
    window.location.href = 'sports.html';
  } else if (url === 'https://bingo.com/games' || url === 'games.html') {
    window.location.href = 'games.html';
  } else if (url === 'https://bingo.com/shopping' || url === 'shopping.html') {
    window.location.href = 'shopping.html';
  } else if (url === 'https://bingo.com/music' || url === 'music.html') {
    window.location.href = 'music.html';
  } else {
    // For other URLs, just update the status bar
    console.log('Unknown URL:', url);

    // Simulate navigation delay
    setTimeout(() => {
      updateStatusBar('Done', 'Connected');
    }, 300);
  }
}

// News Articles Functions
function initNewsArticles() {
  const readMoreButtons = document.querySelectorAll('.read-more');

  readMoreButtons.forEach(button => {
    button.addEventListener('click', function() {
      handleNewsClick();
    });
  });
}

function handleNewsClick() {
  console.log('News article clicked');

  // Navigate to the news page
  window.location.href = 'news.html';
}
