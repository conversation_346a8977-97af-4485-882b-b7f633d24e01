<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingo Games</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/games.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="games-page">
        <div class="games-header-bar">
          <h1>Bingo Games</h1>
        </div>

        <div class="games-content">
          <!-- Popular Games Section -->
          <div class="popular-games">
            <h2>Popular Games</h2>
            <div class="games-grid">
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/snake-game.svg" alt="Snake Game">
                </div>
                <div class="game-info">
                  <h3>Snake Game</h3>
                  <p>Control the snake to eat food and grow longer. Avoid hitting walls or your tail!</p>
                  <button class="play-button" id="play-snake-game">Play Now</button>
                </div>
              </div>
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/blockdrop-game.svg" alt="BlockDrop">
                </div>
                <div class="game-info">
                  <h3>BlockDrop</h3>
                  <p>Stack falling blocks and match colors to clear rows.</p>
                  <button class="play-button" id="play-blockdrop-game">Play Now</button>
                </div>
              </div>
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/minesweeper-game.svg" alt="Minesweeper">
                </div>
                <div class="game-info">
                  <h3>Minesweeper</h3>
                  <p>Clear the board without detonating any mines.</p>
                  <button class="play-button" id="play-minesweeper-game">Play Now</button>
                </div>
              </div>
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/sidescroller-game.svg" alt="Adventure Run">
                </div>
                <div class="game-info">
                  <h3>Adventure Run</h3>
                  <p>Run, jump, and collect coins in this side-scrolling adventure.</p>
                  <button class="play-button" id="play-sidescroller-game">Play Now</button>
                </div>
              </div>
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/scrambled-words-game.svg" alt="Word Scramble">
                </div>
                <div class="game-info">
                  <h3>Word Scramble</h3>
                  <p>Unscramble letters to form words before time runs out.</p>
                  <button class="play-button" id="play-wordscramble-game">Play Now</button>
                </div>
              </div>
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/typing-race-game.svg" alt="Speed Typer">
                </div>
                <div class="game-info">
                  <h3>Speed Typer</h3>
                  <p>Test your typing speed and accuracy in this racing game.</p>
                  <button class="play-button" id="play-speedtyper-game">Play Now</button>
                </div>
              </div>
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/quickmath-game.svg" alt="Quick Math">
                </div>
                <div class="game-info">
                  <h3>Quick Math</h3>
                  <p>Solve math problems quickly to test your mental calculation skills.</p>
                  <button class="play-button" id="play-quickmath-game">Play Now</button>
                </div>
              </div>
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/memory-match-game.svg" alt="Memory Match">
                </div>
                <div class="game-info">
                  <h3>Memory Match</h3>
                  <p>Find matching pairs of cards in this classic memory game.</p>
                  <button class="play-button" id="play-memorymatch-game">Play Now</button>
                </div>
              </div>
              <div class="game-card">
                <div class="game-image">
                  <img src="../img/bubble-shooter-game.svg" alt="Bubble Shooter">
                </div>
                <div class="game-info">
                  <h3>Bubble Shooter</h3>
                  <p>Shoot and match colorful bubbles to clear the board.</p>
                  <button class="play-button" id="play-bubbleshooter-game">Play Now</button>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <!-- Snake Game Popup -->
  <div id="snake-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>Snake Game</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <canvas id="snake-canvas" width="400" height="400"></canvas>
          <div class="game-controls">
            <div class="game-score">Score: <span id="current-score">0</span></div>
            <button id="restart-game" class="restart-button">Restart</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- BlockDrop Game Popup -->
  <div id="blockdrop-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>BlockDrop Game</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <canvas id="blockdrop-canvas" width="400" height="400"></canvas>
          <div class="game-controls">
            <div class="game-score">Score: <span id="blockdrop-current-score">0</span></div>
            <button id="restart-blockdrop-game" class="restart-button">Restart</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Minesweeper Game Popup -->
  <div id="minesweeper-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>Minesweeper Game</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <canvas id="minesweeper-canvas" width="400" height="400"></canvas>
          <div class="game-controls">
            <div class="game-info-row">
              <div class="game-mines-left">Mines: <span id="mines-left">10</span></div>
              <div class="game-timer">Time: <span id="minesweeper-timer">0</span></div>
            </div>
            <div class="difficulty-controls">
              <button id="easy-mode" class="difficulty-button active">Easy</button>
              <button id="medium-mode" class="difficulty-button">Medium</button>
              <button id="hard-mode" class="difficulty-button">Hard</button>
            </div>
            <button id="restart-minesweeper-game" class="restart-button">New Game</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Adventure Run Game Popup -->
  <div id="sidescroller-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>Adventure Run</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <canvas id="sidescroller-canvas" width="800" height="400"></canvas>
          <div class="game-controls">
            <div class="game-info-row">
              <div class="game-score">Score: <span id="sidescroller-score">0</span></div>
              <div class="game-lives">Lives: <span id="sidescroller-lives">3</span></div>
              <div class="game-level">Level: <span id="sidescroller-level">1</span></div>
            </div>
            <div class="game-instructions">
              <p>Use arrow keys to move and jump. Collect coins and avoid enemies!</p>
            </div>
            <button id="restart-sidescroller-game" class="restart-button">Restart</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Word Scramble Game Popup -->
  <div id="wordscramble-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>Word Scramble</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <div class="wordscramble-game-area">
            <div class="wordscramble-word-container">
              <div id="scrambled-word" class="scrambled-word"></div>
              <div id="word-hint" class="word-hint"></div>
            </div>
            <div class="wordscramble-input-container">
              <input type="text" id="word-guess" class="word-guess" placeholder="Type your guess here...">
              <button id="submit-guess" class="submit-button">Submit</button>
              <button id="new-word" class="new-word-button">New Word</button>
            </div>
            <div class="game-info-row">
              <div class="game-score">Score: <span id="wordscramble-score">0</span></div>
              <div class="game-timer">Time: <span id="wordscramble-timer">60</span>s</div>
              <div class="game-level">Level: <span id="wordscramble-level">1</span></div>
            </div>
            <div class="difficulty-controls">
              <button id="ws-easy-mode" class="difficulty-button active">Easy</button>
              <button id="ws-medium-mode" class="difficulty-button">Medium</button>
              <button id="ws-hard-mode" class="difficulty-button">Hard</button>
              <button id="ws-expert-mode" class="difficulty-button">Expert</button>
            </div>
          </div>
          <div class="game-controls">
            <div class="game-instructions">
              <p>Unscramble the letters to form a word. Type your answer and click Submit.</p>
            </div>
            <button id="restart-wordscramble-game" class="restart-button">Restart</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Speed Typer Game Popup -->
  <div id="speedtyper-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>Speed Typer</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <div class="speedtyper-game-area">
            <div class="speedtyper-text-display">
              <div id="text-to-type" class="text-to-type"></div>
              <div id="user-typing" class="user-typing"></div>
            </div>
            <div class="speedtyper-input-container">
              <input type="text" id="typing-input" class="typing-input" placeholder="Start typing here...">
            </div>
            <div class="speedtyper-stats">
              <div class="game-info-row">
                <div class="game-wpm">WPM: <span id="typing-wpm">0</span></div>
                <div class="game-accuracy">Accuracy: <span id="typing-accuracy">100</span>%</div>
                <div class="game-timer">Time: <span id="speedtyper-timer">60</span>s</div>
              </div>
              <div class="speedtyper-progress">
                <div class="racer-track">
                  <div id="player-racer" class="player-racer"></div>
                  <div id="opponent-racer" class="opponent-racer"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="game-controls">
            <div class="game-instructions">
              <p>Type the text as quickly and accurately as possible to win the race!</p>
            </div>
            <button id="restart-speedtyper-game" class="restart-button">New Race</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Math Game Popup -->
  <div id="quickmath-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>Quick Math</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <div class="quickmath-game-area">
            <div class="quickmath-problem-container">
              <div id="math-problem" class="math-problem"></div>
              <div id="math-result" class="math-result"></div>
            </div>
            <div class="quickmath-input-container">
              <input type="number" id="math-answer" class="math-answer" placeholder="Enter your answer...">
              <button id="submit-answer" class="submit-button">Submit</button>
            </div>
            <div class="game-info-row">
              <div class="game-score">Score: <span id="quickmath-score">0</span></div>
              <div class="game-timer">Time: <span id="quickmath-timer">30</span>s</div>
              <div class="game-streak">Streak: <span id="quickmath-streak">0</span></div>
            </div>
            <div class="difficulty-controls">
              <button id="qm-easy-mode" class="difficulty-button active">Easy</button>
              <button id="qm-medium-mode" class="difficulty-button">Medium</button>
              <button id="qm-hard-mode" class="difficulty-button">Hard</button>
              <button id="qm-expert-mode" class="difficulty-button">Expert</button>
            </div>
          </div>
          <div class="game-controls">
            <div class="game-instructions">
              <p>Solve the math problems as quickly as possible. Enter your answer and click Submit.</p>
            </div>
            <button id="restart-quickmath-game" class="restart-button">Restart</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Memory Match Game Popup -->
  <div id="memorymatch-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>Memory Match</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <div class="memorymatch-game-area">
            <div class="memorymatch-board" id="memory-board"></div>
            <div class="game-info-row">
              <div class="game-score">Pairs Found: <span id="memorymatch-pairs">0</span>/<span id="memorymatch-total-pairs">8</span></div>
              <div class="game-moves">Moves: <span id="memorymatch-moves">0</span></div>
              <div class="game-timer">Time: <span id="memorymatch-timer">0</span>s</div>
            </div>
            <div class="difficulty-controls">
              <button id="mm-easy-mode" class="difficulty-button active">Easy (4x4)</button>
              <button id="mm-medium-mode" class="difficulty-button">Medium (6x6)</button>
              <button id="mm-hard-mode" class="difficulty-button">Hard (8x8)</button>
            </div>
          </div>
          <div class="game-controls">
            <div class="game-instructions">
              <p>Find all matching pairs of cards. Click on cards to flip them and try to remember their positions.</p>
            </div>
            <button id="restart-memorymatch-game" class="restart-button">New Game</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bubble Shooter Game Popup -->
  <div id="bubbleshooter-game-popup" class="game-popup">
    <div class="game-popup-content">
      <div class="game-popup-header">
        <h2>Bubble Shooter</h2>
        <span class="close-popup">&times;</span>
      </div>
      <div class="game-popup-body">
        <div class="game-container">
          <div class="bubbleshooter-game-area">
            <canvas id="bubbleshooter-canvas" width="600" height="600"></canvas>
            <div class="game-info-row">
              <div class="game-score">Score: <span id="bubbleshooter-score">0</span></div>
              <div class="game-level">Level: <span id="bubbleshooter-level">1</span></div>
              <div class="game-bubbles">Bubbles Left: <span id="bubbleshooter-bubbles">50</span></div>
            </div>
            <div class="difficulty-controls">
              <button id="bs-easy-mode" class="difficulty-button active">Easy</button>
              <button id="bs-medium-mode" class="difficulty-button">Medium</button>
              <button id="bs-hard-mode" class="difficulty-button">Hard</button>
            </div>
          </div>
          <div class="game-controls">
            <div class="game-instructions">
              <p>Aim and shoot bubbles to match 3 or more of the same color. Clear all bubbles to advance to the next level.</p>
              <p>Use mouse to aim. Click or press Space to shoot. Press Shift+Space to swap with the next bubble.</p>
            </div>
            <button id="restart-bubbleshooter-game" class="restart-button">New Game</button>
          </div>
        </div>
      </div>
    </div>
  </div>





  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/snake-game.js"></script>
  <script src="../js/blockdrop-game.js"></script>
  <script src="../js/minesweeper-game.js"></script>
  <script src="../js/sidescroller-game.js"></script>
  <script src="../js/wordscramble-game.js"></script>
  <script src="../js/speedtyper-game.js"></script>
  <script src="../js/quickmath-game.js"></script>
  <script src="../js/memorymatch-game.js"></script>
  <script src="../js/bubbleshooter-game.js"></script>
  <script src="../js/games.js"></script>
</body>
</html>
