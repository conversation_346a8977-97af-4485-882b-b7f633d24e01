// News page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize news page
  initNewsPage();
});

function initNewsPage() {
  // Initialize category tabs
  initCategoryTabs();

  // Initialize news articles
  initNewsArticles();

  // Initialize weather widget
  initWeatherWidget();
}

// Category Tabs Functions
function initCategoryTabs() {
  const categoryTabs = document.querySelectorAll('.category-item');

  categoryTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // Remove active class from all tabs
      categoryTabs.forEach(t => t.classList.remove('active'));

      // Add active class to clicked tab
      tab.classList.add('active');

      // Hide all sections
      const sections = document.querySelectorAll('.news-section');
      sections.forEach(section => {
        section.classList.remove('active');
      });

      // Show the selected section
      const category = tab.getAttribute('data-category');
      const sectionToShow = document.getElementById(category + '-section');
      if (sectionToShow) {
        sectionToShow.classList.add('active');
      }

      // Update status bar
      updateStatusBar('Loading category: ' + tab.textContent, 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('Category loaded: ' + tab.textContent, 'Connected');
      }, 300);
    });
  });
}

// News Articles Functions
function initNewsArticles() {
  // Featured news
  const featuredNews = document.querySelector('.featured-news');
  if (featuredNews) {
    featuredNews.addEventListener('click', function(event) {
      // Don't trigger if the read more button was clicked
      if (!event.target.classList.contains('read-more-btn')) {
        handleArticleClick(featuredNews.querySelector('h2').textContent);
      }
    });
  }

  // Read more buttons
  const readMoreButtons = document.querySelectorAll('.read-more-btn');
  readMoreButtons.forEach(button => {
    button.addEventListener('click', function() {
      const articleTitle = button.closest('.featured-news, .news-list-item, .news-article').querySelector('h2, h3, h4').textContent;
      handleArticleClick(articleTitle);
    });
  });

  // News list items
  const newsListItems = document.querySelectorAll('.news-list-item');
  newsListItems.forEach(item => {
    item.addEventListener('click', function() {
      const articleTitle = item.querySelector('h3').textContent;
      handleArticleClick(articleTitle);
    });
  });

  // News articles
  const newsArticles = document.querySelectorAll('.news-article');
  newsArticles.forEach(article => {
    article.addEventListener('click', function() {
      const articleTitle = article.querySelector('h3').textContent;
      handleArticleClick(articleTitle);
    });
  });
}

function handleArticleClick(title) {
  console.log('Article clicked:', title);

  // In a real application, this would navigate to the article page
  // For demo purposes, just update the status bar
  updateStatusBar('Reading article: ' + title, 'Connected');
}

// Weather Widget Functions
function initWeatherWidget() {
  const weatherWidget = document.querySelector('.weather-widget');
  if (weatherWidget) {
    // Add click event to the weather widget header
    const weatherHeader = weatherWidget.querySelector('h3');
    if (weatherHeader) {
      weatherHeader.addEventListener('click', function() {
        handleWeatherWidgetClick();
      });
    }

    // Initialize subscribe button
    initSubscribeButton();

    // Set weather date to 2010 (as per user preference)
    updateWeatherDates();
  }
}

function handleWeatherWidgetClick() {
  console.log('Weather widget clicked');

  // Navigate to the weather page
  window.location.href = 'weather.html';
}

function initSubscribeButton() {
  const subscribeButton = document.querySelector('.subscribe-button');
  if (subscribeButton) {
    subscribeButton.addEventListener('click', function() {
      const emailInput = document.querySelector('.subscribe-input');
      if (emailInput && emailInput.value.trim() !== '') {
        // Show a simple confirmation message
        alert('Thank you for subscribing to our newsletter!');
        emailInput.value = '';
      } else {
        alert('Please enter a valid email address.');
      }
    });
  }
}

function updateWeatherDates() {
  // Set the main weather date to 2010
  const weatherDate = document.querySelector('.weather-date');
  if (weatherDate) {
    const date = new Date(2010, 4, 15); // May 15, 2010
    weatherDate.textContent = formatDate(date);
  }

  // Update forecast dates
  const forecastDates = document.querySelectorAll('.forecast-date');
  forecastDates.forEach((element, index) => {
    const date = new Date(2010, 4, 16 + index); // May 16, 17, 18, 2010
    element.textContent = formatDate(date, true);
  });
}

function formatDate(date, shortFormat = false) {
  if (shortFormat) {
    // Short format: "May 16"
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  } else {
    // Full format: "May 15, 2010"
    return date.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
  }
}

// Helper function to get formatted date
function getFormattedDate() {
  const date = new Date();
  const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
  return date.toLocaleDateString('en-US', options);
}
