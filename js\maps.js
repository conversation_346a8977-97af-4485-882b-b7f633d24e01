// Maps page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize maps page
  initMapsPage();
});

function initMapsPage() {
  // Initialize tabs
  initTabs();

  // Initialize search functionality
  initSearch();

  // Initialize directions functionality
  initDirections();

  // Initialize map controls
  initMapControls();

  // Initialize map options
  initMapOptions();

  // Initialize map with default view
  initMap();

  // Update status bar
  updateStatusBar('Maps loaded successfully', 'Connected');
}

// Tabs Functions
function initTabs() {
  const tabs = document.querySelectorAll('.maps-tab');

  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // Remove active class from all tabs
      tabs.forEach(t => t.classList.remove('active'));

      // Add active class to clicked tab
      tab.classList.add('active');

      // Hide all panels
      document.querySelector('.maps-search-panel').classList.remove('active');
      document.querySelector('.maps-directions-panel').classList.remove('active');

      // Show the selected panel
      const tabName = tab.getAttribute('data-tab');
      document.querySelector(`.maps-${tabName}-panel`).classList.add('active');

      // Update status bar
      updateStatusBar(`Switched to ${tabName} mode`, 'Connected');
    });
  });
}

// Search Functions
function initSearch() {
  const searchButton = document.getElementById('search-button');
  const searchInput = document.getElementById('search-input');

  if (searchButton && searchInput) {
    searchButton.addEventListener('click', function() {
      handleSearch(searchInput.value);
    });

    searchInput.addEventListener('keypress', function(event) {
      if (event.key === 'Enter') {
        handleSearch(searchInput.value);
      }
    });
  }

  // Initialize search result items
  const resultItems = document.querySelectorAll('.result-item');
  resultItems.forEach(item => {
    item.addEventListener('click', function() {
      const title = item.querySelector('.result-title').textContent;
      handleResultClick(title);
    });
  });
}

function handleSearch(query) {
  if (!query.trim()) {
    updateStatusBar('Please enter a search term', 'Warning');
    return;
  }

  // Update status bar
  updateStatusBar(`Searching for: ${query}`, 'Connected');

  // Show loading state
  const searchResults = document.getElementById('search-results');
  const originalContent = searchResults.innerHTML;
  searchResults.innerHTML = `
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Searching for "${query}"...</p>
    </div>
  `;

  // Simulate search delay
  setTimeout(() => {
    // Generate mock search results
    const results = getMockSearchResults(query);

    // Display search results
    displaySearchResults(results);

    // Update status bar
    updateStatusBar(`Found ${results.length} results for: ${query}`, 'Connected');
  }, 800);
}

function getMockSearchResults(query) {
  // Mock search results based on query
  const allResults = [
    {
      title: 'Delhi Central Park',
      address: 'Connaught Place, New Delhi, India'
    },
    {
      title: 'Mumbai Central Station',
      address: 'Nagar Highway, Mumbai, India'
    },
    {
      title: 'Bangalore Tech Park',
      address: 'Electronic City, Bangalore, India'
    },
    {
      title: 'Chennai Beach',
      address: 'Marina Beach Road, Chennai, India'
    },
    {
      title: 'Kolkata Museum',
      address: 'Park Street, Kolkata, India'
    },
    {
      title: 'Hyderabad Charminar',
      address: 'Old City, Hyderabad, India'
    },
    {
      title: 'Jaipur Palace',
      address: 'City Palace, Jaipur, India'
    }
  ];

  // Filter results based on query
  return allResults.filter(result =>
    result.title.toLowerCase().includes(query.toLowerCase()) ||
    result.address.toLowerCase().includes(query.toLowerCase())
  );
}

function displaySearchResults(results) {
  const searchResults = document.getElementById('search-results');

  if (results.length === 0) {
    searchResults.innerHTML = `
      <h3>No Results Found</h3>
      <p>Try a different search term or browse popular places.</p>

      <h3>Popular Places</h3>
      <div class="result-item">
        <div class="result-title">Taj Mahal</div>
        <div class="result-address">Agra, Uttar Pradesh, India</div>
      </div>
      <div class="result-item">
        <div class="result-title">India Gate</div>
        <div class="result-address">Rajpath, New Delhi, India</div>
      </div>
    `;
  } else {
    searchResults.innerHTML = `
      <h3>Search Results</h3>
      ${results.map(result => `
        <div class="result-item">
          <div class="result-title">${result.title}</div>
          <div class="result-address">${result.address}</div>
        </div>
      `).join('')}
    `;
  }

  // Reinitialize click handlers for result items
  const resultItems = document.querySelectorAll('.result-item');
  resultItems.forEach(item => {
    item.addEventListener('click', function() {
      const title = item.querySelector('.result-title').textContent;
      handleResultClick(title);
    });
  });
}

function handleResultClick(title) {
  // Update status bar
  updateStatusBar(`Selected location: ${title}`, 'Connected');

  // Add marker to map
  addMapMarker(title);
}

// Directions Functions
function initDirections() {
  const directionButton = document.getElementById('direction-button');
  const startInput = document.getElementById('start-input');
  const destinationInput = document.getElementById('destination-input');

  if (directionButton && startInput && destinationInput) {
    directionButton.addEventListener('click', function() {
      handleDirections(startInput.value, destinationInput.value);
    });
  }
}

function handleDirections(start, destination) {
  if (!start.trim() || !destination.trim()) {
    updateStatusBar('Please enter both start and destination', 'Warning');
    return;
  }

  // Update status bar
  updateStatusBar(`Getting directions from ${start} to ${destination}`, 'Connected');

  // Show loading state
  const directionResults = document.getElementById('direction-results');
  directionResults.style.display = 'block';
  directionResults.innerHTML = `
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Calculating route...</p>
    </div>
  `;

  // Simulate directions calculation delay
  setTimeout(() => {
    // Generate mock directions
    const directions = getMockDirections(start, destination);

    // Display directions
    displayDirections(directions);

    // Update status bar
    updateStatusBar(`Route found from ${start} to ${destination}`, 'Connected');

    // Add route to map
    addRouteToMap(start, destination);
  }, 1000);
}

function getMockDirections(start, destination) {
  // Calculate mock distance and duration based on string lengths
  const distance = (start.length * 10 + destination.length * 15);
  const hours = Math.floor((distance / 60));
  const minutes = Math.floor((distance % 60) / 5) * 5;

  return {
    summary: {
      distance: `${distance} km`,
      duration: hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
    },
    steps: [
      {
        instruction: `Head east from ${start}`,
        distance: '2.5 km'
      },
      {
        instruction: 'Turn right onto National Highway',
        distance: `${Math.floor(distance * 0.3)} km`
      },
      {
        instruction: 'Continue onto Express Highway',
        distance: `${Math.floor(distance * 0.4)} km`
      },
      {
        instruction: `Take exit toward ${destination}`,
        distance: `${Math.floor(distance * 0.2)} km`
      },
      {
        instruction: `Turn left to reach ${destination}`,
        distance: '1.5 km'
      }
    ]
  };
}

function displayDirections(directions) {
  const directionResults = document.getElementById('direction-results');

  directionResults.innerHTML = `
    <div class="direction-summary">
      <div class="summary-item">
        <div class="summary-value">${directions.summary.distance}</div>
        <div class="summary-label">Distance</div>
      </div>
      <div class="summary-item">
        <div class="summary-value">${directions.summary.duration}</div>
        <div class="summary-label">Duration</div>
      </div>
    </div>

    <h3>Route Directions</h3>
    ${directions.steps.map((step, index) => `
      <div class="direction-step">
        <div class="step-number">${index + 1}</div>
        <div class="step-instruction">${step.instruction}</div>
        <div class="step-distance">${step.distance}</div>
      </div>
    `).join('')}
  `;
}

// Map Functions
function initMap() {
  const mapGrid = document.getElementById('map-grid');

  // Ensure the map background is properly set
  mapGrid.style.backgroundImage = 'url("../img/map-background.svg")';
  mapGrid.style.backgroundSize = 'cover';
  mapGrid.style.backgroundPosition = 'center';

  // Force a repaint to ensure the background is displayed
  mapGrid.style.display = 'none';
  mapGrid.offsetHeight; // Force a reflow
  mapGrid.style.display = '';

  console.log("Map initialized with background:", mapGrid.style.backgroundImage);

  // Add initial markers for major Indian cities
  addMapMarker('Delhi', { top: '20%', left: '40%' });
  addMapMarker('Mumbai', { top: '50%', left: '20%' });
  addMapMarker('Bangalore', { top: '70%', left: '40%' });
  addMapMarker('Chennai', { top: '65%', left: '50%' });
  addMapMarker('Kolkata', { top: '40%', left: '70%' });
  addMapMarker('Hyderabad', { top: '55%', left: '45%' });

  // Update status bar
  updateStatusBar('Map loaded', 'Connected');
}

function addMapMarker(title, position = null, markerType = 'default') {
  const mapGrid = document.getElementById('map-grid');

  // If position is not provided, generate a random position
  if (!position) {
    position = {
      top: `${20 + Math.random() * 60}%`,
      left: `${20 + Math.random() * 60}%`
    };
  }

  // Create marker element
  const marker = document.createElement('div');
  marker.className = 'map-marker';

  // Add marker type class if specified
  if (markerType !== 'default') {
    marker.classList.add(`${markerType}-marker`);
  }

  marker.title = title;
  marker.style.top = position.top;
  marker.style.left = position.left;

  // Add marker to map
  mapGrid.appendChild(marker);

  // Add click handler to marker
  marker.addEventListener('click', function() {
    // Highlight the marker
    document.querySelectorAll('.map-marker').forEach(m => {
      m.style.zIndex = 1;
    });
    marker.style.zIndex = 10;

    updateStatusBar(`Selected: ${title}`, 'Connected');

    // Show a small info window
    showInfoWindow(title, marker);
  });

  return marker;
}

function showInfoWindow(title, marker) {
  // Remove any existing info windows
  const existingInfo = document.querySelector('.map-info-window');
  if (existingInfo) {
    existingInfo.remove();
  }

  // Create info window
  const infoWindow = document.createElement('div');
  infoWindow.className = 'map-info-window';
  infoWindow.innerHTML = `
    <div class="info-title">${title}</div>
    <div class="info-content">
      <div>Popular destination in India</div>
      <button class="info-directions-btn">Directions</button>
    </div>
  `;

  // Position the info window above the marker
  infoWindow.style.position = 'absolute';
  infoWindow.style.bottom = '48px'; // Height of the marker
  infoWindow.style.left = '50%';
  infoWindow.style.transform = 'translateX(-50%)';
  infoWindow.style.backgroundColor = 'white';
  infoWindow.style.padding = '8px';
  infoWindow.style.borderRadius = '4px';
  infoWindow.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
  infoWindow.style.zIndex = '100';
  infoWindow.style.minWidth = '150px';

  // Add the info window to the marker
  marker.appendChild(infoWindow);

  // Add click handler for directions button
  const directionsBtn = infoWindow.querySelector('.info-directions-btn');
  directionsBtn.addEventListener('click', function() {
    // Switch to directions tab
    document.querySelector('.maps-tab[data-tab="directions"]').click();

    // Set the destination input
    document.getElementById('destination-input').value = title;
  });
}

function addRouteToMap(start, destination) {
  // Clear existing markers and routes
  const mapGrid = document.getElementById('map-grid');
  const markers = mapGrid.querySelectorAll('.map-marker');
  const routes = mapGrid.querySelectorAll('.map-route');

  markers.forEach(marker => marker.remove());
  routes.forEach(route => route.remove());

  // Add start marker
  const startPosition = { top: '30%', left: '30%' };
  const startMarker = addMapMarker(`Start: ${start}`, startPosition, 'start');

  // Add destination marker
  const destPosition = { top: '60%', left: '60%' };
  const destMarker = addMapMarker(`Destination: ${destination}`, destPosition, 'end');

  // Draw route between markers
  drawRoute(startPosition, destPosition);
}

function drawRoute(startPos, endPos) {
  const mapGrid = document.getElementById('map-grid');

  // Convert percentage positions to pixels for calculation
  const startX = parseFloat(startPos.left) / 100 * mapGrid.offsetWidth;
  const startY = parseFloat(startPos.top) / 100 * mapGrid.offsetHeight;
  const endX = parseFloat(endPos.left) / 100 * mapGrid.offsetWidth;
  const endY = parseFloat(endPos.top) / 100 * mapGrid.offsetHeight;

  // Calculate distance and angle
  const dx = endX - startX;
  const dy = endY - startY;
  const distance = Math.sqrt(dx * dx + dy * dy);
  const angle = Math.atan2(dy, dx) * 180 / Math.PI;

  // Create route element
  const route = document.createElement('div');
  route.className = 'map-route';
  route.style.width = `${distance}px`;
  route.style.left = `${startX}px`;
  route.style.top = `${startY}px`;
  route.style.transform = `rotate(${angle}deg)`;

  // Add route to map
  mapGrid.appendChild(route);

  // Add some waypoints for a more realistic route
  addWaypoints(startPos, endPos);
}

function addWaypoints(startPos, endPos) {
  // Add 2-3 waypoints to make the route look more realistic
  const mapGrid = document.getElementById('map-grid');

  // Calculate positions between start and end
  const numWaypoints = 2 + Math.floor(Math.random() * 2); // 2 or 3 waypoints

  for (let i = 1; i <= numWaypoints; i++) {
    // Calculate position with some randomness
    const ratio = i / (numWaypoints + 1);
    const randomOffsetX = (Math.random() - 0.5) * 10;
    const randomOffsetY = (Math.random() - 0.5) * 10;

    const wpLeft = parseFloat(startPos.left) + (parseFloat(endPos.left) - parseFloat(startPos.left)) * ratio + randomOffsetX;
    const wpTop = parseFloat(startPos.top) + (parseFloat(endPos.top) - parseFloat(startPos.top)) * ratio + randomOffsetY;

    const prevLeft = i === 1 ? parseFloat(startPos.left) :
                    parseFloat(startPos.left) + (parseFloat(endPos.left) - parseFloat(startPos.left)) * ((i-1) / (numWaypoints + 1)) + randomOffsetX;
    const prevTop = i === 1 ? parseFloat(startPos.top) :
                   parseFloat(startPos.top) + (parseFloat(endPos.top) - parseFloat(startPos.top)) * ((i-1) / (numWaypoints + 1)) + randomOffsetY;

    // Draw route segment
    drawRoute(
      { left: `${prevLeft}%`, top: `${prevTop}%` },
      { left: `${wpLeft}%`, top: `${wpTop}%` }
    );

    // If it's the last waypoint, connect to destination
    if (i === numWaypoints) {
      drawRoute(
        { left: `${wpLeft}%`, top: `${wpTop}%` },
        endPos
      );
    }
  }
}

// Map Controls Functions
function initMapControls() {
  const zoomIn = document.getElementById('zoom-in');
  const zoomOut = document.getElementById('zoom-out');

  if (zoomIn && zoomOut) {
    zoomIn.addEventListener('click', function() {
      handleZoom('in');
    });

    zoomOut.addEventListener('click', function() {
      handleZoom('out');
    });
  }
}

function handleZoom(direction) {
  const mapGrid = document.getElementById('map-grid');

  // Get current scale
  const currentTransform = window.getComputedStyle(mapGrid).transform;
  let currentScale = 1;

  if (currentTransform && currentTransform !== 'none') {
    // Extract scale value from matrix
    const matrix = currentTransform.match(/^matrix\((.+)\)$/);
    if (matrix) {
      const values = matrix[1].split(', ');
      currentScale = parseFloat(values[0]);
    }
  }

  // Calculate new scale
  let newScale;
  if (direction === 'in') {
    newScale = Math.min(currentScale * 1.2, 3); // Max zoom level
    updateStatusBar('Zoomed in', 'Connected');
  } else {
    newScale = Math.max(currentScale / 1.2, 0.5); // Min zoom level
    updateStatusBar('Zoomed out', 'Connected');
  }

  // Apply new scale
  mapGrid.style.transform = `scale(${newScale})`;
  mapGrid.style.transformOrigin = 'center center';

  // Adjust marker sizes inversely to keep them the same visual size
  const markers = document.querySelectorAll('.map-marker');
  markers.forEach(marker => {
    marker.style.transform = `translate(-50%, -100%) scale(${1/newScale})`;
  });
}

// Map Options Functions
function initMapOptions() {
  const mapViewOption = document.getElementById('map-view-option');
  const satelliteOption = document.getElementById('satellite-option');
  const trafficOption = document.getElementById('traffic-option');
  const transitOption = document.getElementById('transit-option');

  // Initialize map with default view
  const mapGrid = document.getElementById('map-grid');

  // Make sure the map background is set correctly - using relative path from the HTML file
  mapGrid.style.backgroundImage = 'url("img/map-background.svg")';
  mapGrid.style.backgroundSize = 'cover';
  mapGrid.style.backgroundPosition = 'center';

  // Force a repaint to ensure the background is displayed
  mapGrid.style.display = 'none';
  mapGrid.offsetHeight; // Force a reflow
  mapGrid.style.display = '';

  console.log("Map options initialized with background:", mapGrid.style.backgroundImage);

  // Map view radio button
  if (mapViewOption) {
    mapViewOption.addEventListener('change', function() {
      if (this.checked) {
        toggleMapType('map');
      }
    });
  }

  // Satellite view radio button
  if (satelliteOption) {
    satelliteOption.addEventListener('change', function() {
      if (this.checked) {
        toggleMapType('satellite');
      }
    });
  }

  // Traffic layer checkbox
  if (trafficOption) {
    trafficOption.addEventListener('change', function() {
      toggleMapLayer('traffic', this.checked);
    });
  }

  // Transit layer checkbox
  if (transitOption) {
    transitOption.addEventListener('change', function() {
      toggleMapLayer('transit', this.checked);
    });
  }
}

// Function to toggle between map types (map or satellite)
function toggleMapType(mapType) {
  const mapGrid = document.getElementById('map-grid');
  const mapBackground = document.getElementById('map-background');

  // Reset any transform that might have been applied by zoom
  mapGrid.style.transform = 'scale(1)';

  console.log("Toggling map type to:", mapType);

  if (mapType === 'satellite') {
    // Set satellite view
    mapBackground.src = 'img/satellite-background.svg';
    updateStatusBar('Satellite view enabled', 'Connected');
  } else {
    // Set map view
    mapBackground.src = 'img/map-background.svg';
    updateStatusBar('Map view enabled', 'Connected');
  }

  // Log the current background image for debugging
  console.log("Current background image:", mapBackground.src);

  // Reset any markers to their default size
  const markers = document.querySelectorAll('.map-marker');
  markers.forEach(marker => {
    marker.style.transform = 'translate(-50%, -100%)';
  });
}

// Function to toggle map layers (traffic, transit)
function toggleMapLayer(layer, enabled) {
  const mapGrid = document.getElementById('map-grid');

  switch (layer) {
    case 'traffic':
      updateStatusBar(`Traffic view ${enabled ? 'enabled' : 'disabled'}`, 'Connected');
      // In a real app, this would show traffic data
      if (enabled) {
        // Add some visual indication of traffic
        const trafficOverlay = document.createElement('div');
        trafficOverlay.className = 'traffic-overlay';
        trafficOverlay.style.position = 'absolute';
        trafficOverlay.style.top = '0';
        trafficOverlay.style.left = '0';
        trafficOverlay.style.width = '100%';
        trafficOverlay.style.height = '100%';
        trafficOverlay.style.background = 'linear-gradient(45deg, rgba(255,0,0,0.1) 25%, transparent 25%, transparent 50%, rgba(255,0,0,0.1) 50%, rgba(255,0,0,0.1) 75%, transparent 75%)';
        trafficOverlay.style.backgroundSize = '50px 50px';
        trafficOverlay.style.pointerEvents = 'none';
        trafficOverlay.style.zIndex = '2';

        // Remove existing overlay if any
        const existingOverlay = document.querySelector('.traffic-overlay');
        if (existingOverlay) {
          existingOverlay.remove();
        }

        mapGrid.appendChild(trafficOverlay);
      } else {
        // Remove traffic overlay
        const trafficOverlay = document.querySelector('.traffic-overlay');
        if (trafficOverlay) {
          trafficOverlay.remove();
        }
      }
      break;

    case 'transit':
      updateStatusBar(`Public transit ${enabled ? 'shown' : 'hidden'}`, 'Connected');
      // In a real app, this would show transit lines
      if (enabled) {
        // Add some visual indication of transit lines
        const transitLines = [
          { start: { top: '20%', left: '20%' }, end: { top: '80%', left: '20%' }, color: '#4CAF50' },
          { start: { top: '20%', left: '40%' }, end: { top: '80%', left: '40%' }, color: '#2196F3' },
          { start: { top: '20%', left: '60%' }, end: { top: '80%', left: '60%' }, color: '#FFC107' },
          { start: { top: '20%', left: '20%' }, end: { top: '20%', left: '80%' }, color: '#9C27B0' },
          { start: { top: '50%', left: '20%' }, end: { top: '50%', left: '80%' }, color: '#F44336' }
        ];

        // Remove existing transit lines
        document.querySelectorAll('.transit-line').forEach(line => line.remove());

        // Add transit lines
        transitLines.forEach(line => {
          const transitLine = document.createElement('div');
          transitLine.className = 'transit-line';
          transitLine.style.position = 'absolute';
          transitLine.style.height = '3px';
          transitLine.style.backgroundColor = line.color;
          transitLine.style.zIndex = '2';
          transitLine.style.pointerEvents = 'none';

          // For vertical lines
          if (line.start.left === line.end.left) {
            transitLine.style.width = '3px';
            transitLine.style.height = '60%';
            transitLine.style.top = line.start.top;
            transitLine.style.left = line.start.left;
          }
          // For horizontal lines
          else if (line.start.top === line.end.top) {
            transitLine.style.width = '60%';
            transitLine.style.height = '3px';
            transitLine.style.top = line.start.top;
            transitLine.style.left = line.start.left;
          }

          mapGrid.appendChild(transitLine);
        });
      } else {
        // Remove transit lines
        document.querySelectorAll('.transit-line').forEach(line => line.remove());
      }
      break;
  }
}

// Keep the old function for backward compatibility
function toggleMapOption(option, enabled) {
  if (option === 'satellite') {
    toggleMapType(enabled ? 'satellite' : 'map');
  } else {
    toggleMapLayer(option, enabled);
  }
}
