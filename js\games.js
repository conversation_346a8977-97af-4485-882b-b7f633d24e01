// Games page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize games page
  initGamesPage();
});

function initGamesPage() {
  // Initialize play buttons
  initPlayButtons();



  // Initialize game cards
  initGameCards();

  // Update status bar
  updateStatusBar('Games page loaded successfully', 'Connected');
}

// Play Buttons Functions
function initPlayButtons() {
  // Snake Game
  const playSnakeButton = document.getElementById('play-snake-game');
  const snakeGamePopup = document.getElementById('snake-game-popup');
  const snakeCloseButton = snakeGamePopup ? snakeGamePopup.querySelector('.close-popup') : null;

  if (playSnakeButton && snakeGamePopup) {
    playSnakeButton.addEventListener('click', function() {
      // Show the snake game popup
      snakeGamePopup.style.display = 'block';

      // Start the snake game
      if (typeof startSnakeGame === 'function') {
        startSnakeGame();
      }

      // Update status bar
      updateStatusBar('Snake game started', 'Connected');
    });
  }

  if (snakeCloseButton && snakeGamePopup) {
    snakeCloseButton.addEventListener('click', function() {
      // Hide the snake game popup
      snakeGamePopup.style.display = 'none';

      // Pause the snake game
      if (typeof pauseSnakeGame === 'function') {
        pauseSnakeGame();
      }

      // Update status bar
      updateStatusBar('Snake game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === snakeGamePopup) {
        snakeGamePopup.style.display = 'none';

        // Pause the snake game
        if (typeof pauseSnakeGame === 'function') {
          pauseSnakeGame();
        }

        // Update status bar
        updateStatusBar('Snake game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && snakeGamePopup.style.display === 'block') {
        snakeGamePopup.style.display = 'none';

        // Pause the snake game
        if (typeof pauseSnakeGame === 'function') {
          pauseSnakeGame();
        }

        // Update status bar
        updateStatusBar('Snake game closed', 'Connected');
      }
    });
  }

  // Initialize snake restart button
  const snakeRestartButton = document.getElementById('restart-game');
  if (snakeRestartButton) {
    snakeRestartButton.addEventListener('click', function() {
      // Restart the snake game
      if (typeof restartSnakeGame === 'function') {
        restartSnakeGame();
      }

      // Update status bar
      updateStatusBar('Snake game restarted', 'Connected');
    });
  }

  // BlockDrop Game
  const playBlockDropButton = document.getElementById('play-blockdrop-game');
  const blockDropGamePopup = document.getElementById('blockdrop-game-popup');
  const blockDropCloseButton = blockDropGamePopup ? blockDropGamePopup.querySelector('.close-popup') : null;

  if (playBlockDropButton && blockDropGamePopup) {
    playBlockDropButton.addEventListener('click', function() {
      // Show the BlockDrop game popup
      blockDropGamePopup.style.display = 'block';

      // Start the BlockDrop game
      if (typeof startBlockDropGame === 'function') {
        startBlockDropGame();
      }

      // Update status bar
      updateStatusBar('BlockDrop game started', 'Connected');
    });
  }

  if (blockDropCloseButton && blockDropGamePopup) {
    blockDropCloseButton.addEventListener('click', function() {
      // Hide the BlockDrop game popup
      blockDropGamePopup.style.display = 'none';

      // Pause the BlockDrop game
      if (typeof pauseBlockDropGame === 'function') {
        pauseBlockDropGame();
      }

      // Update status bar
      updateStatusBar('BlockDrop game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === blockDropGamePopup) {
        blockDropGamePopup.style.display = 'none';

        // Pause the BlockDrop game
        if (typeof pauseBlockDropGame === 'function') {
          pauseBlockDropGame();
        }

        // Update status bar
        updateStatusBar('BlockDrop game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && blockDropGamePopup.style.display === 'block') {
        blockDropGamePopup.style.display = 'none';

        // Pause the BlockDrop game
        if (typeof pauseBlockDropGame === 'function') {
          pauseBlockDropGame();
        }

        // Update status bar
        updateStatusBar('BlockDrop game closed', 'Connected');
      }
    });
  }

  // Initialize BlockDrop restart button
  const blockDropRestartButton = document.getElementById('restart-blockdrop-game');
  if (blockDropRestartButton) {
    blockDropRestartButton.addEventListener('click', function() {
      // Restart the BlockDrop game
      if (typeof restartBlockDropGame === 'function') {
        restartBlockDropGame();
      }

      // Update status bar
      updateStatusBar('BlockDrop game restarted', 'Connected');
    });
  }

  // Minesweeper Game
  const playMinesweeperButton = document.getElementById('play-minesweeper-game');
  const minesweeperGamePopup = document.getElementById('minesweeper-game-popup');
  const minesweeperCloseButton = minesweeperGamePopup ? minesweeperGamePopup.querySelector('.close-popup') : null;

  if (playMinesweeperButton && minesweeperGamePopup) {
    playMinesweeperButton.addEventListener('click', function() {
      // Show the Minesweeper game popup
      minesweeperGamePopup.style.display = 'block';

      // Start the Minesweeper game
      if (typeof initMinesweeperGame === 'function') {
        initMinesweeperGame();
      }

      // Update status bar
      updateStatusBar('Minesweeper game started', 'Connected');
    });
  }

  if (minesweeperCloseButton && minesweeperGamePopup) {
    minesweeperCloseButton.addEventListener('click', function() {
      // Hide the Minesweeper game popup
      minesweeperGamePopup.style.display = 'none';

      // Pause the Minesweeper game
      if (typeof pauseMinesweeperGame === 'function') {
        pauseMinesweeperGame();
      }

      // Update status bar
      updateStatusBar('Minesweeper game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === minesweeperGamePopup) {
        minesweeperGamePopup.style.display = 'none';

        // Pause the Minesweeper game
        if (typeof pauseMinesweeperGame === 'function') {
          pauseMinesweeperGame();
        }

        // Update status bar
        updateStatusBar('Minesweeper game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && minesweeperGamePopup.style.display === 'block') {
        minesweeperGamePopup.style.display = 'none';

        // Pause the Minesweeper game
        if (typeof pauseMinesweeperGame === 'function') {
          pauseMinesweeperGame();
        }

        // Update status bar
        updateStatusBar('Minesweeper game closed', 'Connected');
      }
    });
  }

  // Initialize Minesweeper restart button
  const minesweeperRestartButton = document.getElementById('restart-minesweeper-game');
  if (minesweeperRestartButton) {
    minesweeperRestartButton.addEventListener('click', function() {
      // Restart the Minesweeper game
      if (typeof restartMinesweeperGame === 'function') {
        restartMinesweeperGame();
      }

      // Update status bar
      updateStatusBar('Minesweeper game restarted', 'Connected');
    });
  }

  // Adventure Run Game
  const playSideScrollerButton = document.getElementById('play-sidescroller-game');
  const sideScrollerGamePopup = document.getElementById('sidescroller-game-popup');
  const sideScrollerCloseButton = sideScrollerGamePopup ? sideScrollerGamePopup.querySelector('.close-popup') : null;

  if (playSideScrollerButton && sideScrollerGamePopup) {
    playSideScrollerButton.addEventListener('click', function() {
      // Show the Adventure Run game popup
      sideScrollerGamePopup.style.display = 'block';

      // Start the Adventure Run game
      if (typeof startSideScrollerGame === 'function') {
        startSideScrollerGame();
      }

      // Update status bar
      updateStatusBar('Adventure Run game started', 'Connected');
    });
  }

  if (sideScrollerCloseButton && sideScrollerGamePopup) {
    sideScrollerCloseButton.addEventListener('click', function() {
      // Hide the Adventure Run game popup
      sideScrollerGamePopup.style.display = 'none';

      // Pause the Adventure Run game
      if (typeof pauseSideScrollerGame === 'function') {
        pauseSideScrollerGame();
      }

      // Update status bar
      updateStatusBar('Adventure Run game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === sideScrollerGamePopup) {
        sideScrollerGamePopup.style.display = 'none';

        // Pause the Adventure Run game
        if (typeof pauseSideScrollerGame === 'function') {
          pauseSideScrollerGame();
        }

        // Update status bar
        updateStatusBar('Adventure Run game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && sideScrollerGamePopup.style.display === 'block') {
        sideScrollerGamePopup.style.display = 'none';

        // Pause the Adventure Run game
        if (typeof pauseSideScrollerGame === 'function') {
          pauseSideScrollerGame();
        }

        // Update status bar
        updateStatusBar('Adventure Run game closed', 'Connected');
      }
    });
  }

  // Initialize Adventure Run restart button
  const sideScrollerRestartButton = document.getElementById('restart-sidescroller-game');
  if (sideScrollerRestartButton) {
    sideScrollerRestartButton.addEventListener('click', function() {
      // Restart the Adventure Run game
      if (typeof restartSideScrollerGame === 'function') {
        restartSideScrollerGame();
      }

      // Update status bar
      updateStatusBar('Adventure Run game restarted', 'Connected');
    });
  }

  // Word Scramble Game
  const playWordScrambleButton = document.getElementById('play-wordscramble-game');
  const wordScrambleGamePopup = document.getElementById('wordscramble-game-popup');
  const wordScrambleCloseButton = wordScrambleGamePopup ? wordScrambleGamePopup.querySelector('.close-popup') : null;

  if (playWordScrambleButton && wordScrambleGamePopup) {
    playWordScrambleButton.addEventListener('click', function() {
      // Show the Word Scramble game popup
      wordScrambleGamePopup.style.display = 'block';

      // Start the Word Scramble game
      if (typeof startWordScrambleGame === 'function') {
        startWordScrambleGame();
      }

      // Update status bar
      updateStatusBar('Word Scramble game started', 'Connected');
    });
  }

  if (wordScrambleCloseButton && wordScrambleGamePopup) {
    wordScrambleCloseButton.addEventListener('click', function() {
      // Hide the Word Scramble game popup
      wordScrambleGamePopup.style.display = 'none';

      // Pause the Word Scramble game
      if (typeof pauseWordScrambleGame === 'function') {
        pauseWordScrambleGame();
      }

      // Update status bar
      updateStatusBar('Word Scramble game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === wordScrambleGamePopup) {
        wordScrambleGamePopup.style.display = 'none';

        // Pause the Word Scramble game
        if (typeof pauseWordScrambleGame === 'function') {
          pauseWordScrambleGame();
        }

        // Update status bar
        updateStatusBar('Word Scramble game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && wordScrambleGamePopup.style.display === 'block') {
        wordScrambleGamePopup.style.display = 'none';

        // Pause the Word Scramble game
        if (typeof pauseWordScrambleGame === 'function') {
          pauseWordScrambleGame();
        }

        // Update status bar
        updateStatusBar('Word Scramble game closed', 'Connected');
      }
    });
  }

  // Initialize Word Scramble restart button
  const wordScrambleRestartButton = document.getElementById('restart-wordscramble-game');
  if (wordScrambleRestartButton) {
    wordScrambleRestartButton.addEventListener('click', function() {
      // Restart the Word Scramble game
      if (typeof restartWordScrambleGame === 'function') {
        restartWordScrambleGame();
      }

      // Update status bar
      updateStatusBar('Word Scramble game restarted', 'Connected');
    });
  }

  // Speed Typer Game
  const playSpeedTyperButton = document.getElementById('play-speedtyper-game');
  const speedTyperGamePopup = document.getElementById('speedtyper-game-popup');
  const speedTyperCloseButton = speedTyperGamePopup ? speedTyperGamePopup.querySelector('.close-popup') : null;

  if (playSpeedTyperButton && speedTyperGamePopup) {
    playSpeedTyperButton.addEventListener('click', function() {
      // Show the Speed Typer game popup
      speedTyperGamePopup.style.display = 'block';

      // Start the Speed Typer game
      if (typeof startSpeedTyperGame === 'function') {
        startSpeedTyperGame();
      }

      // Update status bar
      updateStatusBar('Speed Typer game started', 'Connected');
    });
  }

  if (speedTyperCloseButton && speedTyperGamePopup) {
    speedTyperCloseButton.addEventListener('click', function() {
      // Hide the Speed Typer game popup
      speedTyperGamePopup.style.display = 'none';

      // Pause the Speed Typer game
      if (typeof pauseSpeedTyperGame === 'function') {
        pauseSpeedTyperGame();
      }

      // Update status bar
      updateStatusBar('Speed Typer game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === speedTyperGamePopup) {
        speedTyperGamePopup.style.display = 'none';

        // Pause the Speed Typer game
        if (typeof pauseSpeedTyperGame === 'function') {
          pauseSpeedTyperGame();
        }

        // Update status bar
        updateStatusBar('Speed Typer game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && speedTyperGamePopup.style.display === 'block') {
        speedTyperGamePopup.style.display = 'none';

        // Pause the Speed Typer game
        if (typeof pauseSpeedTyperGame === 'function') {
          pauseSpeedTyperGame();
        }

        // Update status bar
        updateStatusBar('Speed Typer game closed', 'Connected');
      }
    });
  }

  // Initialize Speed Typer restart button
  const speedTyperRestartButton = document.getElementById('restart-speedtyper-game');
  if (speedTyperRestartButton) {
    speedTyperRestartButton.addEventListener('click', function() {
      // Restart the Speed Typer game
      if (typeof restartSpeedTyperGame === 'function') {
        restartSpeedTyperGame();
      }

      // Update status bar
      updateStatusBar('Speed Typer game restarted', 'Connected');
    });
  }

  // Quick Math Game
  const playQuickMathButton = document.getElementById('play-quickmath-game');
  const quickMathGamePopup = document.getElementById('quickmath-game-popup');
  const quickMathCloseButton = quickMathGamePopup ? quickMathGamePopup.querySelector('.close-popup') : null;

  // Memory Match Game
  const playMemoryMatchButton = document.getElementById('play-memorymatch-game');
  const memoryMatchGamePopup = document.getElementById('memorymatch-game-popup');
  const memoryMatchCloseButton = memoryMatchGamePopup ? memoryMatchGamePopup.querySelector('.close-popup') : null;

  if (playQuickMathButton && quickMathGamePopup) {
    playQuickMathButton.addEventListener('click', function() {
      // Show the Quick Math game popup
      quickMathGamePopup.style.display = 'block';

      // Start the Quick Math game
      if (typeof startQuickMathGame === 'function') {
        startQuickMathGame();
      }

      // Update status bar
      updateStatusBar('Quick Math game started', 'Connected');
    });
  }

  if (quickMathCloseButton && quickMathGamePopup) {
    quickMathCloseButton.addEventListener('click', function() {
      // Hide the Quick Math game popup
      quickMathGamePopup.style.display = 'none';

      // Pause the Quick Math game
      if (typeof pauseQuickMathGame === 'function') {
        pauseQuickMathGame();
      }

      // Update status bar
      updateStatusBar('Quick Math game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === quickMathGamePopup) {
        quickMathGamePopup.style.display = 'none';

        // Pause the Quick Math game
        if (typeof pauseQuickMathGame === 'function') {
          pauseQuickMathGame();
        }

        // Update status bar
        updateStatusBar('Quick Math game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && quickMathGamePopup.style.display === 'block') {
        quickMathGamePopup.style.display = 'none';

        // Pause the Quick Math game
        if (typeof pauseQuickMathGame === 'function') {
          pauseQuickMathGame();
        }

        // Update status bar
        updateStatusBar('Quick Math game closed', 'Connected');
      }
    });
  }

  // Initialize Quick Math restart button
  const quickMathRestartButton = document.getElementById('restart-quickmath-game');
  if (quickMathRestartButton) {
    quickMathRestartButton.addEventListener('click', function() {
      // Restart the Quick Math game
      if (typeof restartQuickMathGame === 'function') {
        restartQuickMathGame();
      }

      // Update status bar
      updateStatusBar('Quick Math game restarted', 'Connected');
    });
  }

  // Memory Match Game Event Listeners
  if (playMemoryMatchButton && memoryMatchGamePopup) {
    playMemoryMatchButton.addEventListener('click', function() {
      // Show the Memory Match game popup
      memoryMatchGamePopup.style.display = 'block';

      // Start the Memory Match game
      if (typeof startMemoryMatchGame === 'function') {
        startMemoryMatchGame();
      }

      // Update status bar
      updateStatusBar('Memory Match game started', 'Connected');
    });
  }

  // Bubble Shooter Game
  const playBubbleShooterButton = document.getElementById('play-bubbleshooter-game');
  const bubbleShooterGamePopup = document.getElementById('bubbleshooter-game-popup');
  const bubbleShooterCloseButton = bubbleShooterGamePopup ? bubbleShooterGamePopup.querySelector('.close-popup') : null;

  if (playBubbleShooterButton && bubbleShooterGamePopup) {
    playBubbleShooterButton.addEventListener('click', function() {
      // Show the Bubble Shooter game popup
      bubbleShooterGamePopup.style.display = 'block';

      // Start the Bubble Shooter game
      if (typeof startBubbleShooterGame === 'function') {
        startBubbleShooterGame();
      }

      // Update status bar
      updateStatusBar('Bubble Shooter game started', 'Connected');
    });
  }

  if (memoryMatchCloseButton && memoryMatchGamePopup) {
    memoryMatchCloseButton.addEventListener('click', function() {
      // Hide the Memory Match game popup
      memoryMatchGamePopup.style.display = 'none';

      // Pause the Memory Match game
      if (typeof pauseMemoryMatchGame === 'function') {
        pauseMemoryMatchGame();
      }

      // Update status bar
      updateStatusBar('Memory Match game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === memoryMatchGamePopup) {
        memoryMatchGamePopup.style.display = 'none';

        // Pause the Memory Match game
        if (typeof pauseMemoryMatchGame === 'function') {
          pauseMemoryMatchGame();
        }

        // Update status bar
        updateStatusBar('Memory Match game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && memoryMatchGamePopup.style.display === 'block') {
        memoryMatchGamePopup.style.display = 'none';

        // Pause the Memory Match game
        if (typeof pauseMemoryMatchGame === 'function') {
          pauseMemoryMatchGame();
        }

        // Update status bar
        updateStatusBar('Memory Match game closed', 'Connected');
      }
    });
  }

  // Initialize Memory Match restart button
  const memoryMatchRestartButton = document.getElementById('restart-memorymatch-game');
  if (memoryMatchRestartButton) {
    memoryMatchRestartButton.addEventListener('click', function() {
      // Restart the Memory Match game
      if (typeof restartMemoryMatchGame === 'function') {
        restartMemoryMatchGame();
      }

      // Update status bar
      updateStatusBar('Memory Match game restarted', 'Connected');
    });
  }

  if (bubbleShooterCloseButton && bubbleShooterGamePopup) {
    bubbleShooterCloseButton.addEventListener('click', function() {
      // Hide the Bubble Shooter game popup
      bubbleShooterGamePopup.style.display = 'none';

      // Pause the Bubble Shooter game
      if (typeof pauseBubbleShooterGame === 'function') {
        pauseBubbleShooterGame();
      }

      // Update status bar
      updateStatusBar('Bubble Shooter game closed', 'Connected');
    });

    // Close popup when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === bubbleShooterGamePopup) {
        bubbleShooterGamePopup.style.display = 'none';

        // Pause the Bubble Shooter game
        if (typeof pauseBubbleShooterGame === 'function') {
          pauseBubbleShooterGame();
        }

        // Update status bar
        updateStatusBar('Bubble Shooter game closed', 'Connected');
      }
    });

    // Add escape key to close popup
    window.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && bubbleShooterGamePopup.style.display === 'block') {
        bubbleShooterGamePopup.style.display = 'none';

        // Pause the Bubble Shooter game
        if (typeof pauseBubbleShooterGame === 'function') {
          pauseBubbleShooterGame();
        }

        // Update status bar
        updateStatusBar('Bubble Shooter game closed', 'Connected');
      }
    });
  }

  // Initialize Bubble Shooter restart button
  const bubbleShooterRestartButton = document.getElementById('restart-bubbleshooter-game');
  if (bubbleShooterRestartButton) {
    bubbleShooterRestartButton.addEventListener('click', function() {
      // Restart the Bubble Shooter game
      if (typeof restartBubbleShooterGame === 'function') {
        restartBubbleShooterGame();
      }

      // Update status bar
      updateStatusBar('Bubble Shooter game restarted', 'Connected');
    });
  }


}

// Game Categories Functions - Removed

// Game Cards Functions
function initGameCards() {
  const gameCards = document.querySelectorAll('.game-card');

  gameCards.forEach(card => {
    // Skip cards that have a play button with an ID (these are handled separately)
    const playButton = card.querySelector('.play-button');
    if (playButton && playButton.id) {
      return;
    }

    card.addEventListener('click', function() {
      const gameName = this.querySelector('h3').textContent;

      // Show a message that this game is coming soon
      alert(`${gameName} is coming soon!`);

      // Update status bar
      updateStatusBar(`${gameName} is coming soon`, 'Connected');
    });
  });
}

// Update high score display
function updateHighScoreDisplay(score) {
  const highScoreElement = document.getElementById('snake-high-score');
  if (highScoreElement) {
    highScoreElement.textContent = score;
  }
}

// Load high score from localStorage
function loadHighScore() {
  const highScore = localStorage.getItem('snakeHighScore') || 0;
  updateHighScoreDisplay(highScore);
  return parseInt(highScore);
}

// Save high score to localStorage
function saveHighScore(score) {
  const currentHighScore = loadHighScore();
  if (score > currentHighScore) {
    localStorage.setItem('snakeHighScore', score);
    updateHighScoreDisplay(score);
    return true;
  }
  return false;
}

// Initialize high score on page load
loadHighScore();
