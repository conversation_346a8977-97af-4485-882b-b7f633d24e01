<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="300" height="300" fill="#F1EFE2" />
  
  <!-- Game title -->
  <text x="150" y="30" font-family="Tahoma, Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#0A246A">Minesweeper</text>
  
  <!-- Game board -->
  <rect x="50" y="50" width="200" height="200" fill="#C0C0C0" stroke="#808080" stroke-width="2" />
  
  <!-- Grid lines -->
  <g stroke="#808080" stroke-width="1">
    <!-- Vertical lines -->
    <line x1="70" y1="50" x2="70" y2="250" />
    <line x1="90" y1="50" x2="90" y2="250" />
    <line x1="110" y1="50" x2="110" y2="250" />
    <line x1="130" y1="50" x2="130" y2="250" />
    <line x1="150" y1="50" x2="150" y2="250" />
    <line x1="170" y1="50" x2="170" y2="250" />
    <line x1="190" y1="50" x2="190" y2="250" />
    <line x1="210" y1="50" x2="210" y2="250" />
    <line x1="230" y1="50" x2="230" y2="250" />
    
    <!-- Horizontal lines -->
    <line x1="50" y1="70" x2="250" y2="70" />
    <line x1="50" y1="90" x2="250" y2="90" />
    <line x1="50" y1="110" x2="250" y2="110" />
    <line x1="50" y1="130" x2="250" y2="130" />
    <line x1="50" y1="150" x2="250" y2="150" />
    <line x1="50" y1="170" x2="250" y2="170" />
    <line x1="50" y1="190" x2="250" y2="190" />
    <line x1="50" y1="210" x2="250" y2="210" />
    <line x1="50" y1="230" x2="250" y2="230" />
  </g>
  
  <!-- Revealed cells -->
  <!-- Cell with 1 -->
  <rect x="50" y="50" width="20" height="20" fill="#C0C0C0" stroke="#808080" stroke-width="1" />
  <text x="60" y="65" font-family="Tahoma, Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#0000FF">1</text>
  
  <!-- Cell with 2 -->
  <rect x="70" y="50" width="20" height="20" fill="#C0C0C0" stroke="#808080" stroke-width="1" />
  <text x="80" y="65" font-family="Tahoma, Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#008000">2</text>
  
  <!-- Cell with 3 -->
  <rect x="90" y="50" width="20" height="20" fill="#C0C0C0" stroke="#808080" stroke-width="1" />
  <text x="100" y="65" font-family="Tahoma, Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#FF0000">3</text>
  
  <!-- Cell with mine -->
  <rect x="110" y="50" width="20" height="20" fill="#C0C0C0" stroke="#808080" stroke-width="1" />
  <circle cx="120" cy="60" r="7" fill="#000000" />
  
  <!-- Cell with flag -->
  <rect x="130" y="50" width="20" height="20" fill="#C0C0C0" stroke="#808080" stroke-width="1" />
  <polygon points="135,53 135,67 145,60" fill="#FF0000" />
  
  <!-- Unopened cells -->
  <g fill="#C0C0C0" stroke="#808080" stroke-width="1">
    <rect x="150" y="50" width="20" height="20" />
    <rect x="170" y="50" width="20" height="20" />
    <rect x="190" y="50" width="20" height="20" />
    <rect x="210" y="50" width="20" height="20" />
    <rect x="230" y="50" width="20" height="20" />
    
    <!-- Add 3D effect to unopened cells -->
    <path d="M150,50 L170,50 L170,70 L150,70 Z" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linejoin="miter" />
    <path d="M170,50 L170,70 L150,70 Z" fill="none" stroke="#808080" stroke-width="2" stroke-linejoin="miter" />
    
    <path d="M170,50 L190,50 L190,70 L170,70 Z" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linejoin="miter" />
    <path d="M190,50 L190,70 L170,70 Z" fill="none" stroke="#808080" stroke-width="2" stroke-linejoin="miter" />
    
    <path d="M190,50 L210,50 L210,70 L190,70 Z" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linejoin="miter" />
    <path d="M210,50 L210,70 L190,70 Z" fill="none" stroke="#808080" stroke-width="2" stroke-linejoin="miter" />
    
    <path d="M210,50 L230,50 L230,70 L210,70 Z" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linejoin="miter" />
    <path d="M230,50 L230,70 L210,70 Z" fill="none" stroke="#808080" stroke-width="2" stroke-linejoin="miter" />
    
    <path d="M230,50 L250,50 L250,70 L230,70 Z" fill="none" stroke="#FFFFFF" stroke-width="2" stroke-linejoin="miter" />
    <path d="M250,50 L250,70 L230,70 Z" fill="none" stroke="#808080" stroke-width="2" stroke-linejoin="miter" />
  </g>
  
  <!-- More cells with numbers -->
  <rect x="50" y="70" width="20" height="20" fill="#C0C0C0" stroke="#808080" stroke-width="1" />
  <text x="60" y="85" font-family="Tahoma, Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#0000FF">1</text>
  
  <rect x="70" y="70" width="20" height="20" fill="#C0C0C0" stroke="#808080" stroke-width="1" />
  <text x="80" y="85" font-family="Tahoma, Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#008000">2</text>
  
  <!-- Game info -->
  <rect x="100" y="260" width="100" height="30" fill="#C0C0C0" stroke="#808080" stroke-width="2" rx="5" ry="5" />
  <text x="150" y="280" font-family="Tahoma, Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#FF0000">012</text>
  
  <!-- Smiley face -->
  <circle cx="150" cy="275" r="12" fill="#FFFF00" stroke="#000000" stroke-width="1" />
  <circle cx="145" cy="271" r="2" fill="#000000" />
  <circle cx="155" cy="271" r="2" fill="#000000" />
  <path d="M143,280 Q150,285 157,280" fill="none" stroke="#000000" stroke-width="1.5" />
</svg>
