// Memory Match Game functionality
let mmBoard = [];
let mmFlippedCards = [];
let mmMatchedPairs = 0;
let mmTotalPairs = 8;
let mmMoves = 0;
let mmTimer = 0;
let mmTimerInterval;
let mmGameRunning = false;
let mmGameOver = false;
let mmDifficulty = 'easy'; // easy, medium, hard

// Windows XP themed card symbols
const mmCardSymbols = {
  easy: [
    '📁', '🖥️', '📄', '🔒', '🔍', '📝', '🖨️', '🔔',
    '📁', '🖥️', '📄', '🔒', '🔍', '📝', '🖨️', '🔔'
  ],
  medium: [
    '📁', '🖥️', '📄', '🔒', '🔍', '📝', '🖨️', '🔔',
    '📊', '📧', '⚙️', '🗑️', '🎮', '🎵', '📅', '🔋',
    '📷', '📞', '📁', '🖥️', '📄', '🔒', '🔍', '📝',
    '🖨️', '🔔', '📊', '📧', '⚙️', '🗑️', '🎮', '🎵',
    '📅', '🔋', '📷', '📞'
  ],
  hard: [
    '📁', '🖥️', '📄', '🔒', '🔍', '📝', '🖨️', '🔔',
    '📊', '📧', '⚙️', '🗑️', '🎮', '🎵', '📅', '🔋',
    '📷', '📞', '🔗', '📌', '🔖', '📚', '🎨', '🌐',
    '🔑', '📈', '📱', '💾', '📡', '🔎', '📂', '🖱️',
    '📁', '🖥️', '📄', '🔒', '🔍', '📝', '🖨️', '🔔',
    '📊', '📧', '⚙️', '🗑️', '🎮', '🎵', '📅', '🔋',
    '📷', '📞', '🔗', '📌', '🔖', '📚', '🎨', '🌐',
    '🔑', '📈', '📱', '💾', '📡', '🔎', '📂', '🖱️'
  ]
};

// Initialize the Memory Match game
function initMemoryMatchGame() {
  // Initialize difficulty buttons
  initMemoryMatchDifficultyButtons();

  // Add event listener to restart button
  const restartButton = document.getElementById('restart-memorymatch-game');
  if (restartButton) {
    restartButton.addEventListener('click', restartMemoryMatchGame);
  }

  // Reset game state
  resetMemoryMatchGame();
}

// Initialize difficulty buttons
function initMemoryMatchDifficultyButtons() {
  const easyButton = document.getElementById('mm-easy-mode');
  const mediumButton = document.getElementById('mm-medium-mode');
  const hardButton = document.getElementById('mm-hard-mode');

  // Function to set active button
  function setActiveButton(button) {
    // Remove active class from all buttons
    document.querySelectorAll('.difficulty-button').forEach(btn => {
      if (btn.id.startsWith('mm-')) {
        btn.classList.remove('active');
      }
    });

    // Add active class to selected button
    button.classList.add('active');
  }

  // Add event listeners
  if (easyButton) {
    easyButton.addEventListener('click', function() {
      setActiveButton(this);
      setMemoryMatchDifficulty('easy');
    });
  }

  if (mediumButton) {
    mediumButton.addEventListener('click', function() {
      setActiveButton(this);
      setMemoryMatchDifficulty('medium');
    });
  }

  if (hardButton) {
    hardButton.addEventListener('click', function() {
      setActiveButton(this);
      setMemoryMatchDifficulty('hard');
    });
  }
}

// Set difficulty
function setMemoryMatchDifficulty(difficulty) {
  mmDifficulty = difficulty;
  restartMemoryMatchGame();
}

// Start the Memory Match game
function startMemoryMatchGame() {
  if (!mmGameRunning && !mmGameOver) {
    mmGameRunning = true;

    // Start timer
    startMemoryMatchTimer();

    // Create board
    createMemoryMatchBoard();
  } else if (mmGameOver) {
    restartMemoryMatchGame();
  }
}

// Pause the Memory Match game
function pauseMemoryMatchGame() {
  mmGameRunning = false;
  clearInterval(mmTimerInterval);
}

// Restart the Memory Match game
function restartMemoryMatchGame() {
  pauseMemoryMatchGame();
  resetMemoryMatchGame();
  startMemoryMatchGame();
}

// Reset the game state
function resetMemoryMatchGame() {
  mmBoard = [];
  mmFlippedCards = [];
  mmMatchedPairs = 0;
  mmMoves = 0;
  mmTimer = 0;
  mmGameRunning = false;
  mmGameOver = false;

  // Set board size based on difficulty
  switch (mmDifficulty) {
    case 'easy':
      mmTotalPairs = 8;
      break;
    case 'medium':
      mmTotalPairs = 18; // Updated to match the new number of pairs in the medium array (36 cards total for 6x6 grid)
      break;
    case 'hard':
      mmTotalPairs = 32;
      break;
  }

  // Update displays
  updateMemoryMatchPairsDisplay();
  updateMemoryMatchMovesDisplay();
  updateMemoryMatchTimerDisplay();
  updateMemoryMatchTotalPairsDisplay();
}

// Create the memory match board
function createMemoryMatchBoard() {
  const boardElement = document.getElementById('memory-board');
  if (!boardElement) return;

  // Clear the board
  boardElement.innerHTML = '';

  // Set grid columns based on difficulty
  let columns;
  switch (mmDifficulty) {
    case 'easy':
      columns = 4; // 4x4 grid
      break;
    case 'medium':
      columns = 6; // 6x6 grid
      break;
    case 'hard':
      columns = 8; // 8x8 grid
      break;
  }

  boardElement.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;

  // Get symbols for current difficulty
  const symbols = [...mmCardSymbols[mmDifficulty]];

  // Shuffle symbols
  shuffleArray(symbols);

  // Create cards
  symbols.forEach((symbol, index) => {
    const card = document.createElement('div');
    card.className = 'memory-card';
    card.dataset.index = index;
    card.dataset.symbol = symbol;

    const cardContent = document.createElement('div');
    cardContent.className = 'card-content';
    cardContent.textContent = symbol;

    card.appendChild(cardContent);

    card.addEventListener('click', () => handleCardClick(card));

    boardElement.appendChild(card);

    // Add to board array
    mmBoard.push({
      index: index,
      symbol: symbol,
      flipped: false,
      matched: false
    });
  });
}

// Handle card click
function handleCardClick(card) {
  // Ignore if game is not running or card is already flipped/matched
  if (!mmGameRunning) return;

  const index = parseInt(card.dataset.index);

  // Ignore if card is already flipped or matched
  if (mmBoard[index].flipped || mmBoard[index].matched) return;

  // Ignore if two cards are already flipped
  if (mmFlippedCards.length >= 2) return;

  // Flip the card
  flipCard(card, index);

  // Add to flipped cards
  mmFlippedCards.push({ card, index });

  // Check for match if two cards are flipped
  if (mmFlippedCards.length === 2) {
    mmMoves++;
    updateMemoryMatchMovesDisplay();

    // Check for match
    checkForMatch();
  }
}

// Flip a card
function flipCard(card, index) {
  card.classList.add('flipped');
  mmBoard[index].flipped = true;
}

// Unflip a card
function unflipCard(card, index) {
  card.classList.remove('flipped');
  mmBoard[index].flipped = false;
}

// Check for match
function checkForMatch() {
  const card1 = mmFlippedCards[0];
  const card2 = mmFlippedCards[1];

  if (mmBoard[card1.index].symbol === mmBoard[card2.index].symbol) {
    // Match found
    markAsMatched(card1.card, card1.index);
    markAsMatched(card2.card, card2.index);

    mmMatchedPairs++;
    updateMemoryMatchPairsDisplay();

    // Check for win
    if (mmMatchedPairs === mmTotalPairs) {
      handleMemoryMatchGameWin();
    }

    // Clear flipped cards
    mmFlippedCards = [];
  } else {
    // No match, flip back after delay
    setTimeout(() => {
      unflipCard(card1.card, card1.index);
      unflipCard(card2.card, card2.index);

      // Clear flipped cards
      mmFlippedCards = [];
    }, 1000);
  }
}

// Mark card as matched
function markAsMatched(card, index) {
  card.classList.add('matched');
  mmBoard[index].matched = true;
}

// Start the timer
function startMemoryMatchTimer() {
  clearInterval(mmTimerInterval);

  mmTimerInterval = setInterval(function() {
    mmTimer++;
    updateMemoryMatchTimerDisplay();
  }, 1000);
}

// Update displays
function updateMemoryMatchPairsDisplay() {
  const pairsElement = document.getElementById('memorymatch-pairs');
  if (pairsElement) {
    pairsElement.textContent = mmMatchedPairs;
  }
}

function updateMemoryMatchTotalPairsDisplay() {
  const totalPairsElement = document.getElementById('memorymatch-total-pairs');
  if (totalPairsElement) {
    totalPairsElement.textContent = mmTotalPairs;
  }
}

function updateMemoryMatchMovesDisplay() {
  const movesElement = document.getElementById('memorymatch-moves');
  if (movesElement) {
    movesElement.textContent = mmMoves;
  }
}

function updateMemoryMatchTimerDisplay() {
  const timerElement = document.getElementById('memorymatch-timer');
  if (timerElement) {
    timerElement.textContent = mmTimer;
  }
}

// Handle game win
function handleMemoryMatchGameWin() {
  mmGameRunning = false;
  mmGameOver = true;
  clearInterval(mmTimerInterval);

  // Show win message
  setTimeout(() => {
    alert(`Congratulations! You found all pairs in ${mmMoves} moves and ${mmTimer} seconds!`);

    // Save high score
    saveMemoryMatchHighScore();
  }, 500);
}

// Save high score
function saveMemoryMatchHighScore() {
  const highScoreKey = `memorymatch-high-score-${mmDifficulty}`;
  const highScore = localStorage.getItem(highScoreKey);

  // Calculate score (lower is better)
  const score = mmMoves + (mmTimer / 10);

  if (!highScore || score < parseFloat(highScore)) {
    localStorage.setItem(highScoreKey, score.toString());
    return true;
  }

  return false;
}

// Utility function to shuffle array
function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the game
  initMemoryMatchGame();
});
