// Bingopedia page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize Bingopedia page
  initBingopediaPage();

  // Initialize popup close button
  document.getElementById('popup-close-button').addEventListener('click', closeArticlePopup);

  // Close popup when clicking outside the content
  document.getElementById('article-popup').addEventListener('click', function(e) {
    if (e.target === this) {
      closeArticlePopup();
    }
  });

  // Close popup when pressing Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && document.getElementById('article-popup').classList.contains('show')) {
      closeArticlePopup();
    }
  });
});

// Global variables
let currentArticle = null;
let allArticles = [];

// Define all categories that work with the Unreal Engine browser plugin
let categories = {
  'technology': [],
  'science': [],
  'history': [],
  'geography': [],
  'culture': [],
  'arts': [],
  'philosophy': [],
  'politics': [],
  'sports': [],
  'medicine': [],
  'people': [],
  'events': [],
  'organizations': [],
  'mathematics': [],
  'art': [],
  'public health': []
};

// Create a special category for all articles
let specialCategories = {
  'all': []
};

function initBingopediaPage() {
  // Initialize sidebar navigation
  initSidebarNavigation();

  // Initialize article tabs
  initArticleTabs();

  try {
    // Load all articles metadata (no longer async)
    loadAllArticles();

    // Initialize featured articles
    initFeaturedArticles();

    // Initialize search
    initSearch();

    // Update sidebar categories
    updateSidebarCategories();

    // Update status bar
    updateStatusBar('Bingopedia loaded', 'Connected');
  } catch (error) {
    console.error('Error loading articles:', error);
    updateStatusBar('Error loading articles', 'Connected');
  }
}

// Initialize sidebar navigation
function initSidebarNavigation() {
  // Main page link
  document.getElementById('main-page-link').addEventListener('click', function(e) {
    e.preventDefault();
    showMainPage();
  });

  // Random article link
  document.getElementById('random-article-link').addEventListener('click', function(e) {
    e.preventDefault();
    showRandomArticle();
  });

  // All articles link
  document.getElementById('all-articles-link').addEventListener('click', function(e) {
    e.preventDefault();
    showAllArticles();
  });

  // We'll update category links after loading articles
}

// Initialize article tabs
function initArticleTabs() {
  const tabs = document.querySelectorAll('.article-tabs .tab');
  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // Remove active class from all tabs
      tabs.forEach(t => t.classList.remove('active'));

      // Add active class to clicked tab
      this.classList.add('active');

      // Handle tab click
      const tabName = this.getAttribute('data-tab');
      handleTabClick(tabName);
    });
  });
}

// Handle tab click
function handleTabClick(tabName) {
  // For demo purposes, only the article tab is functional
  if (tabName !== 'article') {
    alert(`The ${tabName} tab is not implemented in this demo.`);

    // Reset to article tab
    document.querySelector('.tab[data-tab="article"]').classList.add('active');
    document.querySelectorAll('.tab:not([data-tab="article"])').forEach(tab => {
      tab.classList.remove('active');
    });
  }
}

// Load all articles metadata - no dynamic loading for Unreal Engine compatibility
function loadAllArticles() {
  try {
    // Use preloaded index data instead of fetching
    if (!window.preloadedArticleIndex || !window.preloadedArticleIndex.articles) {
      throw new Error('Preloaded article index not found');
    }

    // Get articles from preloaded data
    allArticles = window.preloadedArticleIndex.articles;

    // Reset categories
    Object.keys(categories).forEach(key => {
      categories[key] = [];
    });

    // Reset special categories
    Object.keys(specialCategories).forEach(key => {
      specialCategories[key] = [];
    });

    // Add all articles to the 'all' special category
    specialCategories['all'] = [...allArticles];

    // Categorize articles into all categories
    allArticles.forEach(article => {
      if (article.categories && Array.isArray(article.categories)) {
        // For each article, add it to all its categories
        article.categories.forEach(category => {
          // Create the category if it doesn't exist
          if (!categories[category]) {
            categories[category] = [];
          }
          // Add the article to the category
          categories[category].push(article);
        });

        // If the article doesn't belong to any category, add it to a default category
        if (article.categories.length === 0) {
          const defaultCategory = determineDefaultCategory(article);
          if (!categories[defaultCategory]) {
            categories[defaultCategory] = [];
          }
          categories[defaultCategory].push(article);
          article.categories.push(defaultCategory);
        }
      } else {
        // If article has no categories, add it to a default category
        const defaultCategory = determineDefaultCategory(article);
        if (!categories[defaultCategory]) {
          categories[defaultCategory] = [];
        }
        categories[defaultCategory].push(article);

        // Initialize categories array if needed
        if (!article.categories) {
          article.categories = [];
        }

        // Add the default category to the article's categories
        if (!article.categories.includes(defaultCategory)) {
          article.categories.push(defaultCategory);
        }
      }
    });

    // Log category statistics for debugging
    console.log('Category statistics:');
    Object.keys(categories).forEach(category => {
      console.log(`${category}: ${categories[category].length} articles`);
    });

    console.log(`All articles: ${specialCategories['all'].length}`);

    return allArticles;
  } catch (error) {
    console.error('Error loading articles metadata:', error);
    // Create fallback data
    createFallbackData();
    return allArticles;
  }
}

// Determine a default category for an article based on its content
function determineDefaultCategory(article) {
  const title = article.title.toLowerCase();
  const description = article.description.toLowerCase();

  // Check for technology-related keywords
  if (title.includes('computer') || title.includes('internet') || title.includes('technology') ||
      description.includes('computer') || description.includes('internet') || description.includes('technology')) {
    return 'technology';
  }

  // Check for science-related keywords
  if (title.includes('science') || title.includes('physics') || title.includes('biology') ||
      description.includes('science') || description.includes('physics') || description.includes('biology')) {
    return 'science';
  }

  // Check for history-related keywords
  if (title.includes('history') || title.includes('ancient') || title.includes('revolution') ||
      description.includes('history') || description.includes('ancient') || description.includes('revolution')) {
    return 'history';
  }

  // Check for geography-related keywords
  if (title.includes('mountain') || title.includes('river') || title.includes('geography') ||
      description.includes('mountain') || description.includes('river') || description.includes('geography')) {
    return 'geography';
  }

  // Default to culture as a catch-all
  return 'culture';
}

// Create fallback data if the JSON file can't be loaded
function createFallbackData() {
  // Reset categories
  Object.keys(categories).forEach(key => {
    categories[key] = [];
  });

  // Reset special categories
  Object.keys(specialCategories).forEach(key => {
    specialCategories[key] = [];
  });

  allArticles = [
    {
      id: 'internet',
      title: 'Internet',
      description: 'A global system of interconnected computer networks.',
      categories: ['technology', 'science']
    },
    {
      id: 'world-wide-web',
      title: 'World Wide Web',
      description: 'An information system where documents and resources are identified by URLs.',
      categories: ['technology']
    },
    {
      id: 'computer',
      title: 'Computer',
      description: 'An electronic device that manipulates information or data.',
      categories: ['technology']
    },
    {
      id: 'artificial-intelligence',
      title: 'Artificial Intelligence',
      description: 'The simulation of human intelligence in machines.',
      categories: ['technology', 'science']
    },
    {
      id: 'solar-system',
      title: 'Solar System',
      description: 'The collection of planets, moons, and smaller objects that orbit the Sun.',
      categories: ['science']
    },
    {
      id: 'ancient-rome',
      title: 'Ancient Rome',
      description: 'A civilization that began on the Italian Peninsula and expanded to become one of the largest empires in the ancient world.',
      categories: ['history']
    },
    {
      id: 'mount-everest',
      title: 'Mount Everest',
      description: 'The highest mountain on Earth, located in the Mahalangur Himal sub-range of the Himalayas.',
      categories: ['geography']
    },
    {
      id: 'renaissance',
      title: 'Renaissance',
      description: 'A period in European history marking the transition from the Middle Ages to modernity.',
      categories: ['history', 'culture']
    },
    {
      id: 'jazz',
      title: 'Jazz',
      description: 'A music genre that originated in the African-American communities of New Orleans.',
      categories: ['culture']
    },
    {
      id: 'democracy',
      title: 'Democracy',
      description: 'A form of government in which the people have the authority to choose their governing legislators.',
      categories: ['history', 'culture']
    }
  ];

  // Add all articles to the 'all' special category
  specialCategories['all'] = [...allArticles];

  // Categorize articles into the main categories
  allArticles.forEach(article => {
    if (article.categories && Array.isArray(article.categories)) {
      // For each article, check if it belongs to any of our main categories
      article.categories.forEach(category => {
        if (categories[category]) {
          categories[category].push(article);
        }
      });
    }
  });

  console.log('Fallback data created with categories:', Object.keys(categories));
  console.log(`All articles in fallback: ${specialCategories['all'].length}`);
}

// Initialize featured articles
function initFeaturedArticles() {
  const featuredArticlesContainer = document.getElementById('featured-articles');

  if (!featuredArticlesContainer) return;

  // Get 4 random articles for featured section
  const featuredArticles = getRandomArticles(4);

  let html = '';
  featuredArticles.forEach(article => {
    html += `
      <div class="featured-article" data-article-id="${article.id}">
        <h3>${article.title}</h3>
        <p>${article.description}</p>
      </div>
    `;
  });

  featuredArticlesContainer.innerHTML = html;

  // Add event listeners to featured articles
  document.querySelectorAll('.featured-article').forEach(article => {
    article.addEventListener('click', function() {
      const articleId = this.getAttribute('data-article-id');
      if (articleId) {
        loadArticle(articleId);
      }
    });
  });
}

// Get random articles
function getRandomArticles(count) {
  const shuffled = [...allArticles].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// Initialize search
function initSearch() {
  const searchButton = document.getElementById('sidebar-search-button');
  const searchInput = document.getElementById('sidebar-search-input');

  // Search button click
  searchButton.addEventListener('click', function() {
    performSearch(searchInput.value);
  });

  // Enter key in search input
  searchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      performSearch(this.value);
    }
  });
}

// Perform search
function performSearch(query) {
  if (!query.trim()) {
    return;
  }

  console.log('Searching for:', query);
  updateStatusBar(`Searching for: ${query}`, 'Connected');

  // Simple search implementation
  const results = allArticles.filter(article =>
    article.title.toLowerCase().includes(query.toLowerCase()) ||
    article.description.toLowerCase().includes(query.toLowerCase())
  );

  if (results.length > 0) {
    displaySearchResults(results, query);
  } else {
    displayNoResults(query);
  }
}

// Display search results
function displaySearchResults(results, query) {
  const articleContent = document.getElementById('article-content');
  const articleTitle = document.getElementById('article-title');

  articleTitle.textContent = `Search Results: "${query}"`;

  let html = '<div class="search-results">';
  html += `<p>Found ${results.length} result(s) for "${query}":</p>`;
  html += '<ul class="search-results-list">';

  results.forEach(result => {
    html += `<li>
      <a href="#" class="search-result-link" data-article-id="${result.id}">
        ${result.title}
      </a> - ${result.description}
    </li>`;
  });

  html += '</ul></div>';

  articleContent.innerHTML = html;

  // Add event listeners to search result links
  document.querySelectorAll('.search-result-link').forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const articleId = this.getAttribute('data-article-id');
      if (articleId) {
        loadArticle(articleId);
      }
    });
  });

  updateStatusBar(`Found ${results.length} result(s) for "${query}"`, 'Connected');
}

// Display no results message
function displayNoResults(query) {
  const articleContent = document.getElementById('article-content');
  const articleTitle = document.getElementById('article-title');

  articleTitle.textContent = `Search Results: "${query}"`;

  articleContent.innerHTML = `
    <div class="no-results">
      <p>No results found for "${query}".</p>
      <p>Suggestions:</p>
      <ul>
        <li>Check your spelling</li>
        <li>Try more general keywords</li>
        <li>Try different keywords</li>
      </ul>
    </div>
  `;

  updateStatusBar(`No results found for "${query}"`, 'Connected');
}

// Show main page
function showMainPage() {
  const articleContent = document.getElementById('article-content');
  const articleTitle = document.getElementById('article-title');

  // Set title
  articleTitle.textContent = 'Welcome to Bingopedia';

  // Clear any existing content
  articleContent.innerHTML = '';

  // Recreate welcome message
  const welcomeHTML = `
    <div class="welcome-message" id="welcome-message">
      <p>Welcome to Bingopedia, the free encyclopedia that anyone can edit.</p>
      <p>Bingopedia is a multilingual online encyclopedia with exclusively free content, created through open collaboration.</p>
      <p>Select an article from the list below or use the search function to start exploring.</p>

      <h2>Featured Articles</h2>
      <div class="featured-articles" id="featured-articles">
        <!-- This will be populated by JavaScript -->
        <div class="loading-spinner">⟳</div>
        <p>Loading featured articles...</p>
      </div>
    </div>
  `;

  // Add welcome message to content area
  articleContent.innerHTML = welcomeHTML;

  // Make sure we have articles before initializing featured articles
  if (allArticles && allArticles.length > 0) {
    // Update featured articles with a slight delay to ensure DOM is ready
    setTimeout(() => {
      initFeaturedArticles();
    }, 100);
  } else {
    // If articles aren't loaded yet, load them (no longer async)
    try {
      loadAllArticles();
      initFeaturedArticles();
    } catch (error) {
      console.error('Error loading articles:', error);
    }
  }

  // Clear current article
  currentArticle = null;

  updateStatusBar('Welcome to Bingopedia', 'Connected');
}

// Show random article
function showRandomArticle() {
  if (allArticles.length > 0) {
    const randomIndex = Math.floor(Math.random() * allArticles.length);
    const randomArticle = allArticles[randomIndex];
    loadArticle(randomArticle.id);
  } else {
    alert('No articles available.');
  }
}

// Show all articles
function showAllArticles() {
  const articleContent = document.getElementById('article-content');
  const articleTitle = document.getElementById('article-title');

  articleTitle.textContent = 'All Articles';

  let html = '<div class="all-articles">';
  html += '<p>All available articles in Bingopedia:</p>';
  html += '<ul class="all-articles-list">';

  // Sort articles alphabetically by title
  const sortedArticles = [...allArticles].sort((a, b) => a.title.localeCompare(b.title));

  sortedArticles.forEach(article => {
    html += `<li>
      <a href="#" class="article-link" data-article-id="${article.id}">
        ${article.title}
      </a> - ${article.description}
    </li>`;
  });

  html += '</ul></div>';

  articleContent.innerHTML = html;

  // Add event listeners to article links
  document.querySelectorAll('.article-link').forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const articleId = this.getAttribute('data-article-id');
      if (articleId) {
        loadArticle(articleId);
      }
    });
  });

  updateStatusBar('All Articles', 'Connected');
}

// Show category articles
function showCategoryArticles(category) {
  const articleContent = document.getElementById('article-content');
  const articleTitle = document.getElementById('article-title');

  const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
  articleTitle.textContent = `Category: ${categoryName}`;

  // Make sure the category exists in our categories object
  if (!categories[category]) {
    categories[category] = [];
  }

  let html = `<div class="category-articles">`;
  html += `<p>Articles in category "${categoryName}":</p>`;

  // Debug information
  console.log(`Showing category: ${category}`);
  console.log(`Available categories:`, Object.keys(categories));

  // Get articles for this category
  const categoryArticles = categories[category] || [];

  if (categoryArticles.length > 0) {
    console.log(`Found ${categoryArticles.length} articles in category ${category}`);

    html += '<ul class="category-articles-list">';
    // Sort articles alphabetically by title
    const sortedArticles = [...categoryArticles].sort((a, b) => a.title.localeCompare(b.title));

    sortedArticles.forEach(article => {
      html += `<li>
        <a href="#" class="category-article-link" data-article-id="${article.id}">
          ${article.title}
        </a> - ${article.description}
      </li>`;
    });
    html += '</ul>';
  } else {
    console.warn(`No articles found in category: ${category}`);
    html += '<p>No articles found in this category.</p>';

    // Show all available categories as a fallback
    html += '<p>Available categories:</p><ul>';

    // Get categories with articles
    const categoriesWithArticles = Object.keys(categories)
      .filter(cat => categories[cat] && categories[cat].length > 0)
      .sort();

    categoriesWithArticles.forEach(cat => {
      const catName = cat.charAt(0).toUpperCase() + cat.slice(1);
      const articleCount = categories[cat].length;
      html += `<li><a href="#" class="category-link" data-category="${cat}">${catName} (${articleCount})</a></li>`;
    });

    html += '</ul>';
  }

  html += '</div>';

  articleContent.innerHTML = html;

  // Add event listeners to category article links
  document.querySelectorAll('.category-article-link').forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const articleId = this.getAttribute('data-article-id');
      if (articleId) {
        loadArticle(articleId);
      }
    });
  });

  // Add event listeners to category links (in case we're showing the fallback list)
  document.querySelectorAll('.category-link').forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const categoryId = this.getAttribute('data-category');
      if (categoryId) {
        showCategoryArticles(categoryId);
      }
    });
  });

  updateStatusBar(`Category: ${categoryName}`, 'Connected');
}

// Load article - optimized for performance
function loadArticle(articleId) {
  // Show loading indicator in popup
  const popup = document.getElementById('article-popup');
  const popupContent = document.getElementById('popup-article-content');
  const popupTitle = document.getElementById('popup-article-title');

  // Show the popup
  popup.classList.add('show');

  // Reset the popup scroll position
  const popupBody = document.querySelector('.article-popup-body');
  if (popupBody) {
    popupBody.scrollTop = 0;
  }

  // Set loading state
  popupContent.innerHTML = `
    <div class="loading-indicator">
      <div class="spinner">⟳</div>
      <p>Loading article...</p>
    </div>
  `;

  // Find article metadata
  const articleMeta = allArticles.find(article => article.id === articleId);

  if (!articleMeta) {
    popupContent.innerHTML = `<div class="error-message">Article not found: ${articleId}</div>`;
    return;
  }

  // Update popup title
  popupTitle.textContent = articleMeta.title;

  try {
    // Check if we already have the content in memory
    let articleContent = window.preloadedArticleContent && window.preloadedArticleContent[articleId];

    // If content is not preloaded, try to load it on demand
    if (!articleContent) {
      // For Unreal Engine compatibility, we'll use the preloaded content only
      throw new Error(`Article content not found for: ${articleId}`);
    }

    // Create a container for the content
    const formattedContent = document.createElement('div');
    formattedContent.className = 'bingopedia-article-content';

    // Use a more efficient approach to convert markdown to HTML
    // We'll process the content in chunks to avoid blocking the UI
    setTimeout(() => {
      // Simple conversion of markdown to HTML
      // Convert headers
      let htmlContent = articleContent
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^### (.*$)/gm, '<h3>$1</h3>')
        .replace(/^#### (.*$)/gm, '<h4>$1</h4>')
        // Convert bold
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // Convert italic
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // Convert links - simplified to just make them clickable
        .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="#$2">$1</a>')
        // Convert paragraphs (simple approach)
        .replace(/\n\n/g, '</p><p>')
        // Convert lists (simple approach)
        .replace(/^- (.*$)/gm, '<li>$1</li>');

      // Wrap in paragraphs
      htmlContent = '<p>' + htmlContent + '</p>';
      // Fix any double paragraph tags
      htmlContent = htmlContent.replace(/<\/p><p><\/p><p>/g, '</p><p>');

      formattedContent.innerHTML = `<div class="markdown-content">${htmlContent}</div>`;

      // Update popup content
      popupContent.innerHTML = '';
      popupContent.appendChild(formattedContent);

      // Reset scroll position to top
      popupContent.scrollTop = 0;

      // Add event listeners to article links in the popup
      addPopupArticleLinkListeners();

      // Update current article
      currentArticle = articleMeta;

      // Update article categories in popup
      updatePopupArticleCategories(articleMeta);

      // Update status bar
      updateStatusBar(`Reading: ${articleMeta.title}`, 'Connected');
    }, 10); // Small delay to allow the UI to update

  } catch (error) {
    console.error('Error loading article:', error);
    popupContent.innerHTML = `
      <div class="error-message">
        <p>Error loading article: ${error.message}</p>
        <p>This could be because the article doesn't exist yet.</p>
      </div>
    `;
  }
}

// This function is no longer used since we're doing the conversion directly in loadArticle
// Keeping it for reference
function processMarkdownLinks(markdown) {
  return markdown;
}

// Add event listeners to article links
function addArticleLinkListeners() {
  document.querySelectorAll('#article-content a').forEach(link => {
    const href = link.getAttribute('href');
    if (href && href.startsWith('#')) {
      const articleId = href.substring(1);
      link.addEventListener('click', function(e) {
        e.preventDefault();
        loadArticle(articleId);
      });
    }
  });
}

// Add event listeners to article links in popup
function addPopupArticleLinkListeners() {
  document.querySelectorAll('#popup-article-content a').forEach(link => {
    const href = link.getAttribute('href');
    if (href && href.startsWith('#')) {
      const articleId = href.substring(1);
      link.addEventListener('click', function(e) {
        e.preventDefault();
        loadArticle(articleId);
      });
    }
  });
}

// Update article categories
function updateArticleCategories(article) {
  const categoriesContainer = document.getElementById('article-categories');

  if (!categoriesContainer) return;

  if (article.categories && article.categories.length > 0) {
    const categoryLinks = article.categories.map(category => {
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
      return `<a href="#" class="category-link" data-category="${category}">${categoryName}</a>`;
    }).join(' | ');

    categoriesContainer.innerHTML = `<p>Categories: ${categoryLinks}</p>`;

    // Add event listeners to category links
    document.querySelectorAll('.category-link').forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const category = this.getAttribute('data-category');
        if (category) {
          showCategoryArticles(category);
        }
      });
    });
  } else {
    categoriesContainer.innerHTML = '';
  }
}

// Update article categories in popup
function updatePopupArticleCategories(article) {
  const categoriesContainer = document.getElementById('popup-article-categories');

  if (!categoriesContainer) return;

  if (article.categories && article.categories.length > 0) {
    const categoryLinks = article.categories.map(category => {
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
      return `<a href="#" class="popup-category-link" data-category="${category}">${categoryName}</a>`;
    }).join(' | ');

    categoriesContainer.innerHTML = `<p>Categories: ${categoryLinks}</p>`;

    // Add event listeners to category links
    document.querySelectorAll('.popup-category-link').forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const category = this.getAttribute('data-category');
        if (category) {
          // Close the popup
          closeArticlePopup();
          // Show category articles
          showCategoryArticles(category);
        }
      });
    });
  } else {
    categoriesContainer.innerHTML = '';
  }
}

// Close article popup
function closeArticlePopup() {
  const popup = document.getElementById('article-popup');
  popup.classList.remove('show');

  // Reset scroll positions for next time
  const popupBody = document.querySelector('.article-popup-body');
  if (popupBody) {
    popupBody.scrollTop = 0;
  }
}

// Update sidebar categories
function updateSidebarCategories() {
  const categoryLinksContainer = document.getElementById('category-links');

  if (!categoryLinksContainer) {
    console.error('Category links container not found');
    return;
  }

  // We'll keep the pre-rendered categories in the HTML
  // Just update the event listeners and article counts

  // Get all category links
  const categoryLinks = categoryLinksContainer.querySelectorAll('a[data-category]');

  // Add event listeners and update counts
  categoryLinks.forEach(link => {
    const category = link.getAttribute('data-category');

    // Make sure the category exists in our categories object
    if (!categories[category]) {
      categories[category] = [];
    }

    // Update the link text to include the article count
    const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
    const articleCount = categories[category] ? categories[category].length : 0;

    // Only update the text if there are articles in this category
    if (articleCount > 0) {
      link.textContent = `${categoryName} (${articleCount})`;
    } else {
      link.textContent = categoryName;
    }

    // Add event listener
    link.addEventListener('click', function(e) {
      e.preventDefault();
      showCategoryArticles(category);
    });
  });

  console.log(`Updated sidebar with ${categoryLinks.length} categories`);

  // Add any missing categories that have articles
  const existingCategories = Array.from(categoryLinks).map(link =>
    link.getAttribute('data-category')
  );

  const missingCategories = Object.keys(categories)
    .filter(category =>
      categories[category] &&
      categories[category].length > 0 &&
      !existingCategories.includes(category)
    )
    .sort();

  if (missingCategories.length > 0) {
    console.log(`Adding ${missingCategories.length} missing categories to sidebar`);

    missingCategories.forEach(category => {
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
      const articleCount = categories[category].length;

      const listItem = document.createElement('li');
      listItem.innerHTML = `<a href="#" data-category="${category}">${categoryName} (${articleCount})</a>`;

      // Add event listener
      listItem.querySelector('a').addEventListener('click', function(e) {
        e.preventDefault();
        showCategoryArticles(category);
      });

      categoryLinksContainer.appendChild(listItem);
    });
  }
}


