// Finance page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize finance page
  initFinancePage();
});

function initFinancePage() {
  // Initialize market indices
  initMarketIndices();

  // Initialize stock watchlist
  initStockWatchlist();

  // Initialize currency converter
  initCurrencyConverter();

  // Initialize IPO Calendar
  initIPOCalendar();

  // Initialize Market Movers
  initMarketMovers();

  // Initialize Commodity Prices
  initCommodityPrices();

  // Initialize Mutual Funds
  initMutualFunds();

  // Initialize news items
  initNewsItems();

  // Update status bar
  updateStatusBar('Finance page loaded', 'Connected');
}

// Market Indices Functions
function initMarketIndices() {
  const marketIndices = document.querySelectorAll('.market-index');
  let refreshInterval;

  // Add click event to market indices
  marketIndices.forEach(index => {
    index.addEventListener('click', function() {
      const indexName = index.querySelector('.index-name').textContent;

      // Update status bar
      updateStatusBar('Loading details for ' + indexName, 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('Details loaded for ' + indexName, 'Connected');
      }, 300);
    });
  });

  // Function to refresh market indices
  function refreshMarketIndices() {
    // Randomly update market indices
    marketIndices.forEach(index => {
      // 50% chance to update each index
      if (Math.random() > 0.5) {
        const indexValueElement = index.querySelector('.index-value');
        const indexChangeElement = index.querySelector('.index-change');

        // Get current value without the commas
        const currentValue = parseFloat(indexValueElement.textContent.replace(',', ''));

        // Generate a random change between -1% and +1%
        const changePercent = (Math.random() * 2 - 1) / 100;
        const changeAmount = currentValue * changePercent;
        const newValue = currentValue + changeAmount;

        // Update the value element
        indexValueElement.textContent = newValue.toLocaleString('en-IN', {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2
        });

        // Get current change value and percentage
        const currentChangeText = indexChangeElement.textContent;
        const currentChangeMatch = currentChangeText.match(/([+-])([\d,.]+) \(([+-])([\d.]+)%\)/);

        if (currentChangeMatch) {
          // Extract current change percentage
          const currentChangePercent = parseFloat(currentChangeMatch[4]);

          // Calculate new change percentage
          const newChangePercent = currentChangePercent + (changePercent * 100);

          // Calculate new change amount
          const newChangeAmount = (currentValue * newChangePercent) / 100;

          // Update the change element
          const newChangeText = (newChangeAmount >= 0 ? '+' : '') +
            newChangeAmount.toLocaleString('en-IN', {
              maximumFractionDigits: 2,
              minimumFractionDigits: 2
            }) +
            ' (' + (newChangePercent >= 0 ? '+' : '') +
            newChangePercent.toFixed(2) + '%)';

          indexChangeElement.textContent = newChangeText;

          // Update the class based on whether it's positive or negative
          if (newChangeAmount >= 0) {
            indexChangeElement.className = 'index-change positive';
          } else {
            indexChangeElement.className = 'index-change negative';
          }
        }
      }
    });
  }

  // Set up auto-refresh interval (every 2 seconds)
  refreshInterval = setInterval(refreshMarketIndices, 2000);

  // Initial refresh
  refreshMarketIndices();
}

// Stock Watchlist Functions
function initStockWatchlist() {
  const stockRows = document.querySelectorAll('.stock-table tbody tr');
  const watchlistSelect = document.querySelector('.watchlist-select');
  let refreshInterval;

  // Add click event to stock rows
  stockRows.forEach(row => {
    row.addEventListener('click', function() {
      const stockSymbol = this.cells[0].textContent;
      const companyName = this.cells[1].textContent;

      // Update status bar
      updateStatusBar('Loading details for ' + companyName + ' (' + stockSymbol + ')', 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('Details loaded for ' + companyName, 'Connected');
      }, 300);
    });
  });

  // Function to refresh the watchlist
  function refreshWatchlist() {
    // Randomly update some stock prices and changes
    stockRows.forEach(row => {
      // 50% chance to update each stock
      if (Math.random() > 0.5) {
        const priceCell = row.cells[2];
        const changeCell = row.cells[3];

        // Get current price without the ₹ symbol
        const currentPrice = parseFloat(priceCell.textContent.replace('₹', '').replace(',', ''));

        // Generate a random change between -2% and +2%
        const changePercent = (Math.random() * 4 - 2) / 100;
        const changeAmount = currentPrice * changePercent;
        const newPrice = currentPrice + changeAmount;

        // Update the price cell
        priceCell.textContent = '₹' + newPrice.toLocaleString('en-IN', {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2
        });

        // Update the change cell
        const changeText = (changeAmount >= 0 ? '+' : '') +
          changeAmount.toLocaleString('en-IN', {
            maximumFractionDigits: 2,
            minimumFractionDigits: 2
          }) +
          ' (' + (changePercent >= 0 ? '+' : '') +
          (changePercent * 100).toFixed(2) + '%)';

        changeCell.textContent = changeText;

        // Update the class based on whether it's positive or negative
        if (changeAmount >= 0) {
          changeCell.className = 'positive';
        } else {
          changeCell.className = 'negative';
        }
      }
    });

    // Update status bar briefly
    updateStatusBar('Watchlist refreshed', 'Connected');

    // Reset status bar after a short delay
    setTimeout(() => {
      updateStatusBar('Connected', 'Connected');
    }, 500);
  }

  // Set up auto-refresh interval (every 2 seconds)
  refreshInterval = setInterval(refreshWatchlist, 2000);

  // Add change event to watchlist select
  if (watchlistSelect) {
    watchlistSelect.addEventListener('change', function() {
      const selectedWatchlist = this.value;
      updateStatusBar('Loading ' + selectedWatchlist + ' watchlist...', 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar(selectedWatchlist + ' watchlist loaded', 'Connected');
      }, 300);
    });
  }

  // Initial refresh
  refreshWatchlist();
}

// Currency Converter Functions
function initCurrencyConverter() {
  const convertButton = document.getElementById('convert-button');
  const amountInput = document.getElementById('amount');
  const fromCurrency = document.getElementById('from-currency');
  const toCurrency = document.getElementById('to-currency');
  const resultElement = document.getElementById('conversion-result');

  // Exchange rates from 2010
  const rates = {
    'USD': 1,
    'EUR': 0.75,  // 1 USD = 0.75 EUR in 2010
    'GBP': 0.65,  // 1 USD = 0.65 GBP in 2010
    'JPY': 90.45, // 1 USD = 90.45 JPY in 2010
    'INR': 45.75  // 1 USD = 45.75 INR in 2010
  };

  // Function to perform the conversion
  function performConversion() {
    if (amountInput && fromCurrency && toCurrency && resultElement) {
      const amount = parseFloat(amountInput.value);
      const from = fromCurrency.value;
      const to = toCurrency.value;

      if (isNaN(amount)) {
        resultElement.textContent = 'Please enter a valid amount';
        return;
      }

      const result = amount * (rates[to] / rates[from]);

      resultElement.innerHTML = `
        <div class="result-amount">${amount} ${from} = ${result.toFixed(4)} ${to}</div>
        <div class="result-details">
          <div class="result-date">Exchange rate as of May 15, 2010</div>
          <div class="result-source">Source: Bingo Finance</div>
        </div>
      `;

      // Update status bar
      updateStatusBar('Conversion completed', 'Connected');
    }
  }

  // Add event listener to the convert button
  if (convertButton) {
    convertButton.addEventListener('click', function(event) {
      event.preventDefault();
      performConversion();
    });
  }

  // No automatic updates - only convert when button is clicked
}

// IPO Calendar Functions
function initIPOCalendar() {
  const ipoRows = document.querySelectorAll('.ipo-table tbody tr');

  ipoRows.forEach(row => {
    row.addEventListener('click', function() {
      const companyName = this.cells[0].textContent;

      // Update status bar
      updateStatusBar('Loading IPO details for ' + companyName, 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('IPO details loaded for ' + companyName, 'Connected');
      }, 300);
    });
  });
}

// Market Movers Functions
function initMarketMovers() {
  const gainersRows = document.querySelectorAll('.top-gainers .movers-table tbody tr');
  const losersRows = document.querySelectorAll('.top-losers .movers-table tbody tr');

  // Add click event to gainers rows
  gainersRows.forEach(row => {
    row.addEventListener('click', function() {
      const companyName = this.cells[0].textContent;

      // Update status bar
      updateStatusBar('Loading details for ' + companyName, 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('Details loaded for ' + companyName, 'Connected');
      }, 300);
    });
  });

  // Add click event to losers rows
  losersRows.forEach(row => {
    row.addEventListener('click', function() {
      const companyName = this.cells[0].textContent;

      // Update status bar
      updateStatusBar('Loading details for ' + companyName, 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('Details loaded for ' + companyName, 'Connected');
      }, 300);
    });
  });
}

// Commodity Prices Functions
function initCommodityPrices() {
  const commodityItems = document.querySelectorAll('.commodity-item');

  commodityItems.forEach(item => {
    item.addEventListener('click', function() {
      const commodityName = item.querySelector('.commodity-name').textContent;

      // Update status bar
      updateStatusBar('Loading details for ' + commodityName, 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('Details loaded for ' + commodityName, 'Connected');
      }, 300);
    });
  });
}

// Mutual Funds Functions
function initMutualFunds() {
  const fundRows = document.querySelectorAll('.mutual-funds-table tbody tr');

  fundRows.forEach(row => {
    row.addEventListener('click', function() {
      const fundName = this.cells[0].textContent;

      // Update status bar
      updateStatusBar('Loading details for ' + fundName, 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('Details loaded for ' + fundName, 'Connected');
      }, 300);
    });
  });
}

// News Items Functions
function initNewsItems() {
  const newsItems = document.querySelectorAll('.news-item');

  newsItems.forEach(item => {
    item.addEventListener('click', function() {
      const newsTitle = item.querySelector('.news-title').textContent;

      // Update status bar
      updateStatusBar('Loading article: ' + newsTitle, 'Connected');

      // Simulate loading delay
      setTimeout(() => {
        updateStatusBar('Article loaded: ' + newsTitle, 'Connected');
      }, 300);
    });
  });
}
