// This script will replace the status-bar-container with the actual status bar HTML
document.addEventListener('DOMContentLoaded', function() {
  const statusBarContainer = document.getElementById('status-bar-container');

  if (statusBarContainer) {
    // Set the status bar HTML
    statusBarContainer.innerHTML = `
    <div class="status-bar">
      <div class="status-text">Ready</div>
      <div class="connection-status">Connected</div>
      <div class="console-button">Console</div>
    </div>
    `;

    // Add click event listener to the console button
    const consoleButton = document.querySelector('.console-button');
    if (consoleButton) {
      consoleButton.addEventListener('click', function() {
        console.log("123 IT WORKS");
      });
    }
  }
});

// Status Bar Functions - Moved from browser.js
function updateStatusBar(status, connection) {
  const statusText = document.querySelector('.status-text');
  const connectionStatus = document.querySelector('.connection-status');

  if (statusText) {
    statusText.textContent = status;
  }

  if (connectionStatus) {
    connectionStatus.textContent = connection;
  }

  // Make sure console button exists and has click handler
  const consoleButton = document.querySelector('.console-button');
  if (consoleButton && !consoleButton.hasClickListener) {
    consoleButton.addEventListener('click', function() {
      console.log("123 IT WORKS");
    });
    consoleButton.hasClickListener = true;
  }
}
