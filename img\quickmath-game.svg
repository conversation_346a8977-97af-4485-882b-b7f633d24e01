<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="200" fill="#F1EFE2"/>
  
  <!-- Background elements -->
  <rect x="20" y="30" width="160" height="140" rx="5" ry="5" fill="#FFFFFF" stroke="#ACA899" stroke-width="2"/>
  
  <!-- Math symbols -->
  <g transform="translate(40, 60)">
    <!-- Plus symbol -->
    <rect x="0" y="0" width="40" height="40" rx="5" ry="5" fill="#3A6EA5" stroke="#0A246A" stroke-width="1"/>
    <text x="20" y="28" font-family="Tahoma, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">+</text>
    
    <!-- Minus symbol -->
    <rect x="50" y="0" width="40" height="40" rx="5" ry="5" fill="#CC0000" stroke="#800000" stroke-width="1"/>
    <text x="70" y="28" font-family="Tahoma, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">-</text>
    
    <!-- Multiply symbol -->
    <rect x="100" y="0" width="40" height="40" rx="5" ry="5" fill="#008000" stroke="#006000" stroke-width="1"/>
    <text x="120" y="28" font-family="Tahoma, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">×</text>
    
    <!-- Divide symbol -->
    <rect x="25" y="50" width="40" height="40" rx="5" ry="5" fill="#800080" stroke="#600060" stroke-width="1"/>
    <text x="45" y="78" font-family="Tahoma, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">÷</text>
    
    <!-- Equals symbol -->
    <rect x="75" y="50" width="40" height="40" rx="5" ry="5" fill="#FF7F00" stroke="#CC6600" stroke-width="1"/>
    <text x="95" y="78" font-family="Tahoma, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">=</text>
  </g>
  
  <!-- Math equation -->
  <rect x="30" y="120" width="140" height="40" rx="5" ry="5" fill="#F9F9F9" stroke="#ACA899" stroke-width="1"/>
  <text x="100" y="147" font-family="Tahoma, sans-serif" font-size="20" font-weight="bold" fill="#0A246A" text-anchor="middle">12 + 8 = 20</text>
  
  <!-- Timer -->
  <circle cx="100" cy="30" r="15" fill="#F9F9F9" stroke="#ACA899" stroke-width="1"/>
  <text x="100" y="36" font-family="Tahoma, sans-serif" font-size="14" font-weight="bold" fill="#0A246A" text-anchor="middle">30</text>
</svg>
