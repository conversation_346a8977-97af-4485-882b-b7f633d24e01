// Preloaded article index for Bingopedia
// This file is auto-generated - do not edit manually
window.preloadedArticleIndex = {
  "articles": [
    {
      "id": "internet",
      "title": "Internet",
      "description": "A global system of interconnected computer networks.",
      "categories": [
        "technology",
        "science"
      ]
    },
    {
      "id": "world-wide-web",
      "title": "World Wide Web",
      "description": "An information system where documents and resources are identified by URLs.",
      "categories": [
        "technology"
      ]
    },
    {
      "id": "computer",
      "title": "Computer",
      "description": "An electronic device that manipulates information or data.",
      "categories": [
        "technology"
      ]
    },
    {
      "id": "artificial-intelligence",
      "title": "Artificial Intelligence",
      "description": "The simulation of human intelligence in machines.",
      "categories": [
        "technology",
        "science"
      ]
    },
    {
      "id": "solar-system",
      "title": "Solar System",
      "description": "The collection of planets, moons, and smaller objects that orbit the Sun.",
      "categories": [
        "science"
      ]
    },
    {
      "id": "ancient-rome",
      "title": "Ancient Rome",
      "description": "A civilization that began on the Italian Peninsula and expanded to become one of the largest empires in the ancient world.",
      "categories": [
        "history"
      ]
    },
    {
      "id": "mount-everest",
      "title": "Mount Everest",
      "description": "The highest mountain on Earth, located in the Mahalangur Himal sub-range of the Himalayas.",
      "categories": [
        "geography"
      ]
    },
    {
      "id": "renaissance",
      "title": "Renaissance",
      "description": "A period in European history marking the transition from the Middle Ages to modernity.",
      "categories": [
        "history",
        "culture"
      ]
    },
    {
      "id": "jazz",
      "title": "Jazz",
      "description": "A music genre that originated in the African-American communities of New Orleans.",
      "categories": [
        "culture"
      ]
    },
    {
      "id": "democracy",
      "title": "Democracy",
      "description": "A form of government in which the people have the authority to choose their governing legislators.",
      "categories": [
        "history",
        "culture"
      ]
    },
    {
      "id": "photography",
      "title": "Photography",
      "description": "The art, science, and practice of creating durable images by recording light.",
      "categories": [
        "technology",
        "culture"
      ]
    },
    {
      "id": "climate-change",
      "title": "Climate Change",
      "description": "Long-term shifts in temperatures and weather patterns, primarily caused by human activities.",
      "categories": [
        "science",
        "geography"
      ]
    },
    {
      "id": "quantum-physics",
      "title": "Quantum Physics",
      "description": "A fundamental theory in physics that describes nature at the smallest scales of energy levels of atoms and subatomic particles.",
      "categories": [
        "science"
      ]
    },
    {
      "id": "taj-mahal",
      "title": "Taj Mahal",
      "description": "An ivory-white marble mausoleum on the right bank of the river Yamuna in Agra, India.",
      "categories": [
        "geography",
        "history",
        "culture"
      ]
    },
    {
      "id": "blockchain",
      "title": "Blockchain",
      "description": "A distributed database or ledger shared among computer network nodes.",
      "categories": [
        "technology"
      ]
    },
    {
      "id": "french-revolution",
      "title": "French Revolution",
      "description": "A period of radical political and societal change in France that began with the Estates General of 1789.",
      "categories": [
        "history"
      ]
    },
    {
      "id": "human-genome",
      "title": "Human Genome",
      "description": "The complete set of nucleic acid sequences for humans, encoded as DNA within the 23 chromosome pairs.",
      "categories": [
        "science"
      ]
    },
    {
      "id": "impressionism",
      "title": "Impressionism",
      "description": "A 19th-century art movement characterized by small, thin, yet visible brush strokes and emphasis on light.",
      "categories": [
        "culture",
        "arts"
      ]
    },
    {
      "id": "great-barrier-reef",
      "title": "Great Barrier Reef",
      "description": "The world's largest coral reef system composed of over 2,900 individual reefs and 900 islands.",
      "categories": [
        "geography",
        "science"
      ]
    },
    {
      "id": "industrial-revolution",
      "title": "Industrial Revolution",
      "description": "The transition to new manufacturing processes in Europe and the United States from about 1760 to 1840.",
      "categories": [
        "history",
        "technology"
      ]
    },
    {
      "id": "plato",
      "title": "Plato",
      "description": "An ancient Greek philosopher, the founder of the Academy in Athens, and one of the most influential figures in Western philosophy.",
      "categories": [
        "philosophy",
        "history"
      ]
    },
    {
      "id": "democracy-in-america",
      "title": "Democracy in America",
      "description": "A classic French text by Alexis de Tocqueville on the United States in the 1830s and its democratic institutions.",
      "categories": [
        "politics",
        "history"
      ]
    },
    {
      "id": "olympic-games",
      "title": "Olympic Games",
      "description": "A leading international sporting event featuring summer and winter sports competitions in which thousands of athletes participate.",
      "categories": [
        "sports",
        "culture"
      ]
    },
    {
      "id": "penicillin",
      "title": "Penicillin",
      "description": "A group of antibiotics derived from Penicillium fungi, the first antibiotic discovered that effectively treated bacterial infections.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "beethoven",
      "title": "Ludwig van Beethoven",
      "description": "A German composer and pianist who was a crucial figure in the transition between the Classical and Romantic eras in Western music.",
      "categories": [
        "arts",
        "culture"
      ]
    },
    {
      "id": "aristotle",
      "title": "Aristotle",
      "description": "An ancient Greek philosopher and polymath who made significant contributions to logic, metaphysics, mathematics, physics, biology, and ethics.",
      "categories": [
        "philosophy",
        "science"
      ]
    },
    {
      "id": "united-nations",
      "title": "United Nations",
      "description": "An international organization founded in 1945 to promote international cooperation and to create and maintain international order.",
      "categories": [
        "politics",
        "history"
      ]
    },
    {
      "id": "world-cup",
      "title": "FIFA World Cup",
      "description": "An international football competition contested by the senior men's national teams of the members of FIFA.",
      "categories": [
        "sports"
      ]
    },
    {
      "id": "vaccination",
      "title": "Vaccination",
      "description": "The administration of a vaccine to help the immune system develop protection from a disease.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "amazon-rainforest",
      "title": "Amazon Rainforest",
      "description": "The world's largest tropical rainforest, covering much of northwestern Brazil and extending into Colombia, Peru, and other South American countries.",
      "categories": [
        "geography",
        "science"
      ]
    },
    {
      "id": "himalayas",
      "title": "Himalayas",
      "description": "A mountain range in Asia separating the plains of the Indian subcontinent from the Tibetan Plateau, home to the world's highest peaks.",
      "categories": [
        "geography"
      ]
    },
    {
      "id": "grand-canyon",
      "title": "Grand Canyon",
      "description": "A steep-sided canyon carved by the Colorado River in Arizona, United States, renowned for its size and colorful landscape.",
      "categories": [
        "geography"
      ]
    },
    {
      "id": "venice",
      "title": "Venice",
      "description": "A city in northeastern Italy built on a group of 118 small islands separated by canals and linked by over 400 bridges.",
      "categories": [
        "geography",
        "culture",
        "history"
      ]
    },
    {
      "id": "shakespeare",
      "title": "William Shakespeare",
      "description": "An English poet, playwright, and actor, widely regarded as the greatest writer in the English language and the world's greatest dramatist.",
      "categories": [
        "arts",
        "culture"
      ]
    },
    {
      "id": "van-gogh",
      "title": "Vincent van Gogh",
      "description": "A Dutch post-impressionist painter who posthumously became one of the most famous and influential figures in Western art history.",
      "categories": [
        "arts"
      ]
    },
    {
      "id": "ballet",
      "title": "Ballet",
      "description": "A type of performance dance that originated during the Italian Renaissance in the 15th century and later developed into a concert dance form in France and Russia.",
      "categories": [
        "arts",
        "culture"
      ]
    },
    {
      "id": "cinema",
      "title": "Cinema",
      "description": "The art and industry of motion-picture making, including the technology of cinematography and the creative processes of film direction and acting.",
      "categories": [
        "arts",
        "technology",
        "culture"
      ]
    },
    {
      "id": "socrates",
      "title": "Socrates",
      "description": "A classical Greek philosopher credited as one of the founders of Western philosophy and the first moral philosopher of the Western ethical tradition of thought.",
      "categories": [
        "philosophy",
        "history"
      ]
    },
    {
      "id": "confucius",
      "title": "Confucius",
      "description": "A Chinese philosopher and politician of the Spring and Autumn period who was traditionally considered the paragon of Chinese sages.",
      "categories": [
        "philosophy",
        "history"
      ]
    },
    {
      "id": "immanuel-kant",
      "title": "Immanuel Kant",
      "description": "An influential German philosopher in the Age of Enlightenment who argued that fundamental concepts structure human experience and that reason is the source of morality.",
      "categories": [
        "philosophy"
      ]
    },
    {
      "id": "existentialism",
      "title": "Existentialism",
      "description": "A philosophical theory that emphasizes the existence of the individual person as a free and responsible agent determining their own development through acts of the will.",
      "categories": [
        "philosophy"
      ]
    },
    {
      "id": "capitalism",
      "title": "Capitalism",
      "description": "An economic system based on the private ownership of the means of production and their operation for profit.",
      "categories": [
        "politics",
        "history"
      ]
    },
    {
      "id": "socialism",
      "title": "Socialism",
      "description": "A political, social, and economic philosophy encompassing a range of economic and social systems characterized by social ownership of the means of production.",
      "categories": [
        "politics",
        "history"
      ]
    },
    {
      "id": "european-union",
      "title": "European Union",
      "description": "A political and economic union of member states that are located primarily in Europe, established after World War II to foster economic cooperation.",
      "categories": [
        "politics",
        "geography"
      ]
    },
    {
      "id": "cold-war",
      "title": "Cold War",
      "description": "A period of geopolitical tension between the Soviet Union and the United States and their respective allies that began following World War II.",
      "categories": [
        "politics",
        "history"
      ]
    },
    {
      "id": "cricket",
      "title": "Cricket",
      "description": "A bat-and-ball game played between two teams of eleven players on a field with a 22-yard pitch in the center and two wickets at each end.",
      "categories": [
        "sports"
      ]
    },
    {
      "id": "basketball",
      "title": "Basketball",
      "description": "A team sport in which two teams, most commonly of five players each, opposing one another on a rectangular court, compete to get the ball through the opponent's hoop.",
      "categories": [
        "sports"
      ]
    },
    {
      "id": "tennis",
      "title": "Tennis",
      "description": "A racket sport that can be played individually against a single opponent or between two teams of two players each.",
      "categories": [
        "sports"
      ]
    },
    {
      "id": "formula-one",
      "title": "Formula One",
      "description": "The highest class of international auto racing for single-seater formula racing cars sanctioned by the Fédération Internationale de l'Automobile.",
      "categories": [
        "sports",
        "technology"
      ]
    },
    {
      "id": "antibiotics",
      "title": "Antibiotics",
      "description": "A type of antimicrobial substance active against bacteria that has revolutionized medicine by making previously untreatable infections curable.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "surgery",
      "title": "Surgery",
      "description": "A medical specialty that uses operative manual and instrumental techniques on a person to investigate or treat a pathological condition.",
      "categories": [
        "medicine"
      ]
    },
    {
      "id": "cancer",
      "title": "Cancer",
      "description": "A group of diseases involving abnormal cell growth with the potential to invade or spread to other parts of the body.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "heart-disease",
      "title": "Heart Disease",
      "description": "A class of diseases that involve the heart or blood vessels, including coronary artery disease, heart rhythm problems, and congenital heart defects.",
      "categories": [
        "medicine"
      ]
    },
    {
      "id": "mental-health",
      "title": "Mental Health",
      "description": "A person's condition with regard to their psychological and emotional well-being, affecting how they think, feel, and act.",
      "categories": [
        "medicine"
      ]
    },
    {
      "id": "sports",
      "title": "Sports",
      "description": "Competitive physical activities or games that aim to use, maintain, or improve physical ability and skills while providing enjoyment to participants.",
      "categories": [
        "culture"
      ]
    },
    {
      "id": "hypertension",
      "title": "Hypertension",
      "description": "A long-term medical condition in which the blood pressure in the arteries is persistently elevated, increasing risk for heart disease and stroke.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "cholesterol",
      "title": "Cholesterol",
      "description": "A waxy, fat-like substance found in all cells of the body, essential for many bodily functions but linked to cardiovascular disease when elevated.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "diabetes",
      "title": "Diabetes",
      "description": "A group of metabolic disorders characterized by high blood sugar levels over a prolonged period, affecting millions worldwide.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "stroke",
      "title": "Stroke",
      "description": "A medical condition in which poor blood flow to the brain causes cell death, requiring immediate treatment to minimize brain damage.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "cardiology",
      "title": "Cardiology",
      "description": "A branch of medicine that deals with disorders of the heart and the cardiovascular system, including diagnosis and treatment of various heart conditions.",
      "categories": [
        "medicine",
        "science"
      ]
    },
    {
      "id": "television",
      "title": "Television",
      "description": "A telecommunication medium for transmitting moving images and sound, playing a crucial role in shaping modern culture, politics, and society.",
      "categories": [
        "technology",
        "culture"
      ]
    },
    {
      "id": "animation",
      "title": "Animation",
      "description": "A method of creating moving images through a series of drawings, computer-generated images, or photographs of objects.",
      "categories": [
        "technology",
        "art",
        "culture"
      ]
    },
    {
      "id": "hollywood",
      "title": "Hollywood",
      "description": "A neighborhood in Los Angeles, California that has become synonymous with the American film industry and its global cultural influence.",
      "categories": [
        "culture",
        "geography"
      ]
    },
    {
      "id": "film-directors",
      "title": "Film Directors",
      "description": "Creative visionaries responsible for guiding all artistic aspects of motion pictures, from working with actors to overseeing cinematography.",
      "categories": [
        "art",
        "culture"
      ]
    },
    {
      "id": "film-genres",
      "title": "Film Genres",
      "description": "Categories that classify movies based on their narrative elements, settings, themes, moods, and presentation styles.",
      "categories": [
        "art",
        "culture"
      ]
    },
    {
      "id": "alexander-fleming",
      "title": "Alexander Fleming",
      "description": "Scottish physician, microbiologist, and pharmacologist who discovered penicillin, revolutionizing modern medicine and earning the Nobel Prize.",
      "categories": [
        "science",
        "medicine",
        "history"
      ]
    },
    {
      "id": "bacterial-infections",
      "title": "Bacterial Infections",
      "description": "Infections caused by pathogenic bacteria that invade the body, multiply, and cause tissue damage through direct effects or toxin production.",
      "categories": [
        "science",
        "medicine"
      ]
    },
    {
      "id": "antibiotic-resistance",
      "title": "Antibiotic Resistance",
      "description": "The ability of bacteria to survive and multiply in the presence of antibiotics that would normally kill them or inhibit their growth.",
      "categories": [
        "science",
        "medicine",
        "public health"
      ]
    },
    {
      "id": "alexander-the-great",
      "title": "Alexander the Great",
      "description": "Ancient Macedonian king who created one of history's largest empires, stretching from Greece to northwestern India, and spread Hellenistic culture throughout his conquests.",
      "categories": [
        "history"
      ]
    },
    {
      "id": "ancient-greek-philosophy",
      "title": "Ancient Greek Philosophy",
      "description": "Philosophical traditions that emerged in ancient Greece, establishing methods and questions that have remained central to Western philosophy.",
      "categories": [
        "philosophy",
        "history"
      ]
    },
    {
      "id": "ethics",
      "title": "Ethics",
      "description": "Branch of philosophy concerned with systematizing, defending, and recommending concepts of right and wrong behavior.",
      "categories": [
        "philosophy"
      ]
    },
    {
      "id": "logic",
      "title": "Logic",
      "description": "The systematic study of valid inference and reasoning, investigating the structure of statements and arguments in formal and informal contexts.",
      "categories": [
        "philosophy",
        "mathematics"
      ]
    },
    {
      "id": "ancient-greece",
      "title": "Ancient Greece",
      "description": "Civilization that flourished from the 8th century BCE to the 6th century CE, making profound contributions to philosophy, mathematics, art, and politics.",
      "categories": [
        "history"
      ]
    },
    {
      "id": "football",
      "title": "Football",
      "description": "Team sport played between two teams of eleven players with a spherical ball, also known as soccer in some countries.",
      "categories": [
        "sports"
      ]
    },
    {
      "id": "fifa",
      "title": "FIFA",
      "description": "International governing body of association football, futsal, and beach soccer, responsible for organizing major tournaments including the World Cup.",
      "categories": [
        "sports",
        "organizations"
      ]
    },
    {
      "id": "wimbledon",
      "title": "Wimbledon",
      "description": "The oldest tennis tournament in the world, held annually at the All England Club in London, known for its grass courts and prestigious status as a Grand Slam event.",
      "categories": [
        "sports",
        "events"
      ]
    },
    {
      "id": "roger-federer",
      "title": "Roger Federer",
      "description": "Swiss former professional tennis player widely regarded as one of the greatest players in the history of the sport, with 20 Grand Slam singles titles.",
      "categories": [
        "sports",
        "people"
      ]
    },
    {
      "id": "lionel-messi",
      "title": "Lionel Messi",
      "description": "Argentine professional footballer widely regarded as one of the greatest players of all time, known for his exceptional dribbling, playmaking, and goal-scoring abilities.",
      "categories": [
        "sports",
        "people"
      ]
    },
    {
      "id": "serena-williams",
      "title": "Serena Williams",
      "description": "American former professional tennis player widely regarded as one of the greatest players of all time, with 23 Grand Slam singles titles in her career.",
      "categories": [
        "sports",
        "people"
      ]
    },
    {
      "id": "diego-maradona",
      "title": "Diego Maradona",
      "description": "Argentine football player and manager widely regarded as one of the greatest players in the history of the sport, known for leading Argentina to victory in the 1986 World Cup.",
      "categories": [
        "sports",
        "people"
      ]
    },
    {
      "id": "grand-slam-tennis",
      "title": "Grand Slam (Tennis)",
      "description": "The four most prestigious annual tennis tournaments: the Australian Open, the French Open, Wimbledon, and the US Open.",
      "categories": [
        "sports",
        "events"
      ]
    },
    {
      "id": "pele",
      "title": "Pelé",
      "description": "Brazilian football player widely regarded as one of the greatest players of all time, known for winning three FIFA World Cups and scoring over 1,000 career goals.",
      "categories": [
        "sports",
        "people"
      ]
    },
    {
      "id": "colorado-river",
      "title": "Colorado River",
      "description": "A major river in the southwestern United States and northwestern Mexico, known for carving the Grand Canyon and providing water to seven U.S. states.",
      "categories": [
        "geography"
      ]
    },
    {
      "id": "national-parks",
      "title": "National Parks",
      "description": "Protected areas of natural beauty, unique geological features, diverse ecosystems, or historic significance that are set aside and managed by national governments for conservation and public enjoyment.",
      "categories": [
        "geography",
        "conservation"
      ]
    },
    {
      "id": "geology",
      "title": "Geology",
      "description": "The scientific study of the Earth, its composition, structure, physical properties, history, and the processes that shape it.",
      "categories": [
        "science"
      ]
    },
    {
      "id": "arizona",
      "title": "Arizona",
      "description": "A state located in the southwestern region of the United States, known for its desert landscape, diverse climate zones, and the Grand Canyon.",
      "categories": [
        "geography"
      ]
    },
    {
      "id": "erosion",
      "title": "Erosion",
      "description": "The geological process by which earthen materials are worn away and transported by natural forces such as wind, water, or ice.",
      "categories": [
        "science",
        "geography"
      ]
    },
    {
      "id": "native-american-history",
      "title": "Native American History",
      "description": "The diverse cultures, societies, and experiences of the indigenous peoples of North America, spanning from their arrival on the continent at least 15,000 years ago to the present day.",
      "categories": [
        "history",
        "culture"
      ]
    },
    {
      "id": "hoover-dam",
      "title": "Hoover Dam",
      "description": "A concrete arch-gravity dam in the Black Canyon of the Colorado River, on the border between Nevada and Arizona in the United States, completed in 1936.",
      "categories": [
        "geography",
        "technology",
        "history"
      ]
    },
    {
      "id": "rivers",
      "title": "Rivers",
      "description": "Natural flowing watercourses, typically freshwater, flowing towards an ocean, sea, lake, or another river, shaping landscapes through erosion and deposition.",
      "categories": [
        "geography",
        "science"
      ]
    },
    {
      "id": "weathering",
      "title": "Weathering",
      "description": "The breakdown and alteration of rocks, minerals, and soils at or near the Earth's surface through physical, chemical, and biological processes.",
      "categories": [
        "science",
        "geography"
      ]
    }
  ]
};
