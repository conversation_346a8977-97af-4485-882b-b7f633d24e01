/* Home Page Styles */
.search-engine {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(to bottom, white, rgb(172, 168, 153));
  min-height: 100vh;
}

.search-logo {
  margin-bottom: 20px;
  margin-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-logo svg {
  width: 272px;
  height: 92px;
}

/* Windows XP Search Bar Styles */
.xp-search-bar {
  display: flex;
  width: 80%;
  max-width: 600px;
  margin-bottom: 35px;
  background-color: #FFFFFF;
  border: 1px solid #7F9DB9;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
  z-index: 1;
  padding: 10px 15px;
  align-items: center;
}

.xp-search-icon {
  font-size: 18px;
  margin-right: 10px;
  color: #0A246A;
}

.xp-search-input {
  flex: 1;
  height: 30px;
  border: none;
  outline: none;
  font-size: 14px;
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  color: #000000;
  background-color: transparent;
}

.xp-search-button {
  height: 30px;
  padding: 0 18px;
  background: linear-gradient(to bottom, #7EB2F7, #0A246A);
  color: white;
  border: 1px solid #003C74;
  border-radius: 15px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  margin-left: 10px;
}

.xp-search-button:hover {
  background: linear-gradient(to bottom, #97C4FF, #0D3A89);
}

.xp-search-button:active {
  background: linear-gradient(to bottom, #0A246A, #7EB2F7);
}

.quick-links {
  width: 80%;
  max-width: 800px;
  margin-bottom: 30px;
  background-color: white;
  border: 1px solid #ACA899;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  position: relative;
}

.quick-links h2 {
  font-size: 16px;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding: 5px 10px;
  color: white;
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  background: linear-gradient(to bottom, #0A246A, #3A6EA5);
  border-radius: 3px 3px 0 0;
  margin: -20px -20px 15px -20px;
  text-align: left;
  width: calc(100% + 40px); /* Account for the padding of the container */
  box-sizing: border-box;
}

.quick-links-container {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  padding: 15px 10px;
  width: 100%;
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid transparent;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.quick-link:hover {
  background: linear-gradient(to bottom, #E3EFFF, #C4D7F5);
  border: 1px solid #7F9DB9;
}

.quick-link:hover .quick-link-text {
  text-decoration: underline;
}

.quick-link-icon {
  font-size: 32px;
  margin-bottom: 10px;
  color: #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.2s ease, color 0.2s ease;
  width: 40px;
  height: 40px;
}

.quick-link:hover .quick-link-icon {
  transform: scale(1.15);
  color: #3A6EA5;
}

.quick-link-text {
  font-size: 13px;
  color: #0A246A;
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  margin-top: 5px;
  width: 100%;
  text-align: center;
}

.news-section {
  width: 80%;
  max-width: 800px;
  margin-top: 30px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 20px;
  border: 1px solid #ACA899;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.news-section h2 {
  font-size: 16px;
  margin-bottom: 15px;
  border-bottom: 1px solid #ACA899;
  padding: 5px 10px;
  color: white;
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  background: linear-gradient(to bottom, #0A246A, #3A6EA5);
  border-radius: 3px 3px 0 0;
  margin: -20px -20px 15px -20px;
  text-align: left;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 0;
}

.news-item {
  border: 1px solid #ACA899;
  padding: 12px;
  background: linear-gradient(to bottom, #FFFFFF, #F1EFE2);
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border-radius: 3px;
}

.news-item:hover {
  background: linear-gradient(to bottom, #F1EFE2, #FFFFFF);
  border-color: #7F9DB9;
}

.news-item h3 {
  font-size: 11px;
  margin-top: 0;
  margin-bottom: 5px;
  color: #666;
  font-weight: normal;
}

.news-item h4 {
  font-size: 13px;
  margin-top: 0;
  margin-bottom: 8px;
  color: #0A246A;
}

.news-item p {
  font-size: 11px;
  margin-bottom: 12px;
  line-height: 1.4;
  color: #333333;
}

.read-more {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  color: #0A246A;
  border: 1px solid #7F9DB9;
  padding: 3px 8px;
  cursor: pointer;
  font-size: 11px;
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  border-radius: 3px;
}

.read-more:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
  text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .news-grid {
    grid-template-columns: 1fr;
  }

  .quick-links-container {
    grid-template-columns: repeat(3, 1fr);
  }

  .xp-search-bar,
  .quick-links,
  .news-section {
    width: 95%;
  }

  .search-logo {
    margin-bottom: 20px;
  }

  .search-logo svg {
    width: 232px;
    height: 78px;
  }
}

@media (max-width: 480px) {
  .quick-links-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .search-logo {
    margin-bottom: 15px;
  }

  .search-logo svg {
    width: 190px;
    height: 64px;
  }

  .xp-search-bar {
    margin-bottom: 20px;
    padding: 8px 10px;
    border-radius: 15px;
  }

  .xp-search-input {
    height: 28px;
    font-size: 13px;
  }

  .xp-search-button {
    height: 28px;
    padding: 0 12px;
    font-size: 11px;
    border-radius: 12px;
  }
}
