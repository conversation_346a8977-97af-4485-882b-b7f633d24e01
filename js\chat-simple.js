// Simple Chat Application
document.addEventListener('DOMContentLoaded', function() {
  console.log('Simple Chat loaded');

  // Initialize the chat
  initChat();
});

// Global variables
let currentContact = 'doctor';
let messageHistory = {};

// Initialize the chat application
function initChat() {
  console.log('Initializing simple chat...');

  // Add click handlers to contacts
  setupContactClicks();

  // Set up the send button
  setupSendButton();

  // Load initial messages for the doctor
  loadInitialMessages('doctor');

  // Set the status bar to show "ONLINE"
  if (typeof updateStatusBar === 'function') {
    updateStatusBar('ONLINE', 'Connected');
  }

  console.log('Chat initialized');
}

// Set up click handlers for contacts
function setupContactClicks() {
  // Get all contact items
  const contacts = document.querySelectorAll('.contact-item');

  // Add click handler to each contact
  contacts.forEach(contact => {
    contact.addEventListener('click', function() {
      // Get the contact ID
      const contactId = this.getAttribute('data-contact');
      console.log('Contact clicked:', contactId);

      // Update the active contact
      setActiveContact(contactId);

      // Load messages for this contact
      displayMessages(contactId);
    });
  });
}

// Set up the send button
function setupSendButton() {
  const sendButton = document.getElementById('send-button');
  const chatInput = document.getElementById('chat-input');

  // Add click handler to send button
  sendButton.addEventListener('click', function() {
    sendMessage();
  });

  // Add enter key handler to input field
  chatInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      sendMessage();
    }
  });
}

// Send a message
function sendMessage() {
  const chatInput = document.getElementById('chat-input');
  const message = chatInput.value.trim();

  if (message === '') return;

  console.log('Sending message:', message);

  // Add the message to the chat
  addMessage(currentContact, 'sent', message);

  // Clear the input field
  chatInput.value = '';

  // Check if contact is offline
  const contactStatus = getContactStatus(currentContact);
  if (contactStatus === 'offline') {
    console.log(`Contact ${currentContact} is offline and cannot respond`);
    // Add a system message
    addSystemMessage(`${getContactName(currentContact)} is offline and cannot respond.`);
    return;
  }

  // Show typing indicator
  showTypingIndicator(true);

  // Update contact status to "typing"
  updateContactStatus(currentContact, 'typing');

  // Calculate typing delay based on message length
  // Longer messages take longer to type
  const typingDelay = Math.max(1500, message.length * 50); // Minimum 1.5 seconds, or 50ms per character

  // Generate a response after a delay to simulate typing
  setTimeout(() => {
    // Hide typing indicator
    showTypingIndicator(false);

    // Update contact status back to "online"
    updateContactStatus(currentContact, 'online');

    const response = generateSimpleResponse(currentContact, message);

    // If response is null, the contact has gone offline
    if (response === null) {
      console.log(`No response from ${currentContact} as they are offline`);
      return;
    }

    addMessage(currentContact, 'received', response);

    // Check if this was the last response before going offline
    if (typeof responseCounter !== 'undefined' &&
        typeof MAX_RESPONSES_BEFORE_OFFLINE !== 'undefined' &&
        responseCounter[currentContact] >= MAX_RESPONSES_BEFORE_OFFLINE) {
      // Set contact to offline after a short delay
      setTimeout(() => {
        updateContactStatus(currentContact, 'offline');
      }, 2000);
    }
  }, typingDelay);
}

// Show or hide the typing indicator
function showTypingIndicator(show) {
  const typingIndicator = document.querySelector('.typing-indicator');
  if (!typingIndicator) return;

  if (show) {
    typingIndicator.classList.add('active');
  } else {
    typingIndicator.classList.remove('active');
  }
}

// Update the contact status in the UI
function updateContactStatus(contactId, status) {
  console.log(`Updating contact ${contactId} status to ${status}`);

  // Update the contact status in the UI
  const contactItem = document.querySelector(`.contact-item[data-contact="${contactId}"]`);
  if (contactItem) {
    // Update the status dot
    const statusDot = contactItem.querySelector('.status-dot');
    if (statusDot) {
      statusDot.className = `status-dot ${status}`;
    }

    // Update the contact status text if in the chat header
    if (currentContact === contactId) {
      const statusText = document.getElementById('current-chat-status-text');
      if (statusText) {
        // Capitalize first letter of status
        const displayStatus = status.charAt(0).toUpperCase() + status.slice(1);
        statusText.textContent = displayStatus;
      }

      // Update the status dot in the chat header
      const headerStatusDot = document.getElementById('current-chat-status');
      if (headerStatusDot) {
        headerStatusDot.className = `status-dot ${status}`;
      }

      // Update the status bar to match the contact status
      if (typeof updateStatusBar === 'function') {
        // Use uppercase for status bar
        const statusBarText = status === 'typing' ? 'TYPING...' : status.toUpperCase();
        updateStatusBar(statusBarText, 'Connected');
      }
    }
  }

  // If we have the Hinglish generator with contact status
  if (typeof contactStatus !== 'undefined') {
    contactStatus[contactId] = status;
  }
}

// Add a system message to the chat
function addSystemMessage(text) {
  // Get the chat messages container
  const chatMessages = document.getElementById('chat-messages');

  // Create a message element
  const messageElement = document.createElement('div');
  messageElement.classList.add('message', 'system');

  // Add the message content
  messageElement.innerHTML = `
    <div class="message-text">${text}</div>
  `;

  // Add the message to the chat
  chatMessages.appendChild(messageElement);

  // Scroll to the bottom
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Set the active contact
function setActiveContact(contactId) {
  // Update the current contact
  currentContact = contactId;

  // Remove active class from all contacts
  const contacts = document.querySelectorAll('.contact-item');
  contacts.forEach(contact => {
    contact.classList.remove('active');
  });

  // Add active class to the selected contact
  const selectedContact = document.querySelector(`.contact-item[data-contact="${contactId}"]`);
  if (selectedContact) {
    selectedContact.classList.add('active');
  }

  // Update the chat header
  updateChatHeader(contactId);
}

// Update the chat header
function updateChatHeader(contactId) {
  // Get the contact name
  const contactName = getContactName(contactId);

  // Update the header
  const nameElement = document.getElementById('current-chat-name');
  if (nameElement) {
    nameElement.textContent = contactName;
  }

  // Update the avatar
  const avatarElement = document.getElementById('current-chat-avatar');
  if (avatarElement) {
    avatarElement.src = `../img/chat/${contactId}-avatar.svg`;
  }

  // Reset the status bar to show "ONLINE"
  if (typeof updateStatusBar === 'function') {
    updateStatusBar('ONLINE', 'Connected');
  }
}

// Get the contact name
function getContactName(contactId) {
  const names = {
    'doctor': 'Dr. Vikram Sharma',
    'rahul': 'Rahul Verma',
    'priya': 'Priya Patel',
    'amit': 'Amit Desai',
    'neha': 'Neha Gupta'
  };

  return names[contactId] || 'Unknown Contact';
}

// Get the contact status
function getContactStatus(contactId) {
  // If we have the Hinglish generator with contact status
  if (typeof contactStatus !== 'undefined') {
    return contactStatus[contactId] || 'offline';
  }

  // Default status for each contact
  const status = {
    'doctor': 'online',
    'rahul': 'online',
    'priya': 'away',
    'amit': 'offline',
    'neha': 'online'
  };

  return status[contactId] || 'offline';
}

// Set a contact to offline
window.setContactOffline = function(contactId) {
  console.log(`Setting contact ${contactId} to offline`);

  // Update the contact status in the UI
  const contactItem = document.querySelector(`.contact-item[data-contact="${contactId}"]`);
  if (contactItem) {
    // Update the status dot
    const statusDot = contactItem.querySelector('.status-dot');
    if (statusDot) {
      statusDot.className = 'status-dot offline';
    }

    // Update the contact status text if in the chat header
    if (currentContact === contactId) {
      const statusText = document.getElementById('current-chat-status-text');
      if (statusText) {
        statusText.textContent = 'Offline';
      }

      // Update the status dot in the chat header
      const headerStatusDot = document.getElementById('current-chat-status');
      if (headerStatusDot) {
        headerStatusDot.className = 'status-dot offline';
      }

      // Update the status bar to show OFFLINE
      if (typeof updateStatusBar === 'function') {
        updateStatusBar('OFFLINE', 'Connected');
      }
    }
  }

  // Add a system message
  if (currentContact === contactId) {
    addSystemMessage(`${getContactName(contactId)} is now offline.`);
  }

  // If we have the Hinglish generator with contact status
  if (typeof contactStatus !== 'undefined') {
    contactStatus[contactId] = 'offline';
  }
}

// Load initial messages for a contact
function loadInitialMessages(contactId) {
  // Define initial messages with previous conversation history
  const initialMessages = {
    'doctor': [
      { type: 'received', text: "Namaste Arjun Patil, aapka appointment schedule hua hai. Kya aap aa payenge?" },
      { type: 'sent', text: "Haan doctor sahab, main zaroor aaunga. Kitne baje hai appointment?" },
      { type: 'received', text: "Appointment 4 baje hai. Aap time pe aa jayenge to accha rahega." },
      { type: 'sent', text: "Main time pe pahunch jaunga. Kya mujhe kuch prepare karna hai appointment ke liye?" },
      { type: 'received', text: "Nahi, bas apne current medication ki list le aayein aur honest rahiye apne habits ke baare mein." },
      { type: 'sent', text: "Theek hai doctor sahab. Waise mujhe thoda darr lag raha hai apne addiction ke baare mein baat karne mein." },
      { type: 'received', text: "Darr natural hai Arjun, lekin yeh pehla step hai recovery ka. Main help karunga aapki." },
      { type: 'sent', text: "Thank you doctor. Mujhe lagta hai main control kho raha hoon apne ganja use pe." },
      { type: 'received', text: "Yeh realize karna bahut important step hai. Hum milke is problem ko address karenge." },
      { type: 'sent', text: "Kya aapke paas koi immediate suggestions hain? Cravings bahut strong ho rahi hain." },
      { type: 'received', text: "Jab cravings ho to deep breathing exercises try karein aur paani piyein. Distraction techniques bhi help kar sakte hain." },
      { type: 'sent', text: "Main try karunga. Kya meditation se bhi help milegi?" },
      { type: 'received', text: "Haan, meditation bahut effective hai addiction management mein. 5 minute se start karein daily." },
      { type: 'sent', text: "Aur koi physical activities jo help kar sakti hain?" },
      { type: 'received', text: "Exercise bahut important hai recovery mein. Endorphins release hote hain jo mood improve karte hain." },
      { type: 'sent', text: "Main jogging start kar sakta hoon shayad. Waise mujhe neend aane mein problem ho rahi hai." },
      { type: 'received', text: "Sleep issues common hain early recovery mein. Sleep hygiene improve karein - fixed schedule, screen time limit, relaxation before bed." },
      { type: 'sent', text: "Aur diet ke baare mein koi advice?" },
      { type: 'received', text: "Balanced diet follow karein, processed sugar aur caffeine limit karein. Hydration maintain karein." },
      { type: 'received', text: "Humari last session mein, humne aapke ganja use ke concerns discuss kiye the. Kya aap consumption reduce kar paye jaise humne discuss kiya tha?" }
    ],
    'rahul': [
      { type: 'received', text: "Arjun bhai, kahan gayab ho gaya tha? College mein dikha hi nahi tu." },
      { type: 'sent', text: "Yaar Rahul, thoda busy tha. Family issues chal rahe the." },
      { type: 'received', text: "Koi serious baat hai kya? Share kar sakta hai agar comfortable hai to." },
      { type: 'sent', text: "Nahi yaar, bas usual drama. Waise tu bata, kya chal raha hai?" },
      { type: 'received', text: "Bhai, kal party thi Rohit ke ghar pe. Tu miss kar diya ekdum mast scene tha." },
      { type: 'sent', text: "Damn! Kaun kaun tha wahan? Kuch photos bhej." },
      { type: 'received', text: "Saare gang members the. Photos WhatsApp pe bhejta hoon. Waise teri crush bhi thi wahan." },
      { type: 'sent', text: "Kya?! Neha bhi thi? Usne mujhe kuch pucha mere baare mein?" },
      { type: 'received', text: "Haan bhai, 2-3 baar pucha ki tu kahan hai. Interested lag rahi thi." },
      { type: 'sent', text: "Seriously? Ab kya karoon? Message karoon usko?" },
      { type: 'received', text: "Haan kar de bhai. Chance hai tera, waste mat kar." },
      { type: 'sent', text: "Theek hai, try karta hoon. Waise assignments complete kiye tune?" },
      { type: 'received', text: "Abhi tak start nahi kiya bhai. Tu kar liya?" },
      { type: 'sent', text: "Maine bhi nahi kiya. Weekend pe saath mein karte hain?" },
      { type: 'received', text: "Done bhai. Mere ghar pe aa jana. Mummy ne kaha hai ki tujhe bahut time ho gaya aaye hue." },
      { type: 'sent', text: "Aunty ko mera namaste bolna. Unke haath ke parathe khane ka man kar raha hai." },
      { type: 'received', text: "Haan bhai, specially request kar dunga. Waise new game download kiya hai maine, Call of Duty: Black Ops. Abhi release hua hai November mein." },
      { type: 'sent', text: "Seriously? Kahan se mila? Working hai?" },
      { type: 'received', text: "Haan bhai, ekdum mast hai. Graphics aur gameplay dono next level hain." },
      { type: 'received', text: "Arjun Patil bhai, kaise ho? Long time no see! Kahan busy tha itne din?" }
    ],
    'priya': [
      { type: 'received', text: "Arjun, physics assignment mein help chahiye? Main kar chuki hoon." },
      { type: 'sent', text: "Haan Priya, please help kar de. Mujhe kuch formulas samajh nahi aa rahe." },
      { type: 'received', text: "Konse part mein problem hai? Thermodynamics ya Mechanics?" },
      { type: 'sent', text: "Thermodynamics mein. Entropy wale questions solve nahi ho rahe." },
      { type: 'received', text: "Oh, woh thode tricky hain. Kal library mein mil sakte hain, main explain kar dungi." },
      { type: 'sent', text: "Perfect! Kitne baje free hai tu?" },
      { type: 'received', text: "11 baje ke baad free hoon. 11:30 chalega?" },
      { type: 'sent', text: "Haan perfect hai. Main notes le aaunga apne." },
      { type: 'received', text: "Theek hai. Waise chemistry project ka kya hua? Start kiya?" },
      { type: 'sent', text: "Haan, introduction likh liya hai. Lekin research part abhi baaki hai." },
      { type: 'received', text: "Main kuch research papers share kar sakti hoon jo mere paas hain, help ho jayegi." },
      { type: 'sent', text: "That would be great! Please send kar diyo. Waise tu kaisi hai? Busy chal raha hai kya?" },
      { type: 'received', text: "Haan yaar, assignments ke saath internship bhi kar rahi hoon. Time management mushkil ho raha hai." },
      { type: 'sent', text: "Wow, internship kahan pe? Aur kaisa experience hai?" },
      { type: 'received', text: "Tech Solutions mein. Experience accha hai, real-world exposure mil raha hai." },
      { type: 'sent', text: "That's great! Mujhe bhi koi internship dhundhni chahiye." },
      { type: 'received', text: "Haan, resume build karne mein help karta hai. Main kuch openings share kar sakti hoon." },
      { type: 'sent', text: "Please do! Waise class notes miss kiye maine kal. Share kar sakti hai?" },
      { type: 'received', text: "Sure, scan karke bhej dungi. Important concepts cover hue the." },
      { type: 'received', text: "Hi Arjun Patil! College assignment complete kar liya? Deadline next week hai." }
    ],
    'amit': [
      { type: 'received', text: "Bhai Patil, gym join kiya tune? Body banana hai kya?" },
      { type: 'sent', text: "Haan yaar, soch raha hoon. Tu kaunse gym mein hai?" },
      { type: 'received', text: "Fitness Hub mein. Ekdum mast hai, equipment new hain aur trainers bhi acche hain." },
      { type: 'sent', text: "Kitna charge karte hain monthly?" },
      { type: 'received', text: "2500 per month, lekin annual package lete ho to discount milta hai." },
      { type: 'sent', text: "Not bad. Chal saath mein chalte hain kal, dekhta hoon." },
      { type: 'received', text: "Done bhai! 6 baje chalega? Morning workout best hota hai." },
      { type: 'sent', text: "6 baje?! Itni subah? Thoda late nahi ho sakta?" },
      { type: 'received', text: "Abe fitness ke liye commitment chahiye. Chal 7 baje final." },
      { type: 'sent', text: "Theek hai bhai, try karunga. Waise protein shake konsa lete ho?" },
      { type: 'received', text: "ON Gold Standard. Thoda expensive hai but quality acchi hai." },
      { type: 'sent', text: "Accha. Diet plan bhi follow karta hai kya?" },
      { type: 'received', text: "Haan bhai, high protein, moderate carbs. Meal prep Sunday ko karta hoon pure week ke liye." },
      { type: 'sent', text: "Impressive dedication! Mujhe bhi tips dena start karne ke liye." },
      { type: 'received', text: "Bilkul bhai, full support dunga. Waise cricket practice kaisi chal rahi hai?" },
      { type: 'sent', text: "Theek hai. Bowling improve ho rahi hai, lekin batting mein consistency nahi hai." },
      { type: 'received', text: "Weekend pe nets pe chalte hain, kuch drills bataunga jo mujhe coach ne sikhaye the." },
      { type: 'sent', text: "Perfect! Saturday chalega? Sunday ko family function hai." },
      { type: 'received', text: "Haan Saturday theek hai. 4 baje ground pe milte hain." },
      { type: 'received', text: "Bhai Patil, cricket match ka score dekha? India is winning! Kohli ne century maari hai!" }
    ],
    'neha': [
      { type: 'received', text: "Arjun, literature assignment ke liye book mil gayi library se?" },
      { type: 'sent', text: "Nahi Neha, already issued thi. Koi alternative hai?" },
      { type: 'received', text: "Mere paas PDF hai, share kar doon?" },
      { type: 'sent', text: "That would be great! Please send kar do." },
      { type: 'received', text: "Sent! Check your email. Waise analysis start kiya?" },
      { type: 'sent', text: "Abhi notes bana raha hoon. Character analysis thoda complex lag raha hai." },
      { type: 'received', text: "Main notes share kar sakti hoon apne. Different perspective milega." },
      { type: 'sent', text: "That would be really helpful. Tu kitna complete kar chuki hai?" },
      { type: 'received', text: "Almost 70% done hai. Weekend tak finish kar lungi." },
      { type: 'sent', text: "Wow, tu to bahut ahead hai. Mujhe bhi jaldi start karna chahiye." },
      { type: 'received', text: "Time management important hai. Main schedule follow karti hoon strict wala." },
      { type: 'sent', text: "Koi tips hai effective studying ke liye? Mera focus nahi rehta zyada der." },
      { type: 'received', text: "Pomodoro technique try kar - 25 minutes study, 5 minutes break. Works for me." },
      { type: 'sent', text: "Sounds good, try karunga. Waise poetry competition ke liye register kiya tune?" },
      { type: 'received', text: "Haan, tu bhi kar le. Creative writing skills improve hongi." },
      { type: 'sent', text: "Not sure if poetry meri strength hai, lekin try kar sakta hoon." },
      { type: 'received', text: "Everyone starts somewhere. Main help kar sakti hoon if you want." },
      { type: 'sent', text: "That would be nice. Coffee pe discuss kar sakte hain kal?" },
      { type: 'received', text: "Sure, college ke baad Blue Cafe chalenge." },
      { type: 'received', text: "Hey Arjun Patil! Kal college mein milte hain. Main 9 baje library ke bahar wait karungi." }
    ]
  };

  // Get messages for this contact
  const messages = initialMessages[contactId] || [];

  // Initialize message history for this contact if it doesn't exist
  if (!messageHistory[contactId]) {
    messageHistory[contactId] = [];
  }

  // Generate realistic timestamps for the conversation
  const generateTimeSequence = (count, startHour, startMinute, intervalMinutes) => {
    const times = [];
    let hour = startHour;
    let minute = startMinute;

    for (let i = 0; i < count; i++) {
      // Add current time
      times.push(`${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);

      // Increment minutes
      minute += intervalMinutes + Math.floor(Math.random() * 5);

      // Handle hour rollover
      if (minute >= 60) {
        hour += Math.floor(minute / 60);
        minute = minute % 60;

        // Handle day rollover (just for display purposes)
        if (hour >= 24) {
          hour = hour % 24;
        }
      }
    }

    return times;
  };

  // Generate time sequence based on contact
  let times;
  switch(contactId) {
    case 'doctor':
      times = generateTimeSequence(messages.length, 16, 0, 2); // Starting at 4:00 PM
      break;
    case 'rahul':
      times = generateTimeSequence(messages.length, 19, 30, 1); // Starting at 7:30 PM
      break;
    case 'priya':
      times = generateTimeSequence(messages.length, 14, 15, 2); // Starting at 2:15 PM
      break;
    case 'amit':
      times = generateTimeSequence(messages.length, 18, 0, 1); // Starting at 6:00 PM
      break;
    case 'neha':
      times = generateTimeSequence(messages.length, 15, 45, 2); // Starting at 3:45 PM
      break;
    default:
      times = generateTimeSequence(messages.length, 12, 0, 3); // Default starting at noon
  }

  // Add messages to history and display them with custom times
  messages.forEach((msg, index) => {
    addMessage(contactId, msg.type, msg.text, times[index]);
  });
}

// Add a message to the chat
function addMessage(contactId, type, text, customTime) {
  // Get the time - use custom time if provided, otherwise use current time
  let time;
  if (customTime) {
    time = customTime;
  } else {
    const now = new Date();
    time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
  }

  // Create a message object
  const message = {
    type: type,
    text: text,
    time: time
  };

  // Add the message to history
  if (!messageHistory[contactId]) {
    messageHistory[contactId] = [];
  }
  messageHistory[contactId].push(message);

  // If this is the current contact, display the message
  if (contactId === currentContact) {
    displayMessage(message);
  }

  // Update the last message in the contact list
  updateLastMessage(contactId, type, text);
}

// Display a single message
function displayMessage(message) {
  // Get the chat messages container
  const chatMessages = document.getElementById('chat-messages');

  // Create a message element
  const messageElement = document.createElement('div');
  messageElement.classList.add('message', message.type);

  // Add the message content
  messageElement.innerHTML = `
    <div class="message-text">${message.text}</div>
    <div class="message-time">${message.time}</div>
  `;

  // Add the message to the chat
  chatMessages.appendChild(messageElement);

  // Scroll to the bottom
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Display all messages for a contact
function displayMessages(contactId) {
  // Get the chat messages container
  const chatMessages = document.getElementById('chat-messages');

  // Clear the chat
  chatMessages.innerHTML = '';

  // Get messages for this contact
  const messages = messageHistory[contactId] || [];

  // If there are no messages, load initial messages
  if (messages.length === 0) {
    loadInitialMessages(contactId);
  } else {
    // Display all messages
    messages.forEach(message => {
      displayMessage(message);
    });
  }
}

// Update the last message in the contact list
function updateLastMessage(contactId, type, text) {
  // Get the contact item
  const contactItem = document.querySelector(`.contact-item[data-contact="${contactId}"]`);
  if (!contactItem) return;

  // Get the last message element
  const lastMessageElement = contactItem.querySelector('.contact-last-message');
  if (!lastMessageElement) return;

  // Update the text
  if (type === 'sent') {
    lastMessageElement.textContent = `You: ${text}`;
  } else {
    lastMessageElement.textContent = text;
  }

  // Update the time
  const timeElement = contactItem.querySelector('.contact-time');
  if (timeElement) {
    timeElement.textContent = 'now';
  }
}

// Generate a response using the Hinglish generator
function generateSimpleResponse(contactId, message) {
  // Check if the Hinglish generator is available
  if (typeof generateHinglishResponse === 'function') {
    // Use the Hinglish generator
    return generateHinglishResponse(message, contactId);
  } else {
    console.warn('Hinglish generator not found, using fallback responses');

    // Fallback responses if the generator is not available
    const responses = {
      'doctor': [
        "Main samajhta hoon Arjun, ganja use karna health ke liye accha nahi hai. Koi side effects notice kiye hain?",
        "Stress manage karne ke liye yoga ya meditation try karo. It helps a lot.",
        "Dheere dheere consumption kam karna better hota hai, ekdum se chhodna mushkil hai.",
        "Chalo kuch strategies discuss karte hain jo aapko help kar sakti hain, Arjun Patil.",
        "Aapki health meri priority hai. Main aapko support karunga is process mein."
      ],
      'rahul': [
        "Arre yaar, chill kar! Weekend pe hang out karte hain.",
        "Bhai, new movie release hui hai. Dekhne chalega?",
        "Kya scene hai? College mein sab theek?",
        "Bindaas! Mast plan hai.",
        "Tu bhi na, Arjun Patil! Always overthinking everything."
      ],
      'priya': [
        "Arjun, assignment deadline next week hai. Started working on it?",
        "Main help kar sakti hoon if you need it, Arjun.",
        "Library mein milte hain to study together?",
        "Professor ne extra reading assign ki hai.",
        "Hmm, interesting point, Arjun Patil. Let me think about that."
      ],
      'amit': [
        "Arjun bhai, cricket match dekha? India is winning!",
        "Gym chalega aaj, Arjun?",
        "Bhai Patil, fitness is important. Regular exercise helps with everything.",
        "Weekend pe match dekhne milte hain?",
        "Tension mat le yaar, sab theek ho jayega."
      ],
      'neha': [
        "Arjun Patil, literature assignment complete kar liya?",
        "New book mil gayi library se. It's fascinating!",
        "Class notes share kar sakti hoon if you missed the lecture, Arjun.",
        "Coffee pe milte hain to discuss the project?",
        "That's quite thoughtful of you, Arjun Patil."
      ]
    };

    // Get responses for this contact
    const contactResponses = responses[contactId] || responses['doctor'];

    // Return a random response
    return contactResponses[Math.floor(Math.random() * contactResponses.length)];
  }
}
