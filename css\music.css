/* Music Page Styles */
.music-page {
  font-family: '<PERSON><PERSON><PERSON>', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 0;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.music-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 0;
  border-radius: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.music-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.music-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  min-height: 0; /* Allows flex items to shrink below their content size */
}

/* Sidebar Styles */
.music-sidebar {
  width: 220px;
  background-color: #F1EFE2;
  border-right: 1px solid #ACA899;
  padding: 15px 0;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 20px;
}

.sidebar-section h3 {
  padding: 0 15px;
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #0A246A;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu-item {
  padding: 8px 15px;
  cursor: pointer;
  font-size: 12px;
  color: #333333;
  transition: background-color 0.2s ease;
}

.sidebar-menu-item:hover {
  background-color: #E3EFFF;
}

.sidebar-menu-item.active {
  background-color: #316AC5;
  color: white;
}

/* Main Content Styles */
.music-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: white;
}

.music-view {
  display: none;
}

.music-view.active {
  display: block;
}

/* Featured Albums Section */
.featured-section {
  margin-bottom: 30px;
}

.featured-section h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #0A246A;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.album-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 20px;
}

.album-card {
  background-color: #F9F9F9;
  border: 1px solid #ACA899;
  border-radius: 5px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.album-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.album-image {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.album-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.album-card:hover .play-overlay {
  opacity: 1;
}

.album-info {
  padding: 10px;
}

.album-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #0A246A;
}

.album-artist {
  font-size: 12px;
  color: #666666;
}

/* Recently Played Section */
.recently-played-section {
  margin-bottom: 30px;
}

.recently-played-section h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #0A246A;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.song-list {
  background-color: #F9F9F9;
  border: 1px solid #ACA899;
  border-radius: 5px;
}

.song-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #E5E5E5;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.song-item:last-child {
  border-bottom: none;
}

.song-item:hover {
  background-color: #E3EFFF;
}

.song-play-button {
  margin-right: 15px;
  font-size: 14px;
  color: #0A246A;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  border: 1px solid #ACA899;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.2s ease, transform 0.1s ease;
}

.song-play-button:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
  transform: scale(1.1);
}

.song-info {
  flex: 1;
}

.song-title {
  font-size: 13px;
  font-weight: bold;
  color: #333333;
}

.song-artist {
  font-size: 12px;
  color: #666666;
}

.song-album {
  width: 150px;
  font-size: 12px;
  color: #666666;
}

.song-duration {
  width: 50px;
  text-align: right;
  font-size: 12px;
  color: #666666;
}

/* Browse Categories */
.browse-categories {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.category-card {
  background-color: #F9F9F9;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: #E3EFFF;
}

.category-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.category-name {
  font-size: 14px;
  font-weight: bold;
  color: #0A246A;
}

/* Radio Stations */
.radio-stations {
  margin-top: 20px;
}

.radio-station {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #F9F9F9;
  border: 1px solid #ACA899;
  border-radius: 5px;
  margin-bottom: 15px;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.radio-station:hover {
  background-color: #E3EFFF;
}

.radio-icon {
  font-size: 24px;
  margin-right: 15px;
}

.radio-info {
  flex: 1;
}

.radio-name {
  font-size: 14px;
  font-weight: bold;
  color: #0A246A;
}

.radio-genre {
  font-size: 12px;
  color: #666666;
}

.radio-play-button {
  font-size: 18px;
  color: #0A246A;
}

/* Music Player Styles */
.music-player {
  background: linear-gradient(to bottom, #F1EFE2, #D4D0C8);
  border-top: 1px solid #ACA899;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  height: 80px;
}

.now-playing {
  display: flex;
  align-items: center;
  width: 220px;
}

.now-playing-image {
  width: 60px;
  height: 60px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 10px;
}

.now-playing-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.now-playing-info {
  flex: 1;
  overflow: hidden;
}

.now-playing-title {
  font-size: 13px;
  font-weight: bold;
  color: #0A246A;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.now-playing-artist {
  font-size: 12px;
  color: #666666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.player-controls {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 20px;
}

.control-buttons {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-top: 10px;
}

.control-button {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  border: 1px solid #ACA899;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 5px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s ease;
}

.control-button:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
}

.control-button.play-pause {
  width: 40px;
  height: 40px;
  font-size: 16px;
}

.progress-container {
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer; /* Make it clear the entire container is clickable */
  padding: 5px 0; /* Add padding to make it easier to click */
  position: relative; /* For absolute positioning of children */
}

.current-time, .total-time {
  font-size: 12px;
  font-weight: bold;
  color: #0A246A;
  width: 40px;
  background-color: #F1EFE2;
  padding: 2px 4px;
  border-radius: 3px;
  border: 1px solid #ACA899;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.current-time {
  text-align: right;
  margin-right: 10px;
}

.total-time {
  text-align: left;
  margin-left: 10px;
}

.progress-bar {
  flex: 1;
  height: 16px; /* Increased height for easier clicking */
  background-color: #D4D0C8;
  border: 1px solid #ACA899;
  border-radius: 8px;
  position: relative;
  cursor: pointer;
  overflow: hidden; /* Ensure progress stays within the bar */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1); /* Add inner shadow for depth */
  margin: 0 5px; /* Add some margin for better spacing */
  transition: background-color 0.2s ease; /* Smooth transition for hover effect */
}

/* Add hover effect to make it more obvious it's clickable */
.progress-bar:hover {
  background-color: #E3EFFF;
}

.progress {
  height: 100%;
  background: linear-gradient(to right, #316AC5, #3A6EA5); /* Gradient for better visibility */
  border-radius: 6px;
  width: 0%; /* Start at 0%, will be updated by JS */
  transition: width 0.1s linear; /* Smooth transition for width changes */
  position: relative;
  box-shadow: 0 0 8px rgba(49, 106, 197, 0.7); /* Enhanced glow effect */
  cursor: pointer; /* Make it clear it's clickable */
  z-index: 5; /* Ensure it's above other elements */
}

/* Add a subtle animation to make it more noticeable */
.progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite linear;
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Hover indicator for progress bar */
#hover-indicator {
  position: absolute;
  top: 0;
  height: 100%;
  width: 2px;
  background-color: #FFFFFF;
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
  z-index: 10;
  pointer-events: none;
}

#time-tooltip {
  position: absolute;
  bottom: 20px;
  background-color: #0A246A;
  color: #FFFFFF;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 10px;
  transform: translateX(-50%);
  pointer-events: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  border: 1px solid #3A6EA5;
}

.player-options {
  display: flex;
  align-items: center;
}

.option-button {
  background: none;
  border: none;
  font-size: 16px;
  margin: 0 5px;
  cursor: pointer;
  color: #666666;
}

.option-button:hover {
  color: #0A246A;
}

.volume-control {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.volume-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #666666;
  margin-right: 5px;
}

.volume-slider-container {
  width: 80px;
}

.volume-slider {
  width: 100%;
  height: 5px;
  -webkit-appearance: none;
  background-color: #D4D0C8;
  border: 1px solid #ACA899;
  border-radius: 2px;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  background-color: #316AC5;
  border: 1px solid #0A246A;
  border-radius: 50%;
  cursor: pointer;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .music-content {
    flex-direction: column;
  }

  .music-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #ACA899;
    padding: 10px 0;
    max-height: 200px;
  }

  .music-player {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }

  .now-playing {
    width: 100%;
    margin-bottom: 10px;
  }

  .player-controls {
    width: 100%;
    margin: 10px 0;
  }

  .player-options {
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {
  .album-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .song-album {
    display: none;
  }

  .browse-categories {
    grid-template-columns: repeat(2, 1fr);
  }
}
