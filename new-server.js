const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8003;
const HOST = '0.0.0.0';

const MIME_TYPES = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'text/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  // Handle the root URL
  let filePath;
  if (req.url === '/') {
    filePath = './index.html';
  } else {
    // Remove any query parameters
    const urlPath = req.url.split('?')[0];

    // Handle CSS files specifically
    if (urlPath.endsWith('.css')) {
      // Check if the CSS file exists directly
      if (fs.existsSync('.' + urlPath)) {
        filePath = '.' + urlPath;
      }
      // Check if the CSS file exists in the css directory
      else if (fs.existsSync('./css' + urlPath.substring(urlPath.lastIndexOf('/')))) {
        filePath = './css' + urlPath.substring(urlPath.lastIndexOf('/'));
      }
      // Check if the CSS file exists in the html directory
      else if (fs.existsSync('./html' + urlPath)) {
        filePath = './html' + urlPath;
      }
      else {
        console.log(`CSS file not found: ${urlPath}`);
        res.writeHead(404);
        res.end('CSS file not found');
        return;
      }
    }
    // Handle other files
    else {
      filePath = '.' + urlPath;

      // If file doesn't exist directly, try in the html directory
      if (!fs.existsSync(filePath) && !filePath.includes('/html/')) {
        const htmlPath = './html' + urlPath;
        if (fs.existsSync(htmlPath)) {
          filePath = htmlPath;
        }
      }
    }
  }

  console.log(`Attempting to serve file: ${filePath}`);

  // Get the file extension
  const extname = path.extname(filePath);
  let contentType = MIME_TYPES[extname] || 'application/octet-stream';

  // Read the file
  fs.readFile(filePath, (err, content) => {
    if (err) {
      console.log(`Error reading file: ${err.code}`);
      if (err.code === 'ENOENT') {
        // Page not found
        fs.readFile('./html/404.html', (err, content) => {
          if (err) {
            // If 404.html doesn't exist, send a simple 404 message
            res.writeHead(404);
            res.end('404 Not Found');
          } else {
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(content, 'utf-8');
          }
        });
      } else {
        // Server error
        res.writeHead(500);
        res.end(`Server Error: ${err.code}`);
      }
    } else {
      // Success
      // Add caching headers for static assets to improve performance
      const headers = { 'Content-Type': contentType };

      // Add cache control headers for static assets
      if (extname === '.js' || extname === '.css' ||
          extname === '.svg' || extname === '.png' ||
          extname === '.jpg' || extname === '.jpeg' ||
          extname === '.gif') {
        // Cache for 1 hour
        headers['Cache-Control'] = 'public, max-age=3600';
      }

      res.writeHead(200, headers);
      res.end(content, 'utf-8');
      console.log(`Successfully served: ${filePath} as ${contentType}`);
    }
  });
});

server.listen(PORT, HOST, () => {
  console.log(`
  ╔════════════════════════════════════════════════════════════╗
  ║                                                            ║
  ║   🌐 Unreal Browser Website Server Started!                ║
  ║                                                            ║
  ║   📡 Server running at:                                    ║
  ║      http://${HOST}:${PORT}/                                  ║
  ║                                                            ║
  ║   🔗 Access from other devices on your network:            ║
  ║      http://YOUR_IP_ADDRESS:${PORT}/                         ║
  ║                                                            ║
  ║   📱 To find your IP address:                              ║
  ║      - Windows: Run 'ipconfig' in Command Prompt           ║
  ║      - Mac/Linux: Run 'ifconfig' or 'ip addr' in Terminal  ║
  ║                                                            ║
  ║   🛑 To stop the server: Press Ctrl+C                      ║
  ║                                                            ║
  ╚════════════════════════════════════════════════════════════╝
  `);
});
