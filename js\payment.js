// Payment Page Functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the payment page
  initPaymentPage();
});

function initPaymentPage() {
  // Get payment type and details from URL parameters or localStorage
  const paymentType = getPaymentType();
  const paymentDetails = getPaymentDetails();

  // Update the page based on payment type
  updatePaymentSummary(paymentType, paymentDetails);

  // Initialize form validation
  initFormValidation();

  // Initialize payment buttons
  initPaymentButtons(paymentType, paymentDetails);

  // Update status bar
  updateStatusBar('Ready to process payment', 'Connected');
}

// Get payment type from URL parameters or localStorage
function getPaymentType() {
  // Try to get from URL parameters first
  const urlParams = new URLSearchParams(window.location.search);
  const typeFromUrl = urlParams.get('type');

  if (typeFromUrl) {
    return typeFromUrl;
  }

  // If not in URL, try localStorage
  const typeFromStorage = localStorage.getItem('paymentType');

  if (typeFromStorage) {
    return typeFromStorage;
  }

  // Default to 'movie' if not specified
  return 'movie';
}

// Get payment details from localStorage
function getPaymentDetails() {
  const detailsJson = localStorage.getItem('paymentDetails');

  if (detailsJson) {
    try {
      return JSON.parse(detailsJson);
    } catch (e) {
      console.error('Error parsing payment details:', e);
    }
  }

  // Return default details if none found
  return {
    // Default movie ticket details
    movie: {
      title: 'Movie Title',
      date: 'Today (15 May 2010)',
      theater: 'Theater Name - Address',
      time: '7:30 PM',
      tickets: 2,
      ticketPrice: 250,
      total: 500
    },
    // Default shopping details
    shopping: {
      items: [
        { name: 'Product 1', price: 1200, quantity: 1 },
        { name: 'Product 2', price: 800, quantity: 2 }
      ],
      subtotal: 2800,
      shipping: 100,
      tax: 140,
      total: 3040
    },
    // Default travel details
    travel: {
      type: 'flight',
      title: 'Air India (AI 202)',
      from: 'Mumbai',
      to: 'Delhi',
      date: '15 May 2010',
      passengers: 2,
      class: 'Economy',
      baseFare: 10998,
      taxes: 1980,
      total: 12978
    }
  };
}

// Update payment summary based on payment type
function updatePaymentSummary(paymentType, paymentDetails) {
  const summaryElement = document.getElementById('payment-summary');

  if (!summaryElement) return;

  let summaryHTML = '';

  if (paymentType === 'movie') {
    const movie = paymentDetails.movie;

    summaryHTML = `
      <div class="summary-row">
        <div class="summary-label">Movie:</div>
        <div class="summary-value">${movie.title}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Date:</div>
        <div class="summary-value">${movie.date}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Theater:</div>
        <div class="summary-value">${movie.theater}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Time:</div>
        <div class="summary-value">${movie.time}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Tickets:</div>
        <div class="summary-value">${movie.tickets} x ₹${movie.ticketPrice.toFixed(2)}</div>
      </div>
      <div class="summary-row total-row">
        <div class="summary-label">Total:</div>
        <div class="summary-value">₹${movie.total.toFixed(2)}</div>
      </div>
    `;
  } else if (paymentType === 'shopping') {
    const shopping = paymentDetails.shopping;

    // Start with items
    summaryHTML = '<div class="summary-items">';

    shopping.items.forEach(item => {
      summaryHTML += `
        <div class="summary-row">
          <div class="summary-label">${item.name} x ${item.quantity}</div>
          <div class="summary-value">₹${(item.price * item.quantity).toFixed(2)}</div>
        </div>
      `;
    });

    summaryHTML += '</div>';

    // Add subtotal, shipping, tax, and total
    summaryHTML += `
      <div class="summary-row">
        <div class="summary-label">Subtotal:</div>
        <div class="summary-value">₹${shopping.subtotal.toFixed(2)}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Shipping:</div>
        <div class="summary-value">₹${shopping.shipping.toFixed(2)}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Tax:</div>
        <div class="summary-value">₹${shopping.tax.toFixed(2)}</div>
      </div>
      <div class="summary-row total-row">
        <div class="summary-label">Total:</div>
        <div class="summary-value">₹${shopping.total.toFixed(2)}</div>
      </div>
    `;
  } else if (paymentType === 'travel') {
    const travel = paymentDetails.travel;

    summaryHTML = `
      <div class="summary-row">
        <div class="summary-label">${travel.type === 'package' ? 'Package:' : 'Flight:'}</div>
        <div class="summary-value">${travel.title}</div>
      </div>
    `;

    if (travel.type !== 'package') {
      summaryHTML += `
        <div class="summary-row">
          <div class="summary-label">Route:</div>
          <div class="summary-value">${travel.from} to ${travel.to}</div>
        </div>
      `;
    }

    summaryHTML += `
      <div class="summary-row">
        <div class="summary-label">Date:</div>
        <div class="summary-value">${travel.date}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Passengers:</div>
        <div class="summary-value">${travel.passengers} ${travel.passengers === 1 ? 'Person' : 'People'}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Class:</div>
        <div class="summary-value">${travel.class}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Base Fare:</div>
        <div class="summary-value">₹${travel.baseFare.toFixed(2)}</div>
      </div>
      <div class="summary-row">
        <div class="summary-label">Taxes & Fees:</div>
        <div class="summary-value">₹${travel.taxes.toFixed(2)}</div>
      </div>
      <div class="summary-row total-row">
        <div class="summary-label">Total:</div>
        <div class="summary-value">₹${travel.total.toFixed(2)}</div>
      </div>
    `;
  }

  summaryElement.innerHTML = summaryHTML;
}

// Initialize form validation
function initFormValidation() {
  const cardNumberInput = document.getElementById('card-number');
  const expiryDateInput = document.getElementById('expiry-date');
  const cvvInput = document.getElementById('cvv');

  // Format card number as user types (add spaces every 4 digits)
  if (cardNumberInput) {
    cardNumberInput.addEventListener('input', function(e) {
      let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
      let formattedValue = '';

      for (let i = 0; i < value.length; i++) {
        if (i > 0 && i % 4 === 0) {
          formattedValue += ' ';
        }
        formattedValue += value[i];
      }

      e.target.value = formattedValue;
    });
  }

  // Format expiry date as MM/YY
  if (expiryDateInput) {
    expiryDateInput.addEventListener('input', function(e) {
      let value = e.target.value.replace(/[^0-9]/gi, '');

      if (value.length > 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
      }

      e.target.value = value;
    });
  }

  // Limit CVV to 3 digits
  if (cvvInput) {
    cvvInput.addEventListener('input', function(e) {
      e.target.value = e.target.value.replace(/[^0-9]/gi, '').substring(0, 3);
    });
  }
}

// Initialize payment buttons
function initPaymentButtons(paymentType, paymentDetails) {
  const processPaymentBtn = document.getElementById('process-payment-btn');
  const cancelPaymentBtn = document.getElementById('cancel-payment-btn');
  const returnBtn = document.getElementById('return-btn');

  // Process payment button
  if (processPaymentBtn) {
    processPaymentBtn.addEventListener('click', function() {
      // Validate form
      if (!validatePaymentForm()) {
        return;
      }

      // Update status bar
      updateStatusBar('Processing payment...', 'Connected');

      // Disable the button to prevent multiple clicks
      processPaymentBtn.disabled = true;
      processPaymentBtn.textContent = 'Processing...';

      // Simulate payment processing
      setTimeout(() => {
        // Hide the payment content
        document.querySelector('.payment-content').style.display = 'none';

        // Show confirmation
        showPaymentConfirmation(paymentType, paymentDetails);

        // Update status bar
        updateStatusBar('Payment successful', 'Connected');
      }, 1500);
    });
  }

  // Cancel payment button
  if (cancelPaymentBtn) {
    cancelPaymentBtn.addEventListener('click', function() {
      // Go back to previous page
      window.history.back();
    });
  }

  // Return button (after payment confirmation)
  if (returnBtn) {
    returnBtn.addEventListener('click', function() {
      // Clear payment details from localStorage
      localStorage.removeItem('paymentType');
      localStorage.removeItem('paymentDetails');

      // Go to home page
      window.location.href = 'index.html';
    });
  }
}

// Validate payment form
function validatePaymentForm() {
  const cardName = document.getElementById('card-name').value.trim();
  const cardNumber = document.getElementById('card-number').value.trim();
  const expiryDate = document.getElementById('expiry-date').value.trim();
  const cvv = document.getElementById('cvv').value.trim();
  const billingAddress = document.getElementById('billing-address').value.trim();

  // Simple validation
  if (!cardName) {
    alert('Please enter the name on the card.');
    return false;
  }

  if (!cardNumber || cardNumber.replace(/\s+/g, '').length < 16) {
    alert('Please enter a valid 16-digit card number.');
    return false;
  }

  if (!expiryDate || expiryDate.length < 5) {
    alert('Please enter a valid expiry date (MM/YY).');
    return false;
  }

  if (!cvv || cvv.length < 3) {
    alert('Please enter a valid 3-digit CVV.');
    return false;
  }

  if (!billingAddress) {
    alert('Please enter your billing address.');
    return false;
  }

  return true;
}

// Show payment confirmation
function showPaymentConfirmation(paymentType, paymentDetails) {
  // Hide the payment content
  document.querySelector('.payment-content').style.display = 'none';

  // Show confirmation section
  const confirmationSection = document.getElementById('payment-confirmation-section');
  confirmationSection.style.display = 'flex';

  // Make sure the confirmation section is visible
  confirmationSection.scrollIntoView({ behavior: 'smooth' });

  // Update confirmation message based on payment type
  const confirmationMessage = document.getElementById('confirmation-message');
  const confirmationDetails = document.getElementById('confirmation-details');

  if (paymentType === 'movie') {
    const movie = paymentDetails.movie;
    const ticketId = `BNG${Math.floor(100000 + Math.random() * 900000)}`;

    // Set confirmation message
    confirmationMessage.innerHTML = `
      <strong>Your movie ticket booking is confirmed!</strong><br>
      Please arrive 15 minutes before the show time. Show this confirmation at the ticket counter.
    `;

    // Set confirmation details
    confirmationDetails.innerHTML = `
      <div class="confirmation-detail-row" style="background-color: #E5F1FB; padding: 10px; margin-bottom: 15px; border-radius: 5px;">
        <div class="confirmation-detail-label">Ticket ID:</div>
        <div class="confirmation-detail-value" style="font-size: 16px; font-weight: bold; color: #0A246A;">${ticketId}</div>
      </div>
      <div class="confirmation-detail-row" style="background-color: #E5F1FB; padding: 10px; margin-bottom: 15px; border-radius: 5px;">
        <div class="confirmation-detail-label">Show Timing:</div>
        <div class="confirmation-detail-value" style="font-size: 16px; font-weight: bold; color: #0A246A;">${movie.time} | ${movie.date}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Movie:</div>
        <div class="confirmation-detail-value">${movie.title}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Theater:</div>
        <div class="confirmation-detail-value">${movie.theater}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Tickets:</div>
        <div class="confirmation-detail-value">${movie.tickets}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Amount Paid:</div>
        <div class="confirmation-detail-value">₹${movie.total.toFixed(2)}</div>
      </div>
    `;
  } else if (paymentType === 'shopping') {
    const shopping = paymentDetails.shopping;

    // Set confirmation message
    confirmationMessage.textContent = 'Thanks for your purchase! Your order has been confirmed and is on the way.';

    // Set confirmation details
    let detailsHTML = `
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Order ID:</div>
        <div class="confirmation-detail-value">BNG${Math.floor(100000 + Math.random() * 900000)}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Items:</div>
        <div class="confirmation-detail-value">${shopping.items.length}</div>
      </div>
    `;

    // Add each item
    shopping.items.forEach(item => {
      detailsHTML += `
        <div class="confirmation-detail-row">
          <div class="confirmation-detail-label">${item.name} x ${item.quantity}:</div>
          <div class="confirmation-detail-value">₹${(item.price * item.quantity).toFixed(2)}</div>
        </div>
      `;
    });

    // Add total
    detailsHTML += `
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Amount Paid:</div>
        <div class="confirmation-detail-value">₹${shopping.total.toFixed(2)}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Estimated Delivery:</div>
        <div class="confirmation-detail-value">3-5 business days</div>
      </div>
    `;

    confirmationDetails.innerHTML = detailsHTML;
  } else if (paymentType === 'travel') {
    const travel = paymentDetails.travel;
    const bookingId = `BNG${Math.floor(100000 + Math.random() * 900000)}`;

    // Set confirmation message based on travel type
    if (travel.type === 'package') {
      confirmationMessage.innerHTML = `
        <strong>Your travel package booking is confirmed!</strong><br>
        Your itinerary details will be sent to your email. Please check your inbox.
      `;
    } else {
      confirmationMessage.innerHTML = `
        <strong>Your flight booking is confirmed!</strong><br>
        Please arrive at the airport at least 2 hours before departure. E-tickets will be sent to your email.
      `;
    }

    // Set confirmation details
    let detailsHTML = `
      <div class="confirmation-detail-row" style="background-color: #E5F1FB; padding: 10px; margin-bottom: 15px; border-radius: 5px;">
        <div class="confirmation-detail-label">Booking ID:</div>
        <div class="confirmation-detail-value" style="font-size: 16px; font-weight: bold; color: #0A246A;">${bookingId}</div>
      </div>
    `;

    if (travel.type === 'package') {
      detailsHTML += `
        <div class="confirmation-detail-row" style="background-color: #E5F1FB; padding: 10px; margin-bottom: 15px; border-radius: 5px;">
          <div class="confirmation-detail-label">Package:</div>
          <div class="confirmation-detail-value" style="font-size: 16px; font-weight: bold; color: #0A246A;">${travel.title}</div>
        </div>
      `;
    } else {
      detailsHTML += `
        <div class="confirmation-detail-row" style="background-color: #E5F1FB; padding: 10px; margin-bottom: 15px; border-radius: 5px;">
          <div class="confirmation-detail-label">Flight:</div>
          <div class="confirmation-detail-value" style="font-size: 16px; font-weight: bold; color: #0A246A;">${travel.title}</div>
        </div>
        <div class="confirmation-detail-row">
          <div class="confirmation-detail-label">Route:</div>
          <div class="confirmation-detail-value">${travel.from} to ${travel.to}</div>
        </div>
      `;
    }

    detailsHTML += `
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Date:</div>
        <div class="confirmation-detail-value">${travel.date}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Passengers:</div>
        <div class="confirmation-detail-value">${travel.passengers} ${travel.passengers === 1 ? 'Person' : 'People'}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Class:</div>
        <div class="confirmation-detail-value">${travel.class}</div>
      </div>
      <div class="confirmation-detail-row">
        <div class="confirmation-detail-label">Amount Paid:</div>
        <div class="confirmation-detail-value">₹${travel.total.toFixed(2)}</div>
      </div>
    `;

    confirmationDetails.innerHTML = detailsHTML;
  }
}

// Update the entertainment.js file to redirect to payment page
function updateEntertainmentJS() {
  // This function is not actually called, but shows how to modify the entertainment.js file
  // to redirect to the payment page instead of showing an alert

  // In the initConfirmButton function of entertainment.js, replace the alert with:
  /*
  // Store payment details in localStorage
  const paymentDetails = {
    movie: {
      title: movieTitle,
      date: date,
      theater: theater,
      time: time,
      tickets: parseInt(tickets),
      ticketPrice: 250,
      total: parseInt(tickets) * 250
    }
  };

  localStorage.setItem('paymentType', 'movie');
  localStorage.setItem('paymentDetails', JSON.stringify(paymentDetails));

  // Navigate to payment page
  window.location.href = 'payment.html';
  */
}
