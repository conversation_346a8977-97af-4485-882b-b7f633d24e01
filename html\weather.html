<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingo Weather</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/weather.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="weather-page">
        <div class="weather-header-bar">
          <h1>Bingo Weather</h1>
        </div>

        <div class="weather-content">
          <div class="popular-cities">
            <h3>Indian Cities</h3>
            <div class="city-list" id="cities-container">
              <!-- Cities will be loaded from cities.html -->
              <div class="loading-spinner">⟳</div>
              <p>Loading cities list...</p>
            </div>
          </div>

          <div class="weather-display">
            <div class="weather-loading">
              <div class="loading-spinner">⟳</div>
              <p>Loading weather data for Mumbai...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/cities-data.js"></script>
  <script src="../js/weather.js"></script>
</body>
</html>
