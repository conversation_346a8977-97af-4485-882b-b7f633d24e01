<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingo Shopping</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/shopping.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="shopping-page">
        <div class="shopping-header-bar">
          <h1>Bingo Shopping</h1>
          <div class="cart-container">
            <div class="cart-icon" id="cart-icon">
              🛒 <span class="cart-count" id="cart-count">0</span>
            </div>
          </div>
        </div>

        <div class="shopping-content">
          <!-- Categories Navigation -->
          <div class="categories-nav">
            <h2>Categories</h2>
            <ul class="category-list">
              <li class="category-item active" data-category="all">All Products</li>
              <li class="category-item" data-category="electronics">Electronics</li>
              <li class="category-item" data-category="clothing">Clothing</li>
              <li class="category-item" data-category="home">Home & Kitchen</li>
              <li class="category-item" data-category="books">Books</li>
              <li class="category-item" data-category="toys">Toys & Games</li>
              <li class="category-item" data-category="sports">Sports & Fitness</li>
              <li class="category-item" data-category="beauty">Beauty & Personal Care</li>
              <li class="category-item" data-category="groceries">Groceries & Gourmet</li>
            </ul>
          </div>

          <!-- Products Display -->
          <div class="products-container">
            <div class="products-header">
              <h2>All Products</h2>
              <div class="sort-options">
                <label for="sort-select">Sort by:</label>
                <select id="sort-select" class="sort-select">
                  <option value="featured">Featured</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Customer Rating</option>
                </select>
              </div>
            </div>

            <!-- Products Grid -->
            <div class="products-grid" id="products-grid">
              <!-- Products will be dynamically added here -->
            </div>
          </div>
        </div>

        <!-- Cart Popup -->
        <div class="cart-popup" id="cart-popup">
          <div class="cart-popup-header">
            <h3>Your Shopping Cart</h3>
            <button class="close-cart" id="close-cart">×</button>
          </div>
          <div class="cart-items" id="cart-items">
            <!-- Cart items will be dynamically added here -->
            <div class="empty-cart-message">Your cart is empty</div>
          </div>
          <div class="cart-summary">
            <div class="cart-total">
              <span>Total:</span>
              <span id="cart-total-amount">₹0.00</span>
            </div>
            <button class="checkout-button" id="checkout-button">Proceed to Checkout</button>
          </div>
        </div>

        <!-- Overlay for Cart Popup -->
        <div class="overlay" id="overlay"></div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/shopping.js"></script>
</body>
</html>
