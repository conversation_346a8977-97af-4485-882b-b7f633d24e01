<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingopedia - The Free Encyclopedia</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/bingopedia.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="bingopedia-page">
        <div class="bingopedia-header-bar">
          <h1>Bingopedia</h1>
          <div class="bingopedia-subtitle">The Free Encyclopedia</div>
        </div>

        <div class="bingopedia-content">
          <!-- Left Sidebar -->
          <div class="bingopedia-sidebar">
            <div class="bingopedia-logo">
              <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                <!-- Main circle -->
                <circle cx="60" cy="60" r="55" fill="#ECE9D8" stroke="#0A246A" stroke-width="3"/>

                <!-- Book pages -->
                <path d="M30 40 L30 80 L90 80 L90 40 Z" fill="white" stroke="#0A246A" stroke-width="2"/>
                <path d="M60 40 L60 80" stroke="#0A246A" stroke-width="1" stroke-dasharray="2,2"/>

                <!-- Lines representing text -->
                <line x1="35" y1="50" x2="55" y2="50" stroke="#3A6EA5" stroke-width="2"/>
                <line x1="35" y1="55" x2="55" y2="55" stroke="#3A6EA5" stroke-width="2"/>
                <line x1="35" y1="60" x2="50" y2="60" stroke="#3A6EA5" stroke-width="2"/>
                <line x1="35" y1="65" x2="55" y2="65" stroke="#3A6EA5" stroke-width="2"/>
                <line x1="35" y1="70" x2="45" y2="70" stroke="#3A6EA5" stroke-width="2"/>

                <line x1="65" y1="50" x2="85" y2="50" stroke="#3A6EA5" stroke-width="2"/>
                <line x1="65" y1="55" x2="85" y2="55" stroke="#3A6EA5" stroke-width="2"/>
                <line x1="65" y1="60" x2="80" y2="60" stroke="#3A6EA5" stroke-width="2"/>
                <line x1="65" y1="65" x2="85" y2="65" stroke="#3A6EA5" stroke-width="2"/>
                <line x1="65" y1="70" x2="75" y2="70" stroke="#3A6EA5" stroke-width="2"/>

                <!-- Magnifying glass -->
                <circle cx="75" cy="35" r="12" fill="none" stroke="#0A246A" stroke-width="3"/>
                <line x1="85" y1="45" x2="95" y2="55" stroke="#0A246A" stroke-width="3" stroke-linecap="round"/>

                <!-- Text -->
                <text x="60" y="95" font-family="Tahoma, Arial, sans-serif" font-size="12" text-anchor="middle" fill="#0A246A" font-weight="bold">BINGOPEDIA</text>
              </svg>
            </div>

            <div class="sidebar-section">
              <h3>Navigation</h3>
              <ul class="sidebar-links">
                <li><a href="#" id="main-page-link">Main Page</a></li>
                <li><a href="#" id="random-article-link">Random Article</a></li>
                <li><a href="#" id="all-articles-link">All Articles</a></li>
              </ul>
            </div>

            <div class="sidebar-section">
              <h3>Search Bingopedia</h3>
              <div class="sidebar-search">
                <input type="text" id="sidebar-search-input" placeholder="Search...">
                <button id="sidebar-search-button" style="width:40px; min-width:40px; flex-shrink:0;">Go</button>
              </div>
            </div>

            <div class="sidebar-section">
              <h3>Categories</h3>
              <ul class="sidebar-links" id="category-links">
                <li><a href="#" data-category="technology">Technology</a></li>
                <li><a href="#" data-category="science">Science</a></li>
                <li><a href="#" data-category="history">History</a></li>
                <li><a href="#" data-category="geography">Geography</a></li>
                <li><a href="#" data-category="culture">Culture</a></li>
                <li><a href="#" data-category="arts">Arts</a></li>
                <li><a href="#" data-category="philosophy">Philosophy</a></li>
                <li><a href="#" data-category="politics">Politics</a></li>
                <li><a href="#" data-category="sports">Sports</a></li>
                <li><a href="#" data-category="medicine">Medicine</a></li>
                <li><a href="#" data-category="people">People</a></li>
                <li><a href="#" data-category="events">Events</a></li>
                <li><a href="#" data-category="organizations">Organizations</a></li>
                <li><a href="#" data-category="mathematics">Mathematics</a></li>
                <li><a href="#" data-category="art">Art</a></li>
                <li><a href="#" data-category="public health">Public Health</a></li>
              </ul>
            </div>


          </div>

          <!-- Main Content Area -->
          <div class="bingopedia-main">
            <!-- Article Header -->
            <div class="article-header">
              <h1 id="article-title">Welcome to Bingopedia</h1>
              <!-- Article tabs removed -->
            </div>

            <!-- Article Content -->
            <div class="article-content" id="article-content">
              <!-- This will be populated by JavaScript -->
              <div class="loading-indicator" id="loading-indicator" style="display: none;">
                <div class="spinner">⟳</div>
                <p>Loading article...</p>
              </div>

              <div class="welcome-message" id="welcome-message">
                <p>Welcome to Bingopedia, the free encyclopedia that anyone can edit.</p>
                <p>Bingopedia is a multilingual online encyclopedia with exclusively free content, created through open collaboration.</p>
                <p>Select an article from the list below or use the search function to start exploring.</p>

                <h2>Featured Articles</h2>
                <div class="featured-articles" id="featured-articles">
                  <!-- This will be populated by JavaScript -->
                  <div class="loading-spinner">⟳</div>
                  <p>Loading featured articles...</p>
                </div>
              </div>
            </div>

            <!-- Article Footer -->
            <div class="article-footer">
              <div class="article-categories" id="article-categories">
                <!-- Categories will be added here -->
              </div>
              <div class="article-references" id="article-references">
                <!-- References will be added here -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <!-- Article Popup -->
  <div id="article-popup" class="article-popup">
    <div class="article-popup-content">
      <div class="article-popup-header">
        <h2 id="popup-article-title">Article Title</h2>
        <button id="popup-close-button" class="popup-close-button">&times;</button>
      </div>
      <div class="article-popup-body" id="popup-article-content">
        <!-- Article content will be loaded here -->
      </div>
      <div class="article-popup-footer">
        <div id="popup-article-categories" class="popup-article-categories">
          <!-- Categories will be added here -->
        </div>
      </div>
    </div>
  </div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/preloaded-article-content.js"></script>
  <script src="../js/preloaded-article-index.js"></script>
  <script src="../js/bingopedia.js"></script>
</body>
</html>
