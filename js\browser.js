// Browser functionality
// This file contains the core browser functionality
// It is initialized from browser-chrome-inline.js after the chrome HTML is loaded

function initBrowser() {
  // Initialize menu bar
  initMenuBar();

  // Initialize toolbar
  initToolbar();

  // Initialize bookmarks
  initBookmarks();

  // Initialize zoom controls
  initZoomControls();

  // Status bar is initialized in status-bar-inline.js
}

// Menu Bar Functions
function initMenuBar() {
  // Add event listeners for menu items if needed
  const menuItems = document.querySelectorAll('.menu-item');
  menuItems.forEach(item => {
    item.addEventListener('click', function() {
      // Handle menu item click
      console.log('Menu item clicked:', item.textContent);
    });
  });
}

// Toolbar Functions
function initToolbar() {
  // Back button
  const backButton = document.getElementById('back-button');
  if (backButton) {
    backButton.addEventListener('click', handleBack);
  }

  // Forward button
  const forwardButton = document.getElementById('forward-button');
  if (forwardButton) {
    forwardButton.addEventListener('click', handleForward);
  }

  // Refresh button
  const refreshButton = document.getElementById('refresh-button');
  if (refreshButton) {
    refreshButton.addEventListener('click', handleRefresh);
  }

  // Stop button
  const stopButton = document.getElementById('stop-button');
  if (stopButton) {
    stopButton.addEventListener('click', handleStop);
  }

  // Home button
  const homeButton = document.getElementById('home-button');
  if (homeButton) {
    homeButton.addEventListener('click', handleHome);
  }

  // SSL lock is now just an indicator, not a button

  // Address bar
  const addressInput = document.getElementById('address-input');
  if (addressInput) {
    addressInput.addEventListener('keydown', function(event) {
      if (event.key === 'Enter') {
        handleNavigate(addressInput.value);
      }
    });
  }

  // Go button
  const goButton = document.getElementById('go-button');
  if (goButton) {
    goButton.addEventListener('click', function() {
      const addressInput = document.getElementById('address-input');
      if (addressInput) {
        handleNavigate(addressInput.value);
      }
    });
  }
}

// Bookmark Functions
function initBookmarks() {
  const bookmarks = document.querySelectorAll('.bookmark');
  bookmarks.forEach(bookmark => {
    bookmark.addEventListener('click', function() {
      const url = bookmark.getAttribute('data-url');
      if (url) {
        handleNavigate(url);
      }
    });
  });
}

// Navigation Functions
function handleBack() {
  console.log('Back button clicked');
  updateStatusBar('Loading...', 'Connected');

  // Simulate going back in history
  window.history.back();

  // Update status bar after a short delay
  setTimeout(() => {
    updateStatusBar('Done', 'Connected');
  }, 300);
}

function handleForward() {
  console.log('Forward button clicked');
  updateStatusBar('Loading...', 'Connected');

  // Simulate going forward in history
  window.history.forward();

  // Update status bar after a short delay
  setTimeout(() => {
    updateStatusBar('Done', 'Connected');
  }, 300);
}

function handleRefresh() {
  console.log('Refresh button clicked');
  updateStatusBar('Loading...', 'Connected');

  // Simulate refreshing the page
  location.reload();

  // Update status bar after a short delay
  setTimeout(() => {
    updateStatusBar('Done', 'Connected');
  }, 300);
}

function handleStop() {
  console.log('Stop button clicked');
  updateStatusBar('Stopped', 'Connected');
}

function handleHome() {
  console.log('Home button clicked');
  updateStatusBar('Loading...', 'Connected');

  // Navigate to home page
  window.location.href = 'index.html';

  // Update status bar after a short delay
  setTimeout(() => {
    updateStatusBar('Done', 'Connected');
  }, 300);
}

// SSL lock is now just an indicator, not a button with functionality

function handleNavigate(url) {
  console.log('Navigating to:', url);
  updateStatusBar('Loading...', 'Connected');

  // Process the URL
  let processedUrl = url;

  // If it's a known URL, navigate to the corresponding page
  if (url === 'https://bingo.com' || url === 'http://bingo.com') {
    window.location.href = 'index.html';
  } else if (url === 'https://bingo.com/news' || url === 'http://bingo.com/news') {
    window.location.href = 'news.html';
  } else if (url === 'https://bingo.com/weather' || url === 'http://bingo.com/weather') {
    window.location.href = 'weather.html';
  } else if (url === 'https://bingo.com/finance' || url === 'http://bingo.com/finance') {
    window.location.href = 'finance.html';
  } else if (url === 'https://bingo.com/maps' || url === 'http://bingo.com/maps') {
    window.location.href = 'maps.html';
  } else if (url === 'https://bingo.com/sports' || url === 'http://bingo.com/sports') {
    window.location.href = 'sports.html';
  } else if (url === 'https://bingo.com/games' || url === 'http://bingo.com/games') {
    window.location.href = 'games.html';
  } else if (url === 'https://bingo.com/shopping' || url === 'http://bingo.com/shopping') {
    window.location.href = 'shopping.html';
  } else if (url === 'https://bingo.com/music' || url === 'http://bingo.com/music') {
    window.location.href = 'music.html';
  } else if (url === 'https://bingo.com/chat' || url === 'http://bingo.com/chat') {
    window.location.href = 'chat.html';
  } else if (url === 'https://bingo.com/bingopedia' || url === 'http://bingo.com/bingopedia') {
    window.location.href = 'bingopedia.html';
  } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
    // If it doesn't start with http:// or https://, assume it's a search
    window.location.href = 'index.html';
  } else {
    // For other URLs, just reload the current page
    // In a real browser, this would navigate to the URL
    console.log('Unknown URL:', url);

    // For demo purposes, navigate to the error page
    // window.location.href = 'error.html';

    // For now, just stay on the current page
    updateStatusBar('Error: Cannot navigate to ' + url, 'Connected');
    return;
  }

  // Update address bar
  const addressInput = document.getElementById('address-input');
  if (addressInput) {
    addressInput.value = processedUrl;
  }

  // Update status bar after a short delay
  setTimeout(() => {
    updateStatusBar('Done', 'Connected');
  }, 300);
}

// Status Bar Functions are now in status-bar-inline.js

// Zoom Control Functions
function initZoomControls() {
  // Get zoom level from localStorage or use default (100%)
  const savedZoomLevel = localStorage.getItem('browserZoomLevel');
  let currentZoomLevel = savedZoomLevel ? parseFloat(savedZoomLevel) : 100;

  // Apply the saved zoom level on page load
  applyZoom(currentZoomLevel);
  updateZoomDisplay(currentZoomLevel);

  // Zoom in button
  const zoomInButton = document.getElementById('zoom-in-button');
  if (zoomInButton) {
    zoomInButton.addEventListener('click', function() {
      // Increase zoom by 10%
      currentZoomLevel += 10;
      // Cap at 200%
      if (currentZoomLevel > 200) currentZoomLevel = 200;

      applyZoom(currentZoomLevel);
      updateZoomDisplay(currentZoomLevel);
      saveZoomLevel(currentZoomLevel);
    });
  }

  // Zoom out button
  const zoomOutButton = document.getElementById('zoom-out-button');
  if (zoomOutButton) {
    zoomOutButton.addEventListener('click', function() {
      // Decrease zoom by 10%
      currentZoomLevel -= 10;
      // Minimum 50%
      if (currentZoomLevel < 50) currentZoomLevel = 50;

      applyZoom(currentZoomLevel);
      updateZoomDisplay(currentZoomLevel);
      saveZoomLevel(currentZoomLevel);
    });
  }

  // Reset zoom button
  const zoomResetButton = document.getElementById('zoom-reset-button');
  if (zoomResetButton) {
    zoomResetButton.addEventListener('click', function() {
      // Reset to 100%
      currentZoomLevel = 100;

      applyZoom(currentZoomLevel);
      updateZoomDisplay(currentZoomLevel);
      saveZoomLevel(currentZoomLevel);
    });
  }
}

// Apply zoom to the content area only - optimized for performance
function applyZoom(zoomLevel) {
  // First, ensure we have a zoom container for each browser-content
  ensureZoomContainers();

  // Get all zoom containers
  const zoomContainers = document.querySelectorAll('.zoom-container');

  if (zoomContainers.length > 0) {
    // Calculate the scale factor once
    const scaleFactor = zoomLevel / 100;

    // Apply zoom to each container using CSS transform
    for (let i = 0; i < zoomContainers.length; i++) {
      const container = zoomContainers[i];

      // Apply the scale transform
      container.style.transform = `scale(${scaleFactor})`;

      // Get the browser content container (parent)
      const browserContent = container.closest('.browser-content');
      if (browserContent) {
        // When zoomed in (> 100%), we need to adjust the container width
        if (zoomLevel > 100) {
          // Set the container width to accommodate the zoomed content
          // This ensures horizontal scrolling works properly
          container.style.width = `${100 / scaleFactor}%`;

          // Ensure the browser content has proper overflow
          browserContent.style.overflow = 'auto';
        } else {
          // At 100% or less, reset to default width
          container.style.width = '100%';
        }
      }
    }
  }
}

// Ensure each browser-content has a zoom container - optimized for performance
function ensureZoomContainers() {
  const browserContents = document.querySelectorAll('.browser-content');

  for (let i = 0; i < browserContents.length; i++) {
    const content = browserContents[i];

    // Check if this content already has a zoom container
    if (!content.querySelector('.zoom-container')) {
      // Create a zoom container
      const zoomContainer = document.createElement('div');
      zoomContainer.className = 'zoom-container';

      // Use DocumentFragment for better performance
      const fragment = document.createDocumentFragment();

      // Move all children to the fragment
      while (content.firstChild) {
        fragment.appendChild(content.firstChild);
      }

      // Append all children to the zoom container at once
      zoomContainer.appendChild(fragment);

      // Add the zoom container to the content
      content.appendChild(zoomContainer);
    }
  }
}

// Update the zoom level display
function updateZoomDisplay(zoomLevel) {
  const zoomLevelDisplay = document.getElementById('zoom-level');
  if (zoomLevelDisplay) {
    zoomLevelDisplay.textContent = `${zoomLevel}%`;
  }
}

// Save zoom level to localStorage
function saveZoomLevel(zoomLevel) {
  localStorage.setItem('browserZoomLevel', zoomLevel.toString());
}

// Apply the saved zoom level when the page loads - optimized for performance
document.addEventListener('DOMContentLoaded', function() {
  // Check if we're in a frame - if so, don't apply zoom (for popups)
  if (window.self === window.top) {
    // Get saved zoom level first to avoid unnecessary work
    const savedZoomLevel = localStorage.getItem('browserZoomLevel');
    if (savedZoomLevel) {
      const zoomLevel = parseFloat(savedZoomLevel);

      // Use requestAnimationFrame for better performance than setTimeout
      requestAnimationFrame(() => {
        // Always ensure zoom containers are created first
        ensureZoomContainers();

        // Apply the zoom
        applyZoom(zoomLevel);

        // Update zoom display if it exists (main page)
        const zoomLevelDisplay = document.getElementById('zoom-level');
        if (zoomLevelDisplay) {
          updateZoomDisplay(zoomLevel);
        }
      });
    } else {
      // If no saved zoom level, still ensure containers are created
      // but don't apply any zoom (defaults to 100%)
      requestAnimationFrame(ensureZoomContainers);
    }
  }
});
