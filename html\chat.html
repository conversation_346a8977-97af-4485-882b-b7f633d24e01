<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON>t</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/chat.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="chat-page">
        <div class="chat-header-bar">
          <h1>Bingo Chat</h1>
        </div>

        <div class="chat-content">
          <!-- Contacts Sidebar -->
          <div class="contacts-sidebar">
            <div class="contacts-header">
              <div class="user-profile">
                <div class="user-avatar">
                  <img src="../img/chat/user-avatar.svg" alt="Arjun's Avatar">
                </div>
                <div class="user-info">
                  <div class="user-name">Arjun Patil</div>
                  <div class="user-status">
                    <span class="status-indicator online"></span>
                    <span class="status-text">Online</span>
                  </div>
                </div>
              </div>
              <div class="search-contacts">
                <input type="text" placeholder="Search contacts..." id="contact-search">
              </div>
            </div>

            <div class="contacts-list" id="contacts-list">
              <!-- Contact items will be populated by JS -->
              <div class="contact-item active" data-contact="doctor">
                <div class="contact-avatar">
                  <img src="../img/chat/doctor-avatar.svg" alt="Dr. Vikram">
                  <span class="status-dot online"></span>
                </div>
                <div class="contact-info">
                  <div class="contact-name">Dr. Vikram Sharma</div>
                  <div class="contact-last-message">How are you feeling today, Arjun?</div>
                </div>
                <div class="contact-time">2m</div>
              </div>

              <div class="contact-item" data-contact="rahul">
                <div class="contact-avatar">
                  <img src="../img/chat/rahul-avatar.svg" alt="Rahul">
                  <span class="status-dot online"></span>
                </div>
                <div class="contact-info">
                  <div class="contact-name">Rahul Verma</div>
                  <div class="contact-last-message">Bhai, movie dekhne chalega weekend pe?</div>
                </div>
                <div class="contact-time">15m</div>
              </div>

              <div class="contact-item" data-contact="priya">
                <div class="contact-avatar">
                  <img src="../img/chat/priya-avatar.svg" alt="Priya">
                  <span class="status-dot away"></span>
                </div>
                <div class="contact-info">
                  <div class="contact-name">Priya Patel</div>
                  <div class="contact-last-message">College assignment complete kar liya?</div>
                </div>
                <div class="contact-time">1h</div>
              </div>

              <div class="contact-item" data-contact="amit">
                <div class="contact-avatar">
                  <img src="../img/chat/amit-avatar.svg" alt="Amit">
                  <span class="status-dot offline"></span>
                </div>
                <div class="contact-info">
                  <div class="contact-name">Amit Desai</div>
                  <div class="contact-last-message">Cricket match ka score dekha?</div>
                </div>
                <div class="contact-time">2d</div>
              </div>

              <div class="contact-item" data-contact="neha">
                <div class="contact-avatar">
                  <img src="../img/chat/neha-avatar.svg" alt="Neha">
                  <span class="status-dot online"></span>
                </div>
                <div class="contact-info">
                  <div class="contact-name">Neha Gupta</div>
                  <div class="contact-last-message">Kal college mein milte hain</div>
                </div>
                <div class="contact-time">5h</div>
              </div>
            </div>
          </div>

          <!-- Chat Window -->
          <div class="chat-window">
            <div class="chat-window-header">
              <div class="chat-contact-info">
                <div class="chat-contact-avatar">
                  <img src="../img/chat/doctor-avatar.svg" alt="Dr. Vikram" id="current-chat-avatar">
                  <span class="status-dot online" id="current-chat-status"></span>
                </div>
                <div class="chat-contact-details">
                  <div class="chat-contact-name" id="current-chat-name">Dr. Vikram Sharma</div>
                  <div class="chat-contact-status" id="current-chat-status-text">Online</div>
                </div>
              </div>
              <div class="chat-actions">
                <button class="chat-action-button" title="Voice Call">📞</button>
                <button class="chat-action-button" title="Video Call">📹</button>
                <button class="chat-action-button" title="More Options">⋮</button>
              </div>
            </div>

            <div class="chat-messages" id="chat-messages">
              <!-- Messages will be populated by JS -->
            </div>

            <div class="chat-input-area">
              <div class="typing-indicator" id="typing-indicator">
                <span class="typing-dot"></span>
                <span class="typing-dot"></span>
                <span class="typing-dot"></span>
              </div>
              <div class="chat-input-container">
                <input type="text" id="chat-input" placeholder="Type a message...">
                <button id="send-button">Send</button>
              </div>
              <div class="chat-input-actions">
                <button class="input-action-button" title="Emoji">😊</button>
                <button class="input-action-button" title="Attach File">📎</button>
                <button class="input-action-button" title="Voice Message">🎤</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <!-- Chat notification sound -->
  <audio id="message-sound" src="../audio/chat-notification.mp3" preload="auto"></audio>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/hinglish-generator.js"></script>
  <script src="../js/chat-simple.js"></script>
</body>
</html>
