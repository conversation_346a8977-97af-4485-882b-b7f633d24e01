// Bubble Shooter Game functionality
let bsCanvas, bsCtx;
let bsGameRunning = false;
let bsGameOver = false;
let bsScore = 0;
let bsLevel = 1;
let bsBubblesLeft = 50;
let bsGameLoop;
let bsDifficulty = 'easy'; // easy, medium, hard

// Game objects
let bsGrid = [];
let bsShooter = {
  x: 300, // Updated from 240 to match new canvas width of 600
  y: 550,
  angle: -Math.PI / 2, // Point upward
  currentBubble: null,
  nextBubble: null
};
let bsFiredBubble = null;
let bsPopEffects = []; // Visual effects for popping bubbles

// Game constants
const bsBubbleRadius = 20;
const bsGridRows = 10;
const bsGridCols = 12;
const bsGridOffsetX = 80; // Updated from 20 to center the grid in the wider canvas
const bsGridOffsetY = 40;
const bsColors = ['#FF5252', '#4CAF50', '#2196F3', '#FFC107', '#9C27B0', '#FF9800'];
const bsColorNames = ['red', 'green', 'blue', 'yellow', 'purple', 'orange'];

// Initialize the Bubble Shooter game
function initBubbleShooterGame() {
  bsCanvas = document.getElementById('bubbleshooter-canvas');
  bsCtx = bsCanvas.getContext('2d');

  // Add event listeners
  bsCanvas.addEventListener('mousemove', handleMouseMove);
  bsCanvas.addEventListener('click', handleMouseClick);
  document.addEventListener('keydown', handleKeyDown);

  // Initialize difficulty buttons
  initBubbleShooterDifficultyButtons();

  // Reset game state
  resetBubbleShooterGame();
}

// Initialize difficulty buttons
function initBubbleShooterDifficultyButtons() {
  const easyButton = document.getElementById('bs-easy-mode');
  const mediumButton = document.getElementById('bs-medium-mode');
  const hardButton = document.getElementById('bs-hard-mode');

  // Function to set active button
  function setActiveButton(button) {
    // Remove active class from all buttons
    document.querySelectorAll('.difficulty-button').forEach(btn => {
      if (btn.id.startsWith('bs-')) {
        btn.classList.remove('active');
      }
    });

    // Add active class to selected button
    button.classList.add('active');
  }

  // Add event listeners
  if (easyButton) {
    easyButton.addEventListener('click', function() {
      setActiveButton(this);
      setBubbleShooterDifficulty('easy');
    });
  }

  if (mediumButton) {
    mediumButton.addEventListener('click', function() {
      setActiveButton(this);
      setBubbleShooterDifficulty('medium');
    });
  }

  if (hardButton) {
    hardButton.addEventListener('click', function() {
      setActiveButton(this);
      setBubbleShooterDifficulty('hard');
    });
  }
}

// Set difficulty
function setBubbleShooterDifficulty(difficulty) {
  bsDifficulty = difficulty;
  restartBubbleShooterGame();
}

// Start the Bubble Shooter game
function startBubbleShooterGame() {
  if (!bsGameRunning && !bsGameOver) {
    bsGameRunning = true;

    // Create initial grid
    createBubbleGrid();

    // Create initial bubbles
    if (!bsShooter.currentBubble) {
      bsShooter.currentBubble = createRandomBubble();
    }

    if (!bsShooter.nextBubble) {
      bsShooter.nextBubble = createRandomBubble();
    }

    // Start game loop using requestAnimationFrame for better performance
    bsLastFrameTime = performance.now();
    requestAnimationFrame(bubbleShooterGameLoop);
  } else if (bsGameOver) {
    restartBubbleShooterGame();
  }
}

// Pause the Bubble Shooter game
function pauseBubbleShooterGame() {
  bsGameRunning = false;
  // No need to clear interval as we're using requestAnimationFrame
}

// Restart the Bubble Shooter game
function restartBubbleShooterGame() {
  pauseBubbleShooterGame();
  resetBubbleShooterGame();
  startBubbleShooterGame();
}

// Reset the game state
function resetBubbleShooterGame() {
  bsGrid = [];
  bsShooter.currentBubble = null;
  bsShooter.nextBubble = null;
  bsShooter.angle = -Math.PI / 2;
  bsGameRunning = false;
  bsGameOver = false;
  bsScore = 0;
  bsLevel = 1;

  // Set bubbles left based on difficulty
  switch (bsDifficulty) {
    case 'easy':
      bsBubblesLeft = 50;
      break;
    case 'medium':
      bsBubblesLeft = 40;
      break;
    case 'hard':
      bsBubblesLeft = 30;
      break;
  }

  // Update displays
  updateBubbleShooterScoreDisplay();
  updateBubbleShooterLevelDisplay();
  updateBubbleShooterBubblesDisplay();
}

// Create the bubble grid
function createBubbleGrid() {
  bsGrid = [];

  // Determine number of rows based on difficulty
  let rows;
  switch (bsDifficulty) {
    case 'easy':
      rows = 4;
      break;
    case 'medium':
      rows = 5;
      break;
    case 'hard':
      rows = 6;
      break;
  }

  // Create grid
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < bsGridCols; col++) {
      // Offset every other row
      const x = bsGridOffsetX + col * bsBubbleRadius * 2 + (row % 2 === 1 ? bsBubbleRadius : 0);
      const y = bsGridOffsetY + row * bsBubbleRadius * 1.8;

      // Random color
      const colorIndex = Math.floor(Math.random() * bsColors.length);

      bsGrid.push({
        x: x,
        y: y,
        color: bsColors[colorIndex],
        colorName: bsColorNames[colorIndex],
        row: row,
        col: col
      });
    }
  }
}

// Create a random bubble
function createRandomBubble() {
  const colorIndex = Math.floor(Math.random() * bsColors.length);
  return {
    color: bsColors[colorIndex],
    colorName: bsColorNames[colorIndex]
  };
}

// Main game loop with frame limiting
let bsLastFrameTime = 0;
const bsFrameInterval = 1000 / 30; // Limit to 30 FPS for better performance

function bubbleShooterGameLoop(timestamp) {
  if (!bsGameRunning) return;

  // Calculate time since last frame
  if (!timestamp) timestamp = performance.now();
  const elapsed = timestamp - bsLastFrameTime;

  // Only render if enough time has passed (frame limiting)
  if (elapsed > bsFrameInterval) {
    bsLastFrameTime = timestamp - (elapsed % bsFrameInterval);

    // Clear canvas
    bsCtx.clearRect(0, 0, bsCanvas.width, bsCanvas.height);

    // Draw background
    drawBackground();

    // Draw grid
    drawBubbleGrid();

    // Update fired bubble if exists
    if (bsFiredBubble) {
      updateFiredBubble();
    }

    // Draw fired bubble if exists
    if (bsFiredBubble) {
      drawBubble(bsFiredBubble.x, bsFiredBubble.y, bsFiredBubble.color);
    }

    // Update and draw pop effects
    updateAndDrawPopEffects();

    // Draw shooter
    drawShooter();

    // Draw current bubble
    if (!bsFiredBubble) {
      drawCurrentBubble();
    }

    // Draw next bubble
    drawNextBubble();
  }

  // Request next frame using requestAnimationFrame instead of setInterval
  if (bsGameRunning) {
    requestAnimationFrame(bubbleShooterGameLoop);
  }
}

// Update and draw pop effects - optimized to limit number of effects
function updateAndDrawPopEffects() {
  // Limit the number of active effects to improve performance
  if (bsPopEffects.length > 20) {
    bsPopEffects.splice(0, bsPopEffects.length - 20);
  }

  // Update pop effects
  for (let i = bsPopEffects.length - 1; i >= 0; i--) {
    const effect = bsPopEffects[i];

    // Update effect
    effect.radius += 1;
    effect.opacity -= 0.05;

    // Remove effect if it's faded out
    if (effect.opacity <= 0) {
      bsPopEffects.splice(i, 1);
      continue;
    }

    // Draw effect
    bsCtx.fillStyle = `rgba(255, 255, 255, ${effect.opacity})`;
    bsCtx.beginPath();
    bsCtx.arc(effect.x, effect.y, effect.radius, 0, Math.PI * 2);
    bsCtx.fill();
  }
}

// Draw background - cached for better performance
let bsBackgroundCache;

function drawBackground() {
  // Create cached background if it doesn't exist
  if (!bsBackgroundCache) {
    bsBackgroundCache = document.createElement('canvas');
    bsBackgroundCache.width = bsCanvas.width;
    bsBackgroundCache.height = bsCanvas.height;
    const bgCtx = bsBackgroundCache.getContext('2d');

    // Draw sky gradient
    const gradient = bgCtx.createLinearGradient(0, 0, 0, bsCanvas.height);
    gradient.addColorStop(0, '#87CEEB');
    gradient.addColorStop(1, '#E0F7FA');
    bgCtx.fillStyle = gradient;
    bgCtx.fillRect(0, 0, bsCanvas.width, bsCanvas.height);

    // Draw shooter platform
    bgCtx.fillStyle = '#3A6EA5';
    bgCtx.fillRect(bsCanvas.width / 2 - 30, bsCanvas.height - 50, 60, 20);
    bgCtx.fillStyle = '#0A246A';
    bgCtx.fillRect(bsCanvas.width / 2 - 25, bsCanvas.height - 30, 50, 30);
  }

  // Draw the cached background
  bsCtx.drawImage(bsBackgroundCache, 0, 0);
}

// Draw bubble grid
function drawBubbleGrid() {
  bsGrid.forEach(bubble => {
    drawBubble(bubble.x, bubble.y, bubble.color);
  });
}

// Draw shooter
function drawShooter() {
  // Draw shooter base
  bsCtx.fillStyle = '#0A246A';
  bsCtx.beginPath();
  bsCtx.arc(bsShooter.x, bsShooter.y, 15, 0, Math.PI * 2);
  bsCtx.fill();

  // Draw shooter barrel
  bsCtx.strokeStyle = '#0A246A';
  bsCtx.lineWidth = 5;
  bsCtx.beginPath();
  bsCtx.moveTo(bsShooter.x, bsShooter.y);
  bsCtx.lineTo(
    bsShooter.x + Math.cos(bsShooter.angle) * 40,
    bsShooter.y + Math.sin(bsShooter.angle) * 40
  );
  bsCtx.stroke();
}

// Draw current bubble
function drawCurrentBubble() {
  if (bsShooter.currentBubble) {
    const x = bsShooter.x + Math.cos(bsShooter.angle) * 40;
    const y = bsShooter.y + Math.sin(bsShooter.angle) * 40;
    drawBubble(x, y, bsShooter.currentBubble.color);
  }
}

// Draw next bubble
function drawNextBubble() {
  if (bsShooter.nextBubble) {
    drawBubble(bsCanvas.width - 50, bsCanvas.height - 40, bsShooter.nextBubble.color);

    // Draw "Next" text
    bsCtx.fillStyle = '#0A246A';
    bsCtx.font = '12px Arial';
    bsCtx.textAlign = 'center';
    bsCtx.fillText('Next', bsCanvas.width - 50, bsCanvas.height - 60);
  }
}

// Draw a bubble
function drawBubble(x, y, color) {
  // Draw bubble
  bsCtx.fillStyle = color;
  bsCtx.beginPath();
  bsCtx.arc(x, y, bsBubbleRadius, 0, Math.PI * 2);
  bsCtx.fill();

  // Draw bubble highlight
  bsCtx.fillStyle = 'rgba(255, 255, 255, 0.3)';
  bsCtx.beginPath();
  bsCtx.arc(x - bsBubbleRadius / 3, y - bsBubbleRadius / 3, bsBubbleRadius / 3, 0, Math.PI * 2);
  bsCtx.fill();

  // Draw bubble outline
  bsCtx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
  bsCtx.lineWidth = 1;
  bsCtx.beginPath();
  bsCtx.arc(x, y, bsBubbleRadius, 0, Math.PI * 2);
  bsCtx.stroke();
}

// Handle mouse move
function handleMouseMove(event) {
  if (!bsGameRunning) return;

  // Get mouse position relative to canvas
  const rect = bsCanvas.getBoundingClientRect();
  const mouseX = event.clientX - rect.left;
  const mouseY = event.clientY - rect.top;

  // Calculate angle
  const dx = mouseX - bsShooter.x;
  const dy = mouseY - bsShooter.y;
  let angle = Math.atan2(dy, dx);

  // Limit angle to upper half
  if (angle > 0) {
    angle = 0;
  } else if (angle < -Math.PI) {
    angle = -Math.PI;
  }

  bsShooter.angle = angle;
}

// Handle mouse click
function handleMouseClick(event) {
  if (!bsGameRunning) return;

  // Shoot bubble
  shootBubble();
}

// Handle key down
function handleKeyDown(event) {
  if (!bsGameRunning) return;

  if (event.code === 'Space') {
    // If Shift key is also pressed, swap bubbles
    if (event.shiftKey) {
      swapBubbles();
    } else {
      // Otherwise, shoot bubble
      shootBubble();
    }
    event.preventDefault();
  }
}

// Swap current and next bubbles
function swapBubbles() {
  const temp = bsShooter.currentBubble;
  bsShooter.currentBubble = bsShooter.nextBubble;
  bsShooter.nextBubble = temp;
}

// Shoot bubble
function shootBubble() {
  // Don't shoot if a bubble is already in flight or no current bubble
  if (bsFiredBubble || !bsShooter.currentBubble) return;

  // Create fired bubble
  bsFiredBubble = {
    x: bsShooter.x + Math.cos(bsShooter.angle) * 40,
    y: bsShooter.y + Math.sin(bsShooter.angle) * 40,
    color: bsShooter.currentBubble.color,
    colorName: bsShooter.currentBubble.colorName,
    velocityX: Math.cos(bsShooter.angle) * 10,
    velocityY: Math.sin(bsShooter.angle) * 10
  };

  // Reduce bubbles left
  bsBubblesLeft--;
  updateBubbleShooterBubblesDisplay();

  // Check if out of bubbles
  if (bsBubblesLeft <= 0) {
    handleBubbleShooterGameOver();
  }
}

// Update fired bubble position and check for collisions
function updateFiredBubble() {
  if (!bsFiredBubble) return;

  // Update position
  bsFiredBubble.x += bsFiredBubble.velocityX;
  bsFiredBubble.y += bsFiredBubble.velocityY;

  // Check for wall collisions
  if (bsFiredBubble.x < bsBubbleRadius || bsFiredBubble.x > bsCanvas.width - bsBubbleRadius) {
    // Reverse the x velocity for a proper bounce
    bsFiredBubble.velocityX = -bsFiredBubble.velocityX;

    // Create a small bounce effect
    createBounceEffect(bsFiredBubble.x, bsFiredBubble.y);

    // Adjust position to prevent sticking to wall
    if (bsFiredBubble.x < bsBubbleRadius) {
      bsFiredBubble.x = bsBubbleRadius;
    } else {
      bsFiredBubble.x = bsCanvas.width - bsBubbleRadius;
    }
  }

  // Check for ceiling collision
  if (bsFiredBubble.y < bsBubbleRadius) {
    // Snap to grid
    snapBubbleToGrid();
    return;
  }

  // Check for bubble collisions
  for (const bubble of bsGrid) {
    const dx = bubble.x - bsFiredBubble.x;
    const dy = bubble.y - bsFiredBubble.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance < bsBubbleRadius * 2) {
      // Collision detected, snap to grid
      snapBubbleToGrid();
      return;
    }
  }

  // Check if bubble went off the bottom
  if (bsFiredBubble.y > bsCanvas.height) {
    // Bounce off the bottom edge
    bsFiredBubble.velocityY = -bsFiredBubble.velocityY;
    bsFiredBubble.y = bsCanvas.height - bsBubbleRadius;

    // Create a bounce effect
    createBounceEffect(bsFiredBubble.x, bsFiredBubble.y);
  }
}

// Snap bubble to grid
function snapBubbleToGrid() {
  // Find the closest grid position
  let closestRow = 0;
  let closestCol = 0;
  let closestDistance = Infinity;

  // Calculate grid positions
  for (let row = 0; row < bsGridRows; row++) {
    for (let col = 0; col < bsGridCols; col++) {
      // Calculate position
      const x = bsGridOffsetX + col * bsBubbleRadius * 2 + (row % 2 === 1 ? bsBubbleRadius : 0);
      const y = bsGridOffsetY + row * bsBubbleRadius * 1.8;

      // Calculate distance
      const dx = x - bsFiredBubble.x;
      const dy = y - bsFiredBubble.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // Check if this position is empty and closer
      let positionOccupied = false;
      for (const bubble of bsGrid) {
        if (bubble.x === x && bubble.y === y) {
          positionOccupied = true;
          break;
        }
      }

      if (!positionOccupied && distance < closestDistance) {
        closestDistance = distance;
        closestRow = row;
        closestCol = col;
      }
    }
  }

  // Calculate the position
  const x = bsGridOffsetX + closestCol * bsBubbleRadius * 2 + (closestRow % 2 === 1 ? bsBubbleRadius : 0);
  const y = bsGridOffsetY + closestRow * bsBubbleRadius * 1.8;

  // Add to grid
  bsGrid.push({
    x: x,
    y: y,
    color: bsFiredBubble.color,
    colorName: bsFiredBubble.colorName,
    row: closestRow,
    col: closestCol
  });

  // Check for matches
  checkForMatches();

  // Reset fired bubble
  bsFiredBubble = null;

  // Get next bubble
  bsShooter.currentBubble = bsShooter.nextBubble;
  bsShooter.nextBubble = createRandomBubble();
}

// Check for matches
function checkForMatches() {
  // Get the last added bubble (it's the last one in the grid array)
  const lastBubble = bsGrid[bsGrid.length - 1];

  // Find all connected bubbles of the same color
  const matchedBubbles = findMatchingBubbles(lastBubble);

  // If we have 3 or more matching bubbles, remove them
  if (matchedBubbles.length >= 3) {
    // Remove matched bubbles from grid
    removeMatchedBubbles(matchedBubbles);

    // Add points based on number of bubbles removed
    const points = matchedBubbles.length * 50;
    bsScore += points;

    // Show floating score text
    showFloatingText(`+${points}`, lastBubble.x, lastBubble.y);

    // Check for floating bubbles and remove them
    removeFloatingBubbles();

    // Update score display
    updateBubbleShooterScoreDisplay();
  } else {
    // No match, just add a small score for placing a bubble
    bsScore += 10;
    updateBubbleShooterScoreDisplay();
  }
}

// Find all connected bubbles of the same color
function findMatchingBubbles(startBubble) {
  const matchedBubbles = [];
  const checkedBubbles = new Set();

  // Helper function to recursively find matching bubbles
  function findMatches(bubble) {
    // Create a unique ID for this bubble to avoid checking it multiple times
    const bubbleId = `${bubble.row},${bubble.col}`;

    // If we've already checked this bubble, return
    if (checkedBubbles.has(bubbleId)) return;

    // Mark this bubble as checked
    checkedBubbles.add(bubbleId);

    // If the color matches, add it to the matched bubbles
    if (bubble.colorName === startBubble.colorName) {
      matchedBubbles.push(bubble);

      // Check all adjacent bubbles
      const adjacentBubbles = getAdjacentBubbles(bubble);
      adjacentBubbles.forEach(findMatches);
    }
  }

  // Start the recursive search
  findMatches(startBubble);

  return matchedBubbles;
}

// Get all adjacent bubbles
function getAdjacentBubbles(bubble) {
  const adjacentBubbles = [];
  const row = bubble.row;
  const col = bubble.col;
  const isOddRow = row % 2 === 1;

  // Define adjacent positions based on whether the row is odd or even
  const adjacentPositions = [
    { row: row - 1, col: isOddRow ? col : col - 1 },     // Top left
    { row: row - 1, col: isOddRow ? col + 1 : col },     // Top right
    { row: row, col: col - 1 },                          // Left
    { row: row, col: col + 1 },                          // Right
    { row: row + 1, col: isOddRow ? col : col - 1 },     // Bottom left
    { row: row + 1, col: isOddRow ? col + 1 : col }      // Bottom right
  ];

  // Check each adjacent position
  adjacentPositions.forEach(pos => {
    // Find a bubble at this position
    const adjacentBubble = bsGrid.find(b => b.row === pos.row && b.col === pos.col);
    if (adjacentBubble) {
      adjacentBubbles.push(adjacentBubble);
    }
  });

  return adjacentBubbles;
}

// Remove matched bubbles from the grid
function removeMatchedBubbles(bubbles) {
  if (bubbles.length === 0) return;

  // Create a set of bubble IDs to remove
  const bubbleIdsToRemove = new Set();

  // Create pop effects and add bubbles to remove set
  bubbles.forEach(bubble => {
    // Add to remove set
    bubbleIdsToRemove.add(`${bubble.row},${bubble.col}`);

    // Create pop effect
    createPopEffect(bubble);

    // Play pop sound (if we had sound)
    // playPopSound();
  });

  // Filter out the matched bubbles
  bsGrid = bsGrid.filter(bubble => {
    return !bubbleIdsToRemove.has(`${bubble.row},${bubble.col}`);
  });
}

// Create a pop effect at the bubble's position
function createPopEffect(bubble) {
  bsPopEffects.push({
    x: bubble.x,
    y: bubble.y,
    radius: bsBubbleRadius,
    opacity: 1.0,
    color: bubble.color
  });
}

// Create a bounce effect at the specified position
function createBounceEffect(x, y) {
  // Add a small visual effect for the bounce
  bsPopEffects.push({
    x: x,
    y: y,
    radius: bsBubbleRadius / 2,
    opacity: 0.7,
    color: '#FFFFFF'
  });
}

// Check for and remove floating bubbles
function removeFloatingBubbles() {
  // First, mark all bubbles as not connected
  bsGrid.forEach(bubble => {
    bubble.connected = false;
  });

  // Mark all bubbles connected to the ceiling as connected
  const ceilingBubbles = bsGrid.filter(bubble => bubble.row === 0);
  ceilingBubbles.forEach(markConnected);

  // Remove all bubbles that are not connected
  const floatingBubbles = bsGrid.filter(bubble => !bubble.connected);

  // Add points for floating bubbles
  const floatingPoints = floatingBubbles.length * 30;
  if (floatingBubbles.length > 0) {
    bsScore += floatingPoints;

    // Show floating score text if there are floating bubbles
    if (floatingBubbles.length > 0) {
      const firstBubble = floatingBubbles[0];
      showFloatingText(`+${floatingPoints}`, firstBubble.x, firstBubble.y);
    }
  }

  // Remove floating bubbles
  removeMatchedBubbles(floatingBubbles);

  // Check if all bubbles are cleared
  checkForWin();
}

// Mark a bubble and all connected bubbles as connected
function markConnected(bubble) {
  if (bubble.connected) return;

  bubble.connected = true;

  // Mark all adjacent bubbles as connected
  const adjacentBubbles = getAdjacentBubbles(bubble);
  adjacentBubbles.forEach(markConnected);
}

// Show floating score text
function showFloatingText(text, x, y) {
  bsCtx.fillStyle = '#FFFFFF';
  bsCtx.font = 'bold 24px Arial';
  bsCtx.textAlign = 'center';
  bsCtx.textBaseline = 'middle';
  bsCtx.fillText(text, x, y);

  // In a more advanced implementation, you would animate this text
  // For now, we'll just show it briefly
}

// Update displays
function updateBubbleShooterScoreDisplay() {
  const scoreElement = document.getElementById('bubbleshooter-score');
  if (scoreElement) {
    scoreElement.textContent = bsScore;
  }
}

function updateBubbleShooterLevelDisplay() {
  const levelElement = document.getElementById('bubbleshooter-level');
  if (levelElement) {
    levelElement.textContent = bsLevel;
  }
}

function updateBubbleShooterBubblesDisplay() {
  const bubblesElement = document.getElementById('bubbleshooter-bubbles');
  if (bubblesElement) {
    bubblesElement.textContent = bsBubblesLeft;
  }
}

// Handle game over
function handleBubbleShooterGameOver() {
  bsGameRunning = false;
  bsGameOver = true;
  clearInterval(bsGameLoop);

  // Draw game over message
  bsCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
  bsCtx.fillRect(0, 0, bsCanvas.width, bsCanvas.height);

  bsCtx.fillStyle = '#FFFFFF';
  bsCtx.font = 'bold 36px Arial';
  bsCtx.textAlign = 'center';
  bsCtx.textBaseline = 'middle';
  bsCtx.fillText('Game Over', bsCanvas.width / 2, bsCanvas.height / 2 - 40);

  bsCtx.font = '24px Arial';
  bsCtx.fillText(`Final Score: ${bsScore}`, bsCanvas.width / 2, bsCanvas.height / 2 + 10);

  // Save high score
  saveBubbleShooterHighScore();
}

// Save high score
function saveBubbleShooterHighScore() {
  const highScoreKey = `bubbleshooter-high-score-${bsDifficulty}`;
  const highScore = localStorage.getItem(highScoreKey) || 0;

  if (bsScore > parseInt(highScore)) {
    localStorage.setItem(highScoreKey, bsScore.toString());
    return true;
  }

  return false;
}

// Check if all bubbles are cleared
function checkForWin() {
  // If there are no bubbles left in the grid, player wins
  if (bsGrid.length === 0) {
    showWinScreen();
  }
}

// Show win screen
function showWinScreen() {
  bsGameRunning = false;
  bsGameOver = true;
  clearInterval(bsGameLoop);

  // Draw win message
  bsCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
  bsCtx.fillRect(0, 0, bsCanvas.width, bsCanvas.height);

  bsCtx.fillStyle = '#FFFFFF';
  bsCtx.font = 'bold 36px Arial';
  bsCtx.textAlign = 'center';
  bsCtx.textBaseline = 'middle';
  bsCtx.fillText('You Win!', bsCanvas.width / 2, bsCanvas.height / 2 - 40);

  bsCtx.font = '24px Arial';
  bsCtx.fillText(`Final Score: ${bsScore}`, bsCanvas.width / 2, bsCanvas.height / 2 + 10);

  // Add bonus points for remaining bubbles
  const bonusPoints = bsBubblesLeft * 20;
  if (bonusPoints > 0) {
    bsScore += bonusPoints;
    bsCtx.fillText(`Bonus: +${bonusPoints}`, bsCanvas.width / 2, bsCanvas.height / 2 + 50);
    bsCtx.fillText(`Total Score: ${bsScore}`, bsCanvas.width / 2, bsCanvas.height / 2 + 90);

    // Update score display
    updateBubbleShooterScoreDisplay();
  }

  // Save high score
  const isNewHighScore = saveBubbleShooterHighScore();
  if (isNewHighScore) {
    bsCtx.fillStyle = '#FFC107';
    bsCtx.fillText('New High Score!', bsCanvas.width / 2, bsCanvas.height / 2 + 130);
  }
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the game
  initBubbleShooterGame();
});
