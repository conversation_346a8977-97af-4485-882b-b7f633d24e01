// BlockDrop Game functionality
let bdCanvas, bdCtx;
let blocks = [];
let currentBlock = {};
let nextBlock = {};
let gameBoard = [];
let bdDirection = 'down';
let bdGameSpeed = 500; // milliseconds
let bdGameInterval;
let bdScore = 0;
let bdGameRunning = false;
let bdGameOver = false;
let bdGridSize = 20;
let bdCanvasSize = 400;
let bdBoardWidth = bdCanvasSize / bdGridSize;
let bdBoardHeight = bdCanvasSize / bdGridSize;
let bdColors = ['#3A6EA5', '#CC0000', '#008000', '#800080', '#FF7F00', '#0A246A'];

// Initialize the BlockDrop game
function initBlockDropGame() {
  bdCanvas = document.getElementById('blockdrop-canvas');
  bdCtx = bdCanvas.getContext('2d');

  // Reset game state
  resetBlockDropGame();

  // Add keyboard event listener
  document.addEventListener('keydown', handleBlockDropKeyPress);
}

// Start the BlockDrop game
function startBlockDropGame() {
  if (!bdGameRunning && !bdGameOver) {
    bdGameRunning = true;
    bdGameInterval = setInterval(blockDropGameLoop, bdGameSpeed);
  } else if (bdGameOver) {
    restartBlockDropGame();
  }
}

// Pause the BlockDrop game
function pauseBlockDropGame() {
  bdGameRunning = false;
  clearInterval(bdGameInterval);
}

// Restart the BlockDrop game
function restartBlockDropGame() {
  pauseBlockDropGame();
  resetBlockDropGame();
  startBlockDropGame();
}

// Reset the game state
function resetBlockDropGame() {
  // Initialize empty game board
  gameBoard = Array(bdBoardHeight).fill().map(() => Array(bdBoardWidth).fill(null));
  
  // Create initial block
  createNewBlock();
  
  // Create next block
  createNextBlock();
  
  // Initialize score
  bdScore = 0;
  updateBlockDropScore(0);

  // Reset game state
  bdGameRunning = false;
  bdGameOver = false;

  // Draw initial state
  drawBlockDropGame();
}

// Create a new block
function createNewBlock() {
  // If there's a next block, use it
  if (nextBlock.shape) {
    currentBlock = nextBlock;
  } else {
    // Create a random block
    const blockType = Math.floor(Math.random() * 4);
    const colorIndex = Math.floor(Math.random() * bdColors.length);
    
    currentBlock = {
      x: Math.floor(bdBoardWidth / 2) - 1,
      y: 0,
      shape: getBlockShape(blockType),
      color: bdColors[colorIndex]
    };
  }
  
  // Check if the new block can be placed
  if (!canPlaceBlock(currentBlock)) {
    handleBlockDropGameOver();
  }
}

// Create the next block
function createNextBlock() {
  const blockType = Math.floor(Math.random() * 4);
  const colorIndex = Math.floor(Math.random() * bdColors.length);
  
  nextBlock = {
    x: Math.floor(bdBoardWidth / 2) - 1,
    y: 0,
    shape: getBlockShape(blockType),
    color: bdColors[colorIndex]
  };
}

// Get block shape based on type
function getBlockShape(type) {
  switch (type) {
    case 0: // Line shape
      return [
        [1, 1, 1]
      ];
    case 1: // L shape
      return [
        [1, 0],
        [1, 0],
        [1, 1]
      ];
    case 2: // Square shape
      return [
        [1, 1],
        [1, 1]
      ];
    case 3: // Z shape
      return [
        [1, 1, 0],
        [0, 1, 1]
      ];
    default:
      return [
        [1, 1],
        [1, 1]
      ];
  }
}

// Main game loop
function blockDropGameLoop() {
  if (!bdGameRunning) return;

  // Move block down
  if (canMoveBlock(currentBlock, 0, 1)) {
    currentBlock.y++;
  } else {
    // Block has landed
    placeBlockOnBoard();
    
    // Check for completed rows
    const clearedRows = checkForCompletedRows();
    if (clearedRows > 0) {
      // Update score
      updateBlockDropScore(bdScore + (clearedRows * 100));
      
      // Increase game speed slightly
      if (bdGameSpeed > 100) {
        bdGameSpeed -= 10;
        clearInterval(bdGameInterval);
        bdGameInterval = setInterval(blockDropGameLoop, bdGameSpeed);
      }
    }
    
    // Create new block
    createNewBlock();
    createNextBlock();
  }

  // Draw everything
  drawBlockDropGame();
}

// Check if block can be moved
function canMoveBlock(block, deltaX, deltaY) {
  for (let y = 0; y < block.shape.length; y++) {
    for (let x = 0; x < block.shape[y].length; x++) {
      if (block.shape[y][x]) {
        const newX = block.x + x + deltaX;
        const newY = block.y + y + deltaY;
        
        // Check boundaries
        if (newX < 0 || newX >= bdBoardWidth || newY < 0 || newY >= bdBoardHeight) {
          return false;
        }
        
        // Check collision with placed blocks
        if (newY >= 0 && gameBoard[newY][newX] !== null) {
          return false;
        }
      }
    }
  }
  
  return true;
}

// Check if block can be placed
function canPlaceBlock(block) {
  return canMoveBlock(block, 0, 0);
}

// Place block on the game board
function placeBlockOnBoard() {
  for (let y = 0; y < currentBlock.shape.length; y++) {
    for (let x = 0; x < currentBlock.shape[y].length; x++) {
      if (currentBlock.shape[y][x]) {
        const boardX = currentBlock.x + x;
        const boardY = currentBlock.y + y;
        
        if (boardY >= 0 && boardY < bdBoardHeight && boardX >= 0 && boardX < bdBoardWidth) {
          gameBoard[boardY][boardX] = currentBlock.color;
        }
      }
    }
  }
}

// Check for completed rows
function checkForCompletedRows() {
  let completedRows = 0;
  
  for (let y = bdBoardHeight - 1; y >= 0; y--) {
    let rowComplete = true;
    
    for (let x = 0; x < bdBoardWidth; x++) {
      if (gameBoard[y][x] === null) {
        rowComplete = false;
        break;
      }
    }
    
    if (rowComplete) {
      // Remove the row
      for (let y2 = y; y2 > 0; y2--) {
        for (let x = 0; x < bdBoardWidth; x++) {
          gameBoard[y2][x] = gameBoard[y2 - 1][x];
        }
      }
      
      // Clear the top row
      for (let x = 0; x < bdBoardWidth; x++) {
        gameBoard[0][x] = null;
      }
      
      completedRows++;
      y++; // Check the same row again
    }
  }
  
  return completedRows;
}

// Handle game over
function handleBlockDropGameOver() {
  bdGameRunning = false;
  bdGameOver = true;
  clearInterval(bdGameInterval);
  
  // Check if it's a high score
  const isHighScore = saveBlockDropHighScore(bdScore);
  
  // Draw game over screen
  drawBlockDropGameOver(isHighScore);
}

// Draw everything
function drawBlockDropGame() {
  // Clear canvas
  bdCtx.fillStyle = '#F1EFE2';
  bdCtx.fillRect(0, 0, bdCanvas.width, bdCanvas.height);
  
  // Draw grid
  drawBlockDropGrid();
  
  // Draw placed blocks
  drawPlacedBlocks();
  
  // Draw current block
  drawCurrentBlock();
}

// Draw grid
function drawBlockDropGrid() {
  bdCtx.strokeStyle = '#E5E5E5';
  bdCtx.lineWidth = 0.5;
  
  // Draw vertical lines
  for (let x = 0; x <= bdCanvasSize; x += bdGridSize) {
    bdCtx.beginPath();
    bdCtx.moveTo(x, 0);
    bdCtx.lineTo(x, bdCanvasSize);
    bdCtx.stroke();
  }
  
  // Draw horizontal lines
  for (let y = 0; y <= bdCanvasSize; y += bdGridSize) {
    bdCtx.beginPath();
    bdCtx.moveTo(0, y);
    bdCtx.lineTo(bdCanvasSize, y);
    bdCtx.stroke();
  }
}

// Draw placed blocks
function drawPlacedBlocks() {
  for (let y = 0; y < bdBoardHeight; y++) {
    for (let x = 0; x < bdBoardWidth; x++) {
      if (gameBoard[y][x] !== null) {
        bdCtx.fillStyle = gameBoard[y][x];
        bdCtx.fillRect(
          x * bdGridSize,
          y * bdGridSize,
          bdGridSize - 1,
          bdGridSize - 1
        );
      }
    }
  }
}

// Draw current block
function drawCurrentBlock() {
  bdCtx.fillStyle = currentBlock.color;
  
  for (let y = 0; y < currentBlock.shape.length; y++) {
    for (let x = 0; x < currentBlock.shape[y].length; x++) {
      if (currentBlock.shape[y][x]) {
        bdCtx.fillRect(
          (currentBlock.x + x) * bdGridSize,
          (currentBlock.y + y) * bdGridSize,
          bdGridSize - 1,
          bdGridSize - 1
        );
      }
    }
  }
}

// Draw game over screen
function drawBlockDropGameOver(isHighScore) {
  // Semi-transparent overlay
  bdCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
  bdCtx.fillRect(0, 0, bdCanvas.width, bdCanvas.height);
  
  // Game over text
  bdCtx.fillStyle = '#FFFFFF';
  bdCtx.font = 'bold 30px Tahoma';
  bdCtx.textAlign = 'center';
  bdCtx.fillText('Game Over', bdCanvas.width / 2, bdCanvas.height / 2 - 30);
  
  // Score text
  bdCtx.font = '20px Tahoma';
  bdCtx.fillText(`Score: ${bdScore}`, bdCanvas.width / 2, bdCanvas.height / 2 + 10);
  
  // High score text
  if (isHighScore) {
    bdCtx.fillStyle = '#FFFF00';
    bdCtx.fillText('New High Score!', bdCanvas.width / 2, bdCanvas.height / 2 + 40);
  }
}

// Handle key press
function handleBlockDropKeyPress(event) {
  // Ignore if game is not running
  if (!bdGameRunning) return;
  
  // Get key code
  const key = event.key;
  
  switch (key) {
    case 'ArrowLeft':
      if (canMoveBlock(currentBlock, -1, 0)) {
        currentBlock.x--;
        drawBlockDropGame();
      }
      break;
    case 'ArrowRight':
      if (canMoveBlock(currentBlock, 1, 0)) {
        currentBlock.x++;
        drawBlockDropGame();
      }
      break;
    case 'ArrowDown':
      if (canMoveBlock(currentBlock, 0, 1)) {
        currentBlock.y++;
        drawBlockDropGame();
      }
      break;
    case 'ArrowUp':
      rotateBlock();
      drawBlockDropGame();
      break;
  }
  
  // Prevent default behavior for arrow keys (scrolling)
  if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
    event.preventDefault();
  }
}

// Rotate block
function rotateBlock() {
  const originalShape = currentBlock.shape;
  const rows = originalShape.length;
  const cols = originalShape[0].length;
  
  // Create a new rotated shape
  const rotatedShape = Array(cols).fill().map(() => Array(rows).fill(0));
  
  for (let y = 0; y < rows; y++) {
    for (let x = 0; x < cols; x++) {
      rotatedShape[x][rows - 1 - y] = originalShape[y][x];
    }
  }
  
  // Save the original shape
  const originalBlockShape = currentBlock.shape;
  
  // Try the rotation
  currentBlock.shape = rotatedShape;
  
  // If the rotation is not possible, revert back
  if (!canPlaceBlock(currentBlock)) {
    currentBlock.shape = originalBlockShape;
  }
}

// Update score
function updateBlockDropScore(newScore) {
  bdScore = newScore;
  const scoreElement = document.getElementById('blockdrop-current-score');
  if (scoreElement) {
    scoreElement.textContent = bdScore;
  }
}

// Save high score
function saveBlockDropHighScore(score) {
  const highScore = localStorage.getItem('blockdrop-high-score') || 0;
  
  if (score > highScore) {
    localStorage.setItem('blockdrop-high-score', score);
    updateBlockDropHighScoreDisplay(score);
    return true;
  }
  
  return false;
}

// Update high score display
function updateBlockDropHighScoreDisplay(score) {
  const highScoreElement = document.getElementById('blockdrop-high-score');
  if (highScoreElement) {
    highScoreElement.textContent = score;
  }
}

// Load high score
function loadBlockDropHighScore() {
  const highScore = localStorage.getItem('blockdrop-high-score') || 0;
  updateBlockDropHighScoreDisplay(highScore);
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the game
  initBlockDropGame();
  
  // Load high score
  loadBlockDropHighScore();
});
