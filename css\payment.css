/* Payment Page Styles */
.payment-page {
  font-family: '<PERSON>hom<PERSON>', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 30px;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.payment-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 25px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.payment-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.payment-content {
  display: flex;
  gap: 20px;
  flex: 1;
  margin-bottom: 30px;
}

/* Payment Form Section */
.payment-form-section {
  flex: 3;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.payment-form-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ACA899;
}

.payment-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-group.half {
  flex: 1;
}

.form-group label {
  font-size: 12px;
  color: #333333;
  font-weight: bold;
}

.form-input {
  padding: 8px 10px;
  border: 1px solid #7F9DB9;
  border-radius: 3px;
  font-size: 12px;
  color: #333333;
  background-color: #FFFFFF;
}

.form-input:focus {
  border-color: #0A246A;
  outline: none;
  box-shadow: 0 0 3px rgba(10, 36, 106, 0.3);
}

/* Payment Summary Section */
.payment-summary-section {
  flex: 2;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.payment-summary-section h2 {
  color: #0A246A;
  font-size: 18px;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ACA899;
}

.payment-summary {
  flex: 1;
  margin-bottom: 20px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 12px;
  padding: 5px 0;
}

.summary-label {
  color: #333333;
  font-weight: normal;
}

.summary-value {
  color: #0A246A;
  font-weight: bold;
}

.summary-row.total-row {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #ACA899;
  font-weight: bold;
  font-size: 14px;
}

.payment-actions {
  display: flex;
  gap: 10px;
  justify-content: space-between;
}

.process-payment-btn {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  color: #0A246A;
  border: 1px solid #7F9DB9;
  padding: 8px 15px;
  cursor: pointer;
  font-size: 12px;
  border-radius: 3px;
  transition: background 0.2s ease;
  flex: 2;
}

.process-payment-btn:hover {
  background: linear-gradient(to bottom, #E5F1FB, #D9E9F7);
  border-color: #0A246A;
}

.cancel-payment-btn {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  color: #333333;
  border: 1px solid #ACA899;
  padding: 8px 15px;
  cursor: pointer;
  font-size: 12px;
  border-radius: 3px;
  transition: background 0.2s ease;
  flex: 1;
}

.cancel-payment-btn:hover {
  background: linear-gradient(to bottom, #F5F5F5, #E5E5E5);
  border-color: #7F9DB9;
}

/* Payment Confirmation Section */
.payment-confirmation-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 500px; /* Ensure it has enough height to be visible */
  width: 100%;
}

.payment-confirmation {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.confirmation-icon {
  font-size: 48px;
  color: #008800;
  margin-bottom: 20px;
}

.payment-confirmation h2 {
  color: #0A246A;
  font-size: 22px;
  margin: 0 0 20px 0;
}

.confirmation-message {
  font-size: 14px;
  color: #333333;
  margin-bottom: 20px;
  line-height: 1.5;
  background-color: #F1EFE2;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #ACA899;
}

.confirmation-details {
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
  text-align: left;
}

.confirmation-detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  padding: 5px 0;
  border-bottom: 1px dotted #ACA899;
}

.confirmation-detail-row:last-child {
  border-bottom: none;
}

.confirmation-detail-label {
  color: #333333;
  font-weight: bold;
  flex: 1;
}

.confirmation-detail-value {
  color: #0A246A;
  flex: 2;
  text-align: right;
}

.confirmation-actions {
  margin-top: 20px;
}

.return-btn {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  color: #0A246A;
  border: 1px solid #7F9DB9;
  padding: 10px 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  border-radius: 3px;
  transition: background 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.return-btn:hover {
  background: linear-gradient(to bottom, #E5F1FB, #D9E9F7);
  border-color: #0A246A;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .payment-content {
    flex-direction: column;
  }

  .payment-form-section,
  .payment-summary-section {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .payment-page {
    padding: 15px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .payment-actions {
    flex-direction: column;
  }

  .process-payment-btn,
  .cancel-payment-btn {
    width: 100%;
  }
}
