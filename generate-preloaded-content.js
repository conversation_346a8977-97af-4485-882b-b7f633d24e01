// Script to generate preloaded article content for Bingopedia
const fs = require('fs');
const path = require('path');

// Directory containing the HTML files
const sourceDir = path.join(__dirname, 'bingopedia');
const outputFile = path.join(__dirname, 'js', 'preloaded-article-content.js');

// Function to extract article content from HTML file
function extractArticleContent(htmlContent) {
    // Create a simple regex to extract content between article tags
    const match = htmlContent.match(/<article id="article-content"[^>]*>([\s\S]*?)<\/article>/i);
    
    if (match && match[1]) {
        return match[1].trim();
    }
    
    return null;
}

// Function to generate preloaded content
function generatePreloadedContent() {
    // Get all HTML files in the source directory
    const files = fs.readdirSync(sourceDir);
    const htmlFiles = files.filter(file => file.endsWith('.html'));
    
    console.log(`Found ${htmlFiles.length} HTML files to process`);
    
    // Create an object to store article content
    const preloadedContent = {};
    
    // Process each HTML file
    htmlFiles.forEach(htmlFile => {
        const articleId = path.basename(htmlFile, '.html');
        const htmlFilePath = path.join(sourceDir, htmlFile);
        
        try {
            // Read HTML file content
            const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
            
            // Extract article content
            const articleContent = extractArticleContent(htmlContent);
            
            if (articleContent) {
                // Add to preloaded content
                preloadedContent[articleId] = articleContent;
                console.log(`Processed article: ${articleId}`);
            } else {
                console.warn(`Could not extract content from: ${htmlFile}`);
            }
        } catch (error) {
            console.error(`Error processing file ${htmlFilePath}: ${error.message}`);
        }
    });
    
    // Generate JavaScript file with preloaded content
    const jsContent = `// Preloaded article content for Bingopedia
// This file is auto-generated - do not edit manually
window.preloadedArticleContent = ${JSON.stringify(preloadedContent, null, 2)};
`;
    
    // Write to output file
    fs.writeFileSync(outputFile, jsContent, 'utf8');
    
    console.log(`Successfully generated preloaded content with ${Object.keys(preloadedContent).length} articles`);
    console.log(`Output file: ${outputFile}`);
}

// Run the generation process
generatePreloadedContent();
