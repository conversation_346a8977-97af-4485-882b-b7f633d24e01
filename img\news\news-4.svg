<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="#F1EFE2"/>
  
  <!-- Health Theme -->
  <rect x="50" y="50" width="300" height="200" fill="#FFFFFF" stroke="#ACA899" stroke-width="2"/>
  
  <!-- Mediterranean Diet Illustration -->
  <g>
    <!-- Plate -->
    <circle cx="200" cy="150" r="80" fill="#FFFFFF" stroke="#CCCCCC" stroke-width="2"/>
    <circle cx="200" cy="150" r="75" fill="none" stroke="#CCCCCC" stroke-width="1"/>
    
    <!-- Food Items -->
    <!-- Olive Oil Bottle -->
    <rect x="120" y="100" width="20" height="40" fill="#A0522D" stroke="#8B4513" stroke-width="1"/>
    <rect x="125" y="90" width="10" height="10" fill="#8B4513" stroke="#8B4513" stroke-width="1"/>
    <ellipse cx="130" cy="140" rx="15" ry="5" fill="#FFFF00" fill-opacity="0.3"/>
    
    <!-- Olives -->
    <circle cx="160" cy="120" r="8" fill="#556B2F" stroke="#333333" stroke-width="0.5"/>
    <circle cx="170" cy="130" r="8" fill="#556B2F" stroke="#333333" stroke-width="0.5"/>
    <circle cx="155" cy="135" r="8" fill="#556B2F" stroke="#333333" stroke-width="0.5"/>
    
    <!-- Tomatoes -->
    <circle cx="190" cy="120" r="10" fill="#FF6347" stroke="#8B0000" stroke-width="0.5"/>
    <circle cx="210" cy="125" r="10" fill="#FF6347" stroke="#8B0000" stroke-width="0.5"/>
    <circle cx="200" cy="140" r="10" fill="#FF6347" stroke="#8B0000" stroke-width="0.5"/>
    
    <!-- Fish -->
    <ellipse cx="240" cy="150" rx="30" ry="15" fill="#F0E68C" stroke="#DAA520" stroke-width="1"/>
    <path d="M270,150 L280,160 L280,140 Z" fill="#F0E68C" stroke="#DAA520" stroke-width="1"/>
    <circle cx="225" cy="145" r="2" fill="#000000"/>
    
    <!-- Nuts -->
    <ellipse cx="170" cy="170" rx="8" ry="6" fill="#8B4513" stroke="#654321" stroke-width="0.5"/>
    <ellipse cx="185" cy="175" rx="8" ry="6" fill="#8B4513" stroke="#654321" stroke-width="0.5"/>
    <ellipse cx="165" cy="185" rx="8" ry="6" fill="#8B4513" stroke="#654321" stroke-width="0.5"/>
    <ellipse cx="180" cy="190" rx="8" ry="6" fill="#8B4513" stroke="#654321" stroke-width="0.5"/>
    
    <!-- Greens/Vegetables -->
    <path d="M210,170 C220,160 230,180 240,170" fill="#90EE90" stroke="#006400" stroke-width="1"/>
    <path d="M215,180 C225,170 235,190 245,180" fill="#90EE90" stroke="#006400" stroke-width="1"/>
    <path d="M220,190 C230,180 240,200 250,190" fill="#90EE90" stroke="#006400" stroke-width="1"/>
  </g>
  
  <!-- Health Icons -->
  <g>
    <!-- Heart -->
    <path d="M100,220 C100,210 90,210 90,220 C90,230 100,240 100,240 C100,240 110,230 110,220 C110,210 100,210 100,220" fill="#FF6347" stroke="#8B0000" stroke-width="1"/>
    
    <!-- Apple -->
    <circle cx="140" cy="220" r="10" fill="#FF0000" stroke="#8B0000" stroke-width="1"/>
    <path d="M140,210 C145,205 150,210 145,215" fill="none" stroke="#006400" stroke-width="1"/>
    
    <!-- Carrot -->
    <path d="M170,230 L180,210 L190,230 Z" fill="#FFA500" stroke="#FF8C00" stroke-width="1"/>
    <path d="M180,210 L180,205" fill="none" stroke="#006400" stroke-width="1"/>
    <path d="M177,207 L183,207" fill="none" stroke="#006400" stroke-width="1"/>
    
    <!-- Broccoli -->
    <circle cx="220" cy="220" r="10" fill="#006400" stroke="#004200" stroke-width="1"/>
    <circle cx="215" cy="215" r="5" fill="#006400" stroke="#004200" stroke-width="1"/>
    <circle cx="225" cy="215" r="5" fill="#006400" stroke="#004200" stroke-width="1"/>
    <rect x="218" y="230" width="4" height="10" fill="#A0522D"/>
    
    <!-- Fish -->
    <ellipse cx="260" cy="220" rx="15" ry="8" fill="#ADD8E6" stroke="#4682B4" stroke-width="1"/>
    <path d="M275,220 L280,225 L280,215 Z" fill="#ADD8E6" stroke="#4682B4" stroke-width="1"/>
    <circle cx="252" cy="218" r="1" fill="#000000"/>
  </g>
  
  <!-- Title Banner -->
  <rect x="50" y="20" width="300" height="30" fill="#4682B4"/>
  <text x="200" y="40" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#FFFFFF">MEDITERRANEAN DIET BENEFITS</text>
</svg>
