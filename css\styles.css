/* Windows XP Theme Styles */
:root {
  /* Windows XP fonts */
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-size: 11px;

  /* Windows XP color scheme - more accurate colors */
  --window-bg: #ECE9D8; /* Classic Windows background */
  --title-bar-bg: #0A246A; /* Windows XP blue title bar */
  --title-bar-active: linear-gradient(to bottom, #0A246A, #A6CAF0); /* Active title bar gradient */
  --title-bar-text: white;
  --border-color: #7F9DB9; /* Windows XP border color */
  --button-face: #ECE9D8; /* Button face color */
  --button-highlight: white;
  --button-shadow: #ACA899;
  --button-border: #003C74;
  --text-color: #000000;
  --link-color: #0000FF;
  --link-visited: #800080;
  --menu-hover-bg: #316AC5; /* Menu hover background */
  --menu-hover-text: white;
  --toolbar-bg: #F1EFE2; /* Toolbar background */
  --address-bar-bg: white;
  --status-bar-bg: #ECE9D8;
  --scrollbar-face: #D4D0C8;
  --scrollbar-shadow: #808080;
  --scrollbar-highlight: white;
  --ie-blue: #0A246A; /* Internet Explorer blue */
  --ie-light-blue: #A6CAF0; /* Internet Explorer light blue */

  color-scheme: light;
  color: var(--text-color);
  background-color: var(--window-bg);
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

body {
  display: flex;
  flex-direction: column;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--window-bg);
  padding: 0;
  margin: 0;
  overflow: hidden;
}

/* Browser Chrome Styles */
.browser-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* Menu Bar Styles */
.menu-bar {
  display: flex;
  background-color: #F1EFE2;
  border-bottom: 1px solid #ACA899;
  padding: 2px 0;
  user-select: none;
  font-size: 11px;
  border-top: 1px solid #FFFFFF;
}

.menu-container {
  position: relative;
}

.menu-item {
  padding: 3px 10px;
  font-size: 11px;
  cursor: pointer;
  margin: 1px;
  border: 1px solid transparent;
}

.menu-container:hover .menu-item {
  background-color: var(--menu-hover-bg);
  color: var(--menu-hover-text);
  border-radius: 2px;
  border: 1px solid #7DA2CE;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 100;
  min-width: 150px;
}

.menu-container:hover .dropdown-menu {
  display: block;
}

.dropdown-item {
  padding: 4px 20px;
  font-size: 11px;
  cursor: pointer;
  white-space: nowrap;
}

.dropdown-item:hover {
  background-color: var(--menu-hover-bg);
  color: var(--menu-hover-text);
}

.dropdown-separator {
  height: 1px;
  background-color: #ACA899;
  margin: 3px 2px;
}

/* Toolbar Styles */
.browser-toolbar {
  display: flex;
  align-items: center;
  background-color: var(--toolbar-bg);
  padding: 5px;
  border-bottom: 1px solid var(--button-shadow);
}

.nav-buttons {
  display: flex;
  margin-right: 10px;
}

.toolbar-button {
  background-color: var(--button-face);
  border: 1px solid var(--button-shadow);
  border-radius: 3px;
  padding: 2px 6px;
  margin-right: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
}

.toolbar-button svg {
  display: block;
  width: 20px;
  height: 20px;
  margin: 0;
  padding: 0;
}

.toolbar-button:hover {
  background-color: var(--button-highlight);
}

.toolbar-button:active {
  border-color: var(--button-shadow);
  background-color: var(--button-shadow);
}



.address-bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  margin: 0 5px;
}

.address-label {
  margin-right: 5px;
  font-size: 11px;
}

.address-input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  background-color: var(--address-bar-bg);
  padding-left: 2px;
}

.ssl-lock {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  margin-right: 5px;
  z-index: 1;
  color: #008000;
  font-size: 14px;
}

.address-input {
  flex: 1;
  height: 22px;
  border: none;
  padding: 2px 5px;
  font-size: 11px;
  background-color: transparent;
}

.go-button {
  margin-left: 5px;
  background-color: var(--button-face);
  border: 1px solid var(--button-shadow);
  border-radius: 3px;
  padding: 2px 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.go-button svg {
  display: block;
  width: 20px;
  height: 20px;
  margin: 0;
  padding: 0;
}

.go-button:hover {
  background-color: var(--button-highlight);
}

.go-button:active {
  border-color: var(--button-shadow);
  background-color: var(--button-shadow);
}

/* Bookmark Bar Styles */
.bookmark-bar {
  display: flex;
  background-color: #F1EFE2;
  border-bottom: 1px solid #ACA899;
  padding: 3px 6px;
  user-select: none;
}

.bookmark {
  font-size: 11px;
  padding: 2px 8px;
  margin-right: 10px;
  cursor: pointer;
  color: #0000CC;
  position: relative;
}

.bookmark::before {
  content: "🔖";
  font-size: 10px;
  margin-right: 4px;
  color: #666666;
}

.bookmark:hover {
  text-decoration: underline;
  color: #CC0000;
}

/* Zoom Controls Styles */
.zoom-controls {
  display: flex;
  align-items: center;
  margin-left: auto;
  padding-right: 10px;
}

.zoom-button {
  background-color: var(--button-face);
  border: 1px solid var(--button-shadow);
  border-radius: 3px;
  padding: 2px 6px;
  margin: 0 2px;
  cursor: pointer;
  font-size: 11px;
  height: 22px;
  min-width: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-button:hover {
  background-color: var(--button-highlight);
}

.zoom-button:active {
  border-color: var(--button-shadow);
  background-color: var(--button-shadow);
}

#zoom-level {
  font-size: 11px;
  margin: 0 5px;
  min-width: 40px;
  text-align: center;
}

#zoom-reset-button {
  font-size: 10px;
}

/* Browser Content Styles */
.browser-content {
  flex: 1;
  overflow: auto;
  background-color: white;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allows content to shrink below its natural size */
}

/* Browser content wrapper for zoom */
.browser-content {
  position: relative;
  overflow: auto !important; /* Always allow scrolling */
}

/* Zoom container - this is the element that will be zoomed */
.zoom-container {
  width: 100%;
  min-height: 100%;
  transform-origin: 0 0; /* Top left origin for predictable scaling */
  transition: transform 0.2s ease;
}

/* Ensure all direct children of zoom container take appropriate width */
.zoom-container > * {
  max-width: 100%;
  box-sizing: border-box;
}

/* Special handling for specific page containers to ensure they fit properly */
.search-engine,
.games-page,
.finance-page,
.weather-page,
.maps-page,
.calendar-page,
.news-page,
.shopping-page,
.music-page,
.chat-page,
.bingopedia-page {
  width: 100%;
  box-sizing: border-box;
}

/* Status Bar Styles */
.status-bar {
  display: flex;
  justify-content: space-between;
  background-color: #F1EFE2;
  border-top: 1px solid #ACA899;
  padding: 3px 6px;
  font-size: 11px;
  color: #333333;
}

.status-text {
  position: relative;
  padding-left: 20px;
}

.status-text::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #008800;
  font-weight: bold;
}

.connection-status {
  border-left: 1px solid #ACA899;
  padding-left: 10px;
  margin-left: 10px;
}

.console-button {
  border-left: 1px solid #ACA899;
  padding-left: 10px;
  margin-left: 10px;
  cursor: pointer;
  font-size: 11px;
  color: #333333;
}

.console-button:hover {
  text-decoration: underline;
  color: #0000CC;
}

/* Error Page Styles */
.error-page {
  padding: 20px;
  text-align: center;
}

.error-page h1 {
  color: #CC0000;
  font-size: 24px;
  margin-bottom: 10px;
}

.error-page button {
  background-color: var(--button-face);
  border: 1px solid var(--button-shadow);
  border-radius: 3px;
  padding: 5px 15px;
  margin-top: 20px;
  cursor: pointer;
  font-size: 12px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .address-label {
    display: none;
  }

  .nav-buttons {
    margin-right: 5px;
  }

  .toolbar-button {
    padding: 3px 5px;
  }
}

@media (max-width: 480px) {
  .bookmark-bar {
    overflow-x: auto;
    white-space: nowrap;
  }
}
