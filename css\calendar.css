/* Calendar Page Styles */
.calendar-page {
  font-family: '<PERSON><PERSON><PERSON>', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 30px;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.calendar-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 25px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.calendar-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.calendar-content {
  display: flex;
  flex: 1;
  gap: 25px;
  min-height: 0; /* Ensures flex items can shrink below their content size */
  height: 100%; /* Fill available height */
}

/* Calendar Container Styles */
.calendar-container {
  flex: 1;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.calendar-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: linear-gradient(to bottom, #F1EFE2, #E1E1E1);
  border-bottom: 1px solid #ACA899;
}

.calendar-nav-button {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  color: #0A246A;
  border: 1px solid #7F9DB9;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  border-radius: 3px;
}

.calendar-nav-button:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
}

.current-month {
  font-size: 16px;
  font-weight: bold;
  color: #0A246A;
}

.calendar-grid {
  padding: 15px;
  flex: 1;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  margin-bottom: 10px;
}

.weekday {
  text-align: center;
  font-weight: bold;
  color: #0A246A;
  padding: 5px;
  background-color: #E1E1E1;
  border: 1px solid #ACA899;
  border-radius: 3px;
  font-size: 12px;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 1fr);
  gap: 5px;
  height: calc(100% - 40px);
}

.calendar-day {
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 5px;
  background-color: #F9F9F9;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  min-height: 60px;
}

.calendar-day:hover {
  background-color: #E1E1E1;
}

.calendar-day.other-month {
  background-color: #F1EFE2;
  color: #999999;
}

.calendar-day.today {
  background-color: #E6F0FF;
  border: 1px solid #7F9DB9;
}

.calendar-day.has-events {
  background-color: #FFFFE1;
}

.day-number {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}

.day-events {
  font-size: 10px;
  color: #0A246A;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Reminders Container Styles */
.reminders-container {
  width: 300px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.reminders-header {
  padding: 15px;
  background: linear-gradient(to bottom, #F1EFE2, #E1E1E1);
  border-bottom: 1px solid #ACA899;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reminders-header h2 {
  margin: 0;
  font-size: 16px;
  color: #0A246A;
}

.add-reminder-button {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  color: #0A246A;
  border: 1px solid #7F9DB9;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  border-radius: 3px;
}

.add-reminder-button:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
}

.reminders-list {
  padding: 15px;
  overflow-y: auto;
  flex: 1;
}

.reminder-item {
  border: 1px solid #ACA899;
  border-radius: 3px;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #F9F9F9;
  cursor: pointer;
}

.reminder-item:hover {
  background-color: #E1E1E1;
}

.reminder-date {
  font-size: 12px;
  font-weight: bold;
  color: #0A246A;
  margin-bottom: 5px;
}

.reminder-title {
  font-size: 14px;
  margin-bottom: 5px;
}

.reminder-time {
  font-size: 12px;
  color: #666666;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .calendar-content {
    flex-direction: column;
  }

  .reminders-container {
    width: 100%;
    margin-top: 20px;
  }
}

@media (max-width: 480px) {
  .calendar-page {
    padding: 15px;
  }

  .calendar-header-bar {
    padding: 10px 15px;
    margin-bottom: 15px;
  }

  .calendar-content {
    gap: 15px;
  }
}
