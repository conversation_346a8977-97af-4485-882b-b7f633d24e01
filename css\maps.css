/* Maps Page Styles */
.maps-page {
  font-family: '<PERSON>homa', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 30px;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.maps-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 25px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.maps-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.maps-content {
  display: flex;
  flex: 1;
  gap: 25px;
  min-height: 0; /* Ensures flex items can shrink below their content size */
  height: 100%; /* Fill available height */
}

.maps-sidebar {
  width: 300px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.maps-tabs {
  display: flex;
  border-bottom: 1px solid #ACA899;
}

.maps-tab {
  flex: 1;
  padding: 10px;
  text-align: center;
  background: linear-gradient(to bottom, #F1EFE2, #E1E1E1);
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  border-right: 1px solid #ACA899;
}

.maps-tab:last-child {
  border-right: none;
}

.maps-tab.active {
  background: linear-gradient(to bottom, #0A246A, #3A6EA5);
  color: white;
}

.maps-search-panel, .maps-directions-panel {
  padding: 15px;
  display: none;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  flex-grow: 1;
}

.maps-search-panel.active, .maps-directions-panel.active {
  display: flex;
}

.search-input-group, .direction-input-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.search-input-group label, .direction-input-group label {
  font-size: 12px;
  font-weight: bold;
  color: #0A246A;
}

.search-input, .direction-input {
  padding: 8px 10px;
  border: 1px solid #7F9DB9;
  border-radius: 3px;
  font-size: 12px;
  background-color: white;
}

.search-button, .direction-button {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  border: 1px solid #7F9DB9;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  color: #0A246A;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  font-weight: bold;
  margin-top: 5px;
}

.search-button:hover, .direction-button:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
}

.search-button:active, .direction-button:active {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
  transform: translateY(1px);
}

.maps-options {
  padding: 10px 15px;
  border-top: 1px solid #ACA899;
  background-color: #F1EFE2;
}

.maps-option-group {
  margin-bottom: 15px;
}

.maps-option-group h3 {
  font-size: 13px;
  color: #0A246A;
  margin: 0 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 1px solid #D4D0C8;
}

.maps-option {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.maps-option input[type="checkbox"],
.maps-option input[type="radio"] {
  margin-right: 8px;
}

.maps-option label {
  font-size: 12px;
  color: #333333;
  cursor: pointer;
}

.maps-display {
  flex: 1;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  overflow: hidden;
  position: relative;
}

.map-container {
  width: 100%;
  height: 100%;
  background-color: #E8EAED;
  position: relative;
}

.map-grid {
  width: 100%;
  height: 100%;
  position: relative;
  transition: all 0.3s ease;
  transform-origin: center center;
  /* Fallback background in case the image doesn't load */
  background-color: #E8EAED;
  background-image: linear-gradient(#CCCCCC 1px, transparent 1px),
                    linear-gradient(90deg, #CCCCCC 1px, transparent 1px);
  background-size: 50px 50px;
  overflow: hidden;
}

.map-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.map-marker {
  position: absolute;
  width: 32px;
  height: 48px;
  transform: translate(-50%, -100%);
  cursor: pointer;
  background-image: url('../img/map-marker.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
  z-index: 10; /* Ensure markers appear above the map background */
}

.map-marker:hover {
  transform: translate(-50%, -100%) scale(1.1);
}

.satellite-view {
  background-image: url('../img/satellite-background.svg') !important;
  background-size: cover !important;
  background-position: center !important;
}

.map-marker.start-marker {
  background-image: url('../img/map-marker-start.svg');
}

.map-marker.end-marker {
  background-image: url('../img/map-marker-end.svg');
}

.map-route {
  position: absolute;
  height: 4px;
  background-color: #4285F4;
  transform-origin: 0 0;
  z-index: 5; /* Above the map background but below markers */
  border-radius: 2px;
  box-shadow: 0 0 4px rgba(66, 133, 244, 0.6);
}

.map-controls {
  position: absolute;
  right: 10px;
  top: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 10;
}

.map-control-button {
  width: 30px;
  height: 30px;
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  border: 1px solid #7F9DB9;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.map-control-button:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
}

.map-control-button:active {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

.map-info-window {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  padding: 8px;
  min-width: 150px;
  font-size: 12px;
  position: relative;
}

.map-info-window:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.info-title {
  font-weight: bold;
  color: #0A246A;
  margin-bottom: 5px;
  font-size: 13px;
}

.info-content {
  color: #333;
  font-size: 11px;
}

.info-directions-btn {
  margin-top: 8px;
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  border: 1px solid #7F9DB9;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 11px;
  color: #0A246A;
  border-radius: 3px;
  display: block;
  width: 100%;
  text-align: center;
}

.info-directions-btn:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
}

.search-results, .direction-results {
  margin-top: 15px;
  border-top: 1px solid #ACA899;
  padding-top: 15px;
}

.search-results h3, .direction-results h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #0A246A;
}

.result-item {
  padding: 10px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  margin-bottom: 8px;
  background-color: #F9F9F9;
  cursor: pointer;
  transition: all 0.2s ease;
}

.result-item:hover {
  background-color: #F1EFE2;
  border-color: #7F9DB9;
}

.result-title {
  font-weight: bold;
  font-size: 13px;
  color: #0A246A;
  margin-bottom: 5px;
}

.result-address {
  font-size: 12px;
  color: #666666;
}

.direction-step {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #E1E1E1;
}

.step-number {
  width: 24px;
  height: 24px;
  background-color: #0A246A;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
  flex-shrink: 0;
}

.step-instruction {
  flex: 1;
  font-size: 12px;
  color: #333333;
}

.step-distance {
  font-size: 12px;
  color: #666666;
  margin-left: 10px;
  white-space: nowrap;
}

.direction-summary {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #ACA899;
  margin-bottom: 10px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-label {
  font-size: 11px;
  color: #666666;
}

.summary-value {
  font-size: 14px;
  font-weight: bold;
  color: #0A246A;
}

/* Loading Spinner */
.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3A6EA5;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 40px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .maps-content {
    flex-direction: column;
  }

  .maps-sidebar {
    width: 100%;
    margin-bottom: 15px;
    height: 350px;
  }

  .maps-display {
    height: 400px;
  }
}

@media (max-width: 480px) {
  .maps-page {
    padding: 15px;
  }

  .maps-header-bar {
    padding: 10px 15px;
    margin-bottom: 15px;
  }

  .maps-content {
    gap: 15px;
  }
}
