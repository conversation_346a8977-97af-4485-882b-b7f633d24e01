<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingo Maps</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/maps.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="maps-page">
        <div class="maps-header-bar">
          <h1>Bingo Maps</h1>
        </div>

        <div class="maps-content">
          <div class="maps-sidebar">
            <div class="maps-tabs">
              <div class="maps-tab active" data-tab="search">Search</div>
              <div class="maps-tab" data-tab="directions">Directions</div>
            </div>

            <!-- Search Panel -->
            <div class="maps-search-panel active">
              <div class="search-input-group">
                <label for="search-input">Find a place</label>
                <input type="text" id="search-input" class="search-input" placeholder="Enter a location">
                <button id="search-button" class="search-button">Search</button>
              </div>

              <div class="search-results" id="search-results">
                <h3>Popular Places</h3>
                <div class="result-item">
                  <div class="result-title">Taj Mahal</div>
                  <div class="result-address">Agra, Uttar Pradesh, India</div>
                </div>
                <div class="result-item">
                  <div class="result-title">India Gate</div>
                  <div class="result-address">Rajpath, New Delhi, India</div>
                </div>
                <div class="result-item">
                  <div class="result-title">Gateway of India</div>
                  <div class="result-address">Apollo Bandar, Mumbai, India</div>
                </div>
                <div class="result-item">
                  <div class="result-title">Mysore Palace</div>
                  <div class="result-address">Mysore, Karnataka, India</div>
                </div>
              </div>
            </div>

            <!-- Directions Panel -->
            <div class="maps-directions-panel">
              <div class="direction-input-group">
                <label for="start-input">Start</label>
                <input type="text" id="start-input" class="direction-input" placeholder="Choose starting point">
              </div>
              <div class="direction-input-group">
                <label for="destination-input">Destination</label>
                <input type="text" id="destination-input" class="direction-input" placeholder="Choose destination">
              </div>
              <button id="direction-button" class="direction-button">Get Directions</button>

              <div class="direction-results" id="direction-results" style="display: none;">
                <div class="direction-summary">
                  <div class="summary-item">
                    <div class="summary-value">450 km</div>
                    <div class="summary-label">Distance</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-value">5h 30m</div>
                    <div class="summary-label">Duration</div>
                  </div>
                </div>

                <h3>Route Directions</h3>
                <div class="direction-step">
                  <div class="step-number">1</div>
                  <div class="step-instruction">Head east on Main Road toward Park Street</div>
                  <div class="step-distance">1.2 km</div>
                </div>
                <div class="direction-step">
                  <div class="step-number">2</div>
                  <div class="step-instruction">Turn right onto National Highway 8</div>
                  <div class="step-distance">120 km</div>
                </div>
                <div class="direction-step">
                  <div class="step-number">3</div>
                  <div class="step-instruction">Continue onto Express Highway</div>
                  <div class="step-distance">200 km</div>
                </div>
                <div class="direction-step">
                  <div class="step-number">4</div>
                  <div class="step-instruction">Take exit 24 toward City Center</div>
                  <div class="step-distance">2.5 km</div>
                </div>
                <div class="direction-step">
                  <div class="step-number">5</div>
                  <div class="step-instruction">Turn left onto Destination Road</div>
                  <div class="step-distance">0.8 km</div>
                </div>
              </div>
            </div>

            <!-- Map Options -->
            <div class="maps-options">
              <div class="maps-option-group">
                <h3>Map Type</h3>
                <div class="maps-option">
                  <input type="radio" id="map-view-option" name="map-type" checked>
                  <label for="map-view-option">Map view</label>
                </div>
                <div class="maps-option">
                  <input type="radio" id="satellite-option" name="map-type">
                  <label for="satellite-option">Satellite view</label>
                </div>
              </div>

              <div class="maps-option-group">
                <h3>Map Layers</h3>
                <div class="maps-option">
                  <input type="checkbox" id="traffic-option">
                  <label for="traffic-option">Show traffic</label>
                </div>
                <div class="maps-option">
                  <input type="checkbox" id="transit-option">
                  <label for="transit-option">Show public transit</label>
                </div>
              </div>
            </div>
          </div>

          <div class="maps-display">
            <div class="map-container">
              <div class="map-grid" id="map-grid">
                <!-- Map background image -->
                <img src="../img/map-background.svg" class="map-background" id="map-background" alt="Map Background">
                <!-- Map will be rendered here -->
              </div>
              <div class="map-controls">
                <div class="map-control-button" id="zoom-in">+</div>
                <div class="map-control-button" id="zoom-out">−</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/maps.js"></script>
</body>
</html>
