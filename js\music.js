// Music page functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize music page
  initMusicPage();

  // Add debug keyboard shortcut (Ctrl+Shift+D) to show/hide debug info
  document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
      const debugElement = document.getElementById('audio-debug');
      if (debugElement) {
        debugElement.style.display = debugElement.style.display === 'none' ? 'block' : 'none';
        updateDebugInfo();
      }
    }
  });

  // Make the handleProgressClick function globally available
  window.handleProgressClick = function(event) {
    // Prevent default behavior and stop propagation
    event.preventDefault();
    event.stopPropagation();

    console.log('Progress clicked directly from HTML');

    // Call the seekToPosition function
    seekToPosition(event);
  };

  // Make the toggleDebugMode function globally available
  window.toggleDebugMode = function() {
    // Toggle the debug panel
    const debugElement = document.getElementById('audio-debug');
    if (debugElement) {
      debugElement.style.display = debugElement.style.display === 'none' ? 'block' : 'none';
      updateDebugInfo();
    }

    // Show the audio element controls for direct interaction
    const audioPlayer = document.getElementById('audio-player');
    if (audioPlayer) {
      audioPlayer.style.display = audioPlayer.style.display === 'none' ? 'block' : 'none';

      if (audioPlayer.style.display === 'block') {
        // Position it below the player
        audioPlayer.style.position = 'absolute';
        audioPlayer.style.bottom = '20px';
        audioPlayer.style.left = '50%';
        audioPlayer.style.transform = 'translateX(-50%)';
        audioPlayer.style.zIndex = '1000';
        audioPlayer.style.width = '80%';

        // Log audio player state
        console.log('Audio player state:', {
          src: audioPlayer.src,
          readyState: audioPlayer.readyState,
          paused: audioPlayer.paused,
          duration: audioPlayer.duration,
          currentTime: audioPlayer.currentTime,
          volume: audioPlayer.volume
        });
      }
    }

    // Add a test seek button if it doesn't exist
    let testSeekButton = document.getElementById('test-seek-button');
    if (!testSeekButton) {
      testSeekButton = document.createElement('button');
      testSeekButton.id = 'test-seek-button';
      testSeekButton.textContent = 'Test Seek to 50%';
      testSeekButton.style.position = 'fixed';
      testSeekButton.style.bottom = '100px';
      testSeekButton.style.left = '50%';
      testSeekButton.style.transform = 'translateX(-50%)';
      testSeekButton.style.zIndex = '1000';
      testSeekButton.style.padding = '10px';
      testSeekButton.style.backgroundColor = '#0A246A';
      testSeekButton.style.color = 'white';
      testSeekButton.style.border = 'none';
      testSeekButton.style.borderRadius = '5px';
      testSeekButton.style.cursor = 'pointer';

      testSeekButton.addEventListener('click', function() {
        if (audioPlayer) {
          const duration = audioPlayer.duration || 0;
          if (duration > 0) {
            audioPlayer.currentTime = duration * 0.5; // Seek to 50%
            console.log('Test seek to 50%:', audioPlayer.currentTime);
            updateProgress();
          }
        }
      });

      document.body.appendChild(testSeekButton);
    } else {
      testSeekButton.style.display = testSeekButton.style.display === 'none' ? 'block' : 'none';
    }
  };
});

function initMusicPage() {
  // Initialize sidebar navigation
  initSidebarNav();

  // Initialize album cards
  initAlbumCards();

  // Initialize song list
  initSongList();

  // Initialize player controls
  initPlayerControls();

  // Initialize radio stations
  initRadioStations();

  // Initialize browse categories
  initBrowseCategories();

  // Update status bar
  updateStatusBar('Music page loaded successfully', 'Connected');
}

// Sidebar Navigation Functions
function initSidebarNav() {
  const sidebarItems = document.querySelectorAll('.sidebar-menu-item');

  sidebarItems.forEach(item => {
    item.addEventListener('click', function() {
      // Handle view navigation
      if (item.hasAttribute('data-view')) {
        const viewName = item.getAttribute('data-view');
        switchView(viewName);
      }

      // Handle playlist selection
      if (item.hasAttribute('data-playlist')) {
        const playlistName = item.getAttribute('data-playlist');
        loadPlaylist(playlistName);
      }

      // Update active state
      sidebarItems.forEach(i => i.classList.remove('active'));
      item.classList.add('active');
    });
  });
}

function switchView(viewName) {
  // Hide all views
  const views = document.querySelectorAll('.music-view');
  views.forEach(view => view.classList.remove('active'));

  // Show selected view
  const selectedView = document.getElementById(viewName + '-view');
  if (selectedView) {
    selectedView.classList.add('active');
  }

  // Update status bar
  updateStatusBar(`Viewing ${viewName} page`, 'Connected');
}

function loadPlaylist(playlistName) {
  console.log('Loading playlist:', playlistName);

  // In a real app, this would load the playlist data
  // For demo purposes, just update the status bar
  updateStatusBar(`Loading playlist: ${playlistName}`, 'Connected');

  // Simulate loading delay
  setTimeout(() => {
    updateStatusBar(`Playlist loaded: ${playlistName}`, 'Connected');
  }, 300);
}

// Album Cards Functions
function initAlbumCards() {
  const albumCards = document.querySelectorAll('.album-card');

  albumCards.forEach(card => {
    card.addEventListener('click', function() {
      const albumId = card.getAttribute('data-album');
      playAlbum(albumId);
    });
  });
}

function playAlbum(albumId) {
  console.log('Playing album:', albumId);

  // Get album info
  const albumCard = document.querySelector(`[data-album="${albumId}"]`);
  if (albumCard) {
    const albumTitle = albumCard.querySelector('.album-title').textContent;
    const albumArtist = albumCard.querySelector('.album-artist').textContent;
    const albumImage = albumCard.querySelector('img').src;

    // Update now playing info
    updateNowPlaying(albumTitle, albumArtist, albumImage);

    // Map albums to their first song
    const albumSongMap = {
      'album1': 'song1', // NCS Releases - Royalty
      'album2': 'song1', // NCS Classics - Use song1 as fallback
      'album3': 'song1', // When We All Fall Asleep... - Use song1 as fallback
      'album4': 'song1'  // Fine Line - Use song1 as fallback
    };

    // Get the first song of the album
    const firstSongId = albumSongMap[albumId] || 'song1';

    // Find the song element
    const songElement = document.querySelector(`[data-song="${firstSongId}"]`);

    if (songElement) {
      // Play the first song of the album
      playSong(firstSongId, songElement);
    } else {
      // If song element not found, just start playback with default audio
      if (audioPlayer) {
        try {
          // First pause any current playback
          audioPlayer.pause();

          // Set the audio source to the default
          audioPlayer.src = '../audio/song1.mp3';

          // Start playback
          startPlayback();
        } catch (e) {
          console.error('Error playing album:', e);
          useFallbackAudio();
        }
      } else {
        useFallbackAudio();
      }

      // Update status bar
      updateStatusBar(`Now playing: ${albumTitle} by ${albumArtist}`, 'Connected');
    }
  }
}

// Song List Functions
function initSongList() {
  const songItems = document.querySelectorAll('.song-item');

  // Track the currently playing song item
  window.currentPlayingSongItem = null;

  songItems.forEach(item => {
    // Add click event to the entire song item
    item.addEventListener('click', function() {
      const songId = item.getAttribute('data-song');
      playSong(songId, item);
    });

    // Add specific click event to the play button
    const playButton = item.querySelector('.song-play-button');
    if (playButton) {
      playButton.addEventListener('click', function(e) {
        // Prevent the click from triggering the parent's click event
        e.stopPropagation();

        const songId = item.getAttribute('data-song');

        // If this is already the playing song, toggle play/pause
        if (window.currentPlayingSongItem === item && isPlaying) {
          // Pause the current song
          togglePlayPause();
          // Update the play button icon
          playButton.textContent = '▶';
        } else {
          // Reset all play buttons to play icon
          document.querySelectorAll('.song-play-button').forEach(btn => {
            btn.textContent = '▶';
          });

          // Play the selected song
          playSong(songId, item);

          // Update the play button icon
          playButton.textContent = '⏸';

          // Update the currently playing song item
          window.currentPlayingSongItem = item;
        }
      });
    }
  });
}

function playSong(songId, songElement) {
  console.log('Playing song:', songId);

  // Get song info
  const songTitle = songElement.querySelector('.song-title').textContent;
  const songArtist = songElement.querySelector('.song-artist').textContent;
  const songAlbum = songElement.querySelector('.song-album').textContent;

  // Update now playing info
  updateNowPlaying(songTitle, songArtist, getAlbumImageForSong(songId));

  // Update the play button icon in the song list
  if (songElement) {
    // Reset all play buttons to play icon
    document.querySelectorAll('.song-play-button').forEach(btn => {
      btn.textContent = '▶';
    });

    // Set this song's play button to pause icon
    const playButton = songElement.querySelector('.song-play-button');
    if (playButton) {
      playButton.textContent = '⏸';
    }

    // Update the currently playing song item
    window.currentPlayingSongItem = songElement;
  }

  // Try to load the corresponding audio file
  if (audioPlayer) {
    try {
      // First pause any current playback
      audioPlayer.pause();

      // Map song IDs to audio files
      const songAudioMap = {
        'song1': '../audio/song1.mp3', // Royalty by NCS
        'song2': '../audio/song1.mp3', // Use song1 as fallback
        'song3': '../audio/song1.mp3', // Use song1 as fallback
        'song4': '../audio/song1.mp3', // Use song1 as fallback
        'song5': '../audio/song1.mp3'  // Use song1 as fallback
      };

      // Set the audio source
      const audioSrc = songAudioMap[songId] || '../audio/song1.mp3';
      audioPlayer.src = audioSrc;

      // Start playback
      startPlayback();

      // Update status bar
      updateStatusBar(`Now playing: ${songTitle} by ${songArtist}`, 'Connected');
    } catch (e) {
      console.error('Error loading song:', e);

      // Use fallback audio with different frequencies for different songs
      if (window.fallbackAudio) {
        // Different frequencies for different songs to simulate different tracks
        const frequencies = {
          'song1': 440, // A4
          'song2': 494, // B4
          'song3': 523, // C5
          'song4': 587, // D5
          'song5': 659  // E5
        };

        // Use the frequency for this song or default to A4
        const frequency = frequencies[songId] || 440;

        // Update the fallback audio frequency
        if (window.fallbackAudio.oscillator && window.fallbackAudio.isPlaying) {
          // If already playing, stop the current oscillator
          window.fallbackAudio.oscillator.stop();
          window.fallbackAudio.isPlaying = false;
        }

        // Create a new oscillator with the song's frequency
        const oscillator = window.fallbackAudio.context.createOscillator();
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(frequency, window.fallbackAudio.context.currentTime);
        oscillator.connect(window.fallbackAudio.gainNode);
        oscillator.start();

        // Store the new oscillator
        window.fallbackAudio.oscillator = oscillator;
        window.fallbackAudio.isPlaying = true;

        // Update UI
        document.getElementById('play-pause-button').textContent = '⏸';
        isPlaying = true;

        // Update status bar
        updateStatusBar(`Now playing: ${songTitle} by ${songArtist} (fallback audio)`, 'Connected');
      } else {
        // Create fallback audio if it doesn't exist
        createFallbackAudio();
        // Try again after creating fallback
        setTimeout(() => playSong(songId, songElement), 100);
      }
    }
  } else {
    // If HTML5 audio is not available, try to use the fallback with the correct frequency
    // Different frequencies for different songs to simulate different tracks
    const frequencies = {
      'song1': 440, // A4
      'song2': 494, // B4
      'song3': 523, // C5
      'song4': 587, // D5
      'song5': 659  // E5
    };

    // Use the frequency for this song or default to A4
    const frequency = frequencies[songId] || 440;

    useFallbackAudio(frequency);
    updateStatusBar(`Now playing: ${songTitle} by ${songArtist} (fallback audio)`, 'Connected');
  }
}

function getAlbumImageForSong(songId) {
  // Map song IDs to album images
  const songAlbumMap = {
    'song1': '../img/album1.svg', // Royalty - No Copyright Sounds
    'song2': '../img/album2.svg', // Mortals - No Copyright Sounds
    'song3': '../img/album3.svg', // Dance Monkey - Tones and I (using Billie's album art as placeholder)
    'song4': '../img/album4.svg', // Someone You Loved - Lewis Capaldi (using Harry's album art as placeholder)
    'song5': '../img/album3.svg'  // Bad Guy - Billie Eilish
  };

  return songAlbumMap[songId] || '../img/album1.svg';
}

// Player Controls Functions
function initPlayerControls() {
  // Play/Pause button
  const playPauseButton = document.getElementById('play-pause-button');
  if (playPauseButton) {
    playPauseButton.addEventListener('click', togglePlayPause);
  }

  // Previous button
  const prevButton = document.getElementById('prev-button');
  if (prevButton) {
    prevButton.addEventListener('click', playPrevious);
  }

  // Next button
  const nextButton = document.getElementById('next-button');
  if (nextButton) {
    nextButton.addEventListener('click', playNext);
  }

  // Progress bar
  const progressBar = document.querySelector('.progress-bar');
  if (progressBar) {
    // Add click event to seek to position
    progressBar.addEventListener('click', seekToPosition);
    progressBar.addEventListener('mousedown', seekToPosition); // Add mousedown as backup

    // Add hover effect to show potential seek position
    progressBar.addEventListener('mousemove', showSeekPosition);
    progressBar.addEventListener('mouseleave', hideSeekPosition);

    // Also add click event to the progress container
    const progressContainer = document.querySelector('.progress-container');
    if (progressContainer) {
      progressContainer.addEventListener('click', function(event) {
        // Only handle clicks directly on the progress container, not its children
        if (event.target === progressContainer) {
          seekToPosition(event);
        }
      });
    }

    // Add click event to the progress element itself
    const progressElement = document.getElementById('progress');
    if (progressElement) {
      progressElement.addEventListener('click', function(event) {
        // Stop propagation to prevent double-handling
        event.stopPropagation();
        seekToPosition(event);
      });
    }

    console.log('Progress bar click events initialized');
  } else {
    console.error('Progress bar element not found');
  }

  // Volume slider
  const volumeSlider = document.getElementById('volume-slider');
  if (volumeSlider) {
    volumeSlider.addEventListener('input', changeVolume);
  }

  // Shuffle button
  const shuffleButton = document.getElementById('shuffle-button');
  if (shuffleButton) {
    shuffleButton.addEventListener('click', toggleShuffle);
  }

  // Repeat button
  const repeatButton = document.getElementById('repeat-button');
  if (repeatButton) {
    repeatButton.addEventListener('click', toggleRepeat);
  }

  // Initialize audio player
  initAudioPlayer();
}

// Audio player state
let isPlaying = false;
let isShuffle = false;
let isRepeat = false;
let currentVolume = 80;
let audioPlayer = null;

function initAudioPlayer() {
  audioPlayer = document.getElementById('audio-player');

  if (audioPlayer) {
    console.log('Audio player found, initializing...');

    // Set initial volume
    audioPlayer.volume = currentVolume / 100;

    // Add event listeners
    audioPlayer.addEventListener('timeupdate', updateProgress);
    audioPlayer.addEventListener('ended', handleTrackEnd);
    audioPlayer.addEventListener('error', handleAudioError);
    audioPlayer.addEventListener('loadedmetadata', function() {
      console.log('Audio metadata loaded, duration:', audioPlayer.duration);
      updateProgress(); // Update progress bar once metadata is loaded
    });
    audioPlayer.addEventListener('canplay', function() {
      console.log('Audio can play, ready state:', audioPlayer.readyState);
      updateStatusBar('Audio ready to play', 'Connected');
    });

    // Load default audio - use the NCS music file
    try {
      // First try to load the NCS music file
      audioPlayer.src = '../audio/song1.mp3';
      audioPlayer.load(); // Explicitly load the audio

      console.log('Audio source set to:', audioPlayer.src);

      // Add a fallback to ensure we have audio
      audioPlayer.onerror = function(e) {
        console.error('Error loading audio file:', e);
        // Create a simple beep sound using the Web Audio API
        createFallbackAudio();
      };

      // Check if the audio is already loaded
      if (audioPlayer.readyState >= 2) { // HAVE_CURRENT_DATA or higher
        console.log('Audio already has data, duration:', audioPlayer.duration);
        updateProgress();
      }
    } catch (e) {
      console.error('Error initializing audio player:', e);
      updateStatusBar('Error loading audio', 'Connected');
    }

    // Update the UI to show initial state
    updateProgress();

    // Log audio player state for debugging
    console.log('Audio player initialized:', {
      src: audioPlayer.src,
      readyState: audioPlayer.readyState,
      paused: audioPlayer.paused,
      duration: audioPlayer.duration,
      currentTime: audioPlayer.currentTime,
      volume: audioPlayer.volume
    });
  } else {
    console.error('Audio player element not found');
  }
}

// Create a fallback audio source if no audio files are available
function createFallbackAudio() {
  try {
    // Check if we have the beep.js script loaded
    if (typeof window.createBeep === 'function') {
      // Create a beep with default settings (440Hz, 1 second, low volume)
      const beep = window.createBeep(440, 0.5, 0.1);

      // Store the audio context and nodes for later use
      window.fallbackAudio = {
        context: beep.context,
        oscillator: beep.oscillator,
        gainNode: beep.gainNode,
        isPlaying: true,
        createTone: function(frequency) {
          // Function to create a new tone with the specified frequency
          return window.createBeep(frequency, 0.5, 0.1);
        }
      };

      // Update status bar
      updateStatusBar('Using fallback audio', 'Connected');

      // Set play button to pause state
      document.getElementById('play-pause-button').textContent = '⏸';
      isPlaying = true;

      // After the beep ends, update the UI
      setTimeout(() => {
        if (window.fallbackAudio) {
          window.fallbackAudio.isPlaying = false;
          document.getElementById('play-pause-button').textContent = '▶';
          isPlaying = false;
        }
      }, 500); // Match the duration of the beep
    } else {
      // Fallback to the old method if beep.js is not loaded
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      const audioCtx = new AudioContext();

      // Create an oscillator for a simple tone
      const oscillator = audioCtx.createOscillator();
      const gainNode = audioCtx.createGain();

      oscillator.type = 'sine';
      oscillator.frequency.setValueAtTime(440, audioCtx.currentTime); // A4 note
      gainNode.gain.setValueAtTime(0.1, audioCtx.currentTime); // Lower volume

      oscillator.connect(gainNode);
      gainNode.connect(audioCtx.destination);

      // Store the audio context and nodes for later use
      window.fallbackAudio = {
        context: audioCtx,
        oscillator: oscillator,
        gainNode: gainNode,
        isPlaying: false
      };

      // Update status bar
      updateStatusBar('Using fallback audio (legacy mode)', 'Connected');
    }
  } catch (e) {
    console.error('Error creating fallback audio:', e);
    updateStatusBar('Audio playback not available', 'Connected');
  }
}

// Handle audio loading errors
function handleAudioError(e) {
  console.error('Audio error:', e);
  updateStatusBar('Error playing audio', 'Connected');

  // Try to use fallback audio if available
  if (window.fallbackAudio && !window.fallbackAudio.isPlaying) {
    createFallbackAudio();
  }
}

function togglePlayPause() {
  // Try to use the HTML5 audio player first
  if (audioPlayer) {
    try {
      if (isPlaying) {
        // Pause playback
        audioPlayer.pause();
        document.getElementById('play-pause-button').textContent = '▶';
        updateStatusBar('Playback paused', 'Connected');

        // Update the play button icon in the song list
        if (window.currentPlayingSongItem) {
          const playButton = window.currentPlayingSongItem.querySelector('.song-play-button');
          if (playButton) {
            playButton.textContent = '▶';
          }
        }

        // Clear the progress interval if it exists
        if (window.progressInterval) {
          clearInterval(window.progressInterval);
          window.progressInterval = null;
        }
      } else {
        // Start playback
        const playPromise = audioPlayer.play();

        // Handle the play promise to catch any autoplay restrictions
        if (playPromise !== undefined) {
          playPromise.then(() => {
            // Playback started successfully
            document.getElementById('play-pause-button').textContent = '⏸';
            updateStatusBar('Playback started', 'Connected');

            // Update the play button icon in the song list
            if (window.currentPlayingSongItem) {
              const playButton = window.currentPlayingSongItem.querySelector('.song-play-button');
              if (playButton) {
                playButton.textContent = '⏸';
              }
            }

            // Force an immediate update of the progress bar
            updateProgress();

            // Set up a timer to update the progress more frequently
            if (!window.progressInterval) {
              window.progressInterval = setInterval(() => {
                if (isPlaying && audioPlayer) {
                  updateProgress();
                } else {
                  // Clear the interval if we're not playing anymore
                  clearInterval(window.progressInterval);
                  window.progressInterval = null;
                }
              }, 100); // Update every 100ms for smoother progress
            }
          }).catch(error => {
            // Autoplay was prevented, use fallback
            console.error('Autoplay prevented:', error);
            useFallbackAudio();
          });
        }
      }

      isPlaying = !isPlaying;
    } catch (e) {
      console.error('Error toggling playback:', e);
      useFallbackAudio();
    }
  } else {
    // If HTML5 audio is not available, try to use the fallback
    useFallbackAudio();
  }
}

// Use the Web Audio API fallback if HTML5 audio fails
function useFallbackAudio(frequency = 440) {
  if (window.fallbackAudio) {
    try {
      if (window.fallbackAudio.isPlaying) {
        // If we have the createTone function from beep.js
        if (typeof window.createBeep === 'function') {
          // Just update the UI to show we're stopping
          document.getElementById('play-pause-button').textContent = '▶';
          updateStatusBar('Playback paused', 'Connected');
          window.fallbackAudio.isPlaying = false;
          isPlaying = false;
        } else {
          // Using the old method - stop the oscillator
          try {
            window.fallbackAudio.oscillator.stop();
            window.fallbackAudio.isPlaying = false;
            document.getElementById('play-pause-button').textContent = '▶';
            updateStatusBar('Playback paused', 'Connected');
            isPlaying = false;
          } catch (e) {
            console.error('Error stopping oscillator:', e);
          }
        }
      } else {
        // Start playback
        if (typeof window.createBeep === 'function') {
          // Create a new beep with the specified frequency
          window.createBeep(frequency, 2, 0.1); // 2 second duration

          // Update UI
          document.getElementById('play-pause-button').textContent = '⏸';
          updateStatusBar('Playback started (fallback audio)', 'Connected');
          window.fallbackAudio.isPlaying = true;
          isPlaying = true;

          // After the beep ends, update the UI
          setTimeout(() => {
            if (window.fallbackAudio) {
              window.fallbackAudio.isPlaying = false;
              document.getElementById('play-pause-button').textContent = '▶';
              isPlaying = false;
            }
          }, 2000); // Match the duration of the beep
        } else {
          // Using the old method - create and start a new oscillator
          try {
            const oscillator = window.fallbackAudio.context.createOscillator();
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(frequency, window.fallbackAudio.context.currentTime);
            oscillator.connect(window.fallbackAudio.gainNode);
            oscillator.start();

            // Store the new oscillator
            window.fallbackAudio.oscillator = oscillator;
            window.fallbackAudio.isPlaying = true;

            document.getElementById('play-pause-button').textContent = '⏸';
            updateStatusBar('Playback started (fallback audio)', 'Connected');
            isPlaying = true;
          } catch (e) {
            console.error('Error starting oscillator:', e);
          }
        }
      }
    } catch (e) {
      console.error('Error with fallback audio:', e);
      updateStatusBar('Audio playback failed', 'Connected');
    }
  } else {
    // Try to create fallback audio
    createFallbackAudio();
    // Try again after creating fallback
    setTimeout(() => useFallbackAudio(frequency), 100);
  }
}

function startPlayback() {
  if (audioPlayer) {
    try {
      // Start playback
      const playPromise = audioPlayer.play();

      // Handle the play promise to catch any autoplay restrictions
      if (playPromise !== undefined) {
        playPromise.then(() => {
          // Playback started successfully
          document.getElementById('play-pause-button').textContent = '⏸';
          isPlaying = true;

          // Force an immediate update of the progress bar
          updateProgress();

          // Set up a timer to update the progress more frequently
          if (!window.progressInterval) {
            window.progressInterval = setInterval(() => {
              if (isPlaying && audioPlayer) {
                updateProgress();
              } else {
                // Clear the interval if we're not playing anymore
                clearInterval(window.progressInterval);
                window.progressInterval = null;
              }
            }, 100); // Update every 100ms for smoother progress
          }
        }).catch(error => {
          // Autoplay was prevented, use fallback
          console.error('Autoplay prevented:', error);
          useFallbackAudio();
        });
      } else {
        // For browsers that don't return a promise
        document.getElementById('play-pause-button').textContent = '⏸';
        isPlaying = true;

        // Force an immediate update of the progress bar
        updateProgress();

        // Set up a timer to update the progress more frequently
        if (!window.progressInterval) {
          window.progressInterval = setInterval(() => {
            if (isPlaying && audioPlayer) {
              updateProgress();
            } else {
              // Clear the interval if we're not playing anymore
              clearInterval(window.progressInterval);
              window.progressInterval = null;
            }
          }, 100); // Update every 100ms for smoother progress
        }
      }
    } catch (e) {
      console.error('Error starting playback:', e);
      useFallbackAudio();
    }
  } else {
    // If HTML5 audio is not available, try to use the fallback
    useFallbackAudio();
  }
}

function playPrevious() {
  console.log('Playing previous track');
  updateStatusBar('Playing previous track', 'Connected');

  // In a real app, this would play the previous track
  // For demo purposes, just simulate track change
  simulateTrackChange();
}

function playNext() {
  console.log('Playing next track');
  updateStatusBar('Playing next track', 'Connected');

  // In a real app, this would play the next track
  // For demo purposes, just simulate track change
  simulateTrackChange();
}

function simulateTrackChange() {
  // Get a random song from the list
  const songs = document.querySelectorAll('.song-item');
  const randomIndex = Math.floor(Math.random() * songs.length);
  const randomSong = songs[randomIndex];

  // Play the random song
  if (randomSong) {
    const songId = randomSong.getAttribute('data-song');
    playSong(songId, randomSong);
  }
}

function updateProgress() {
  if (!audioPlayer) return;

  try {
    // Get current time and duration
    const currentTime = audioPlayer.currentTime || 0;
    const duration = audioPlayer.duration || 1; // Prevent division by zero

    // Calculate progress percentage (capped at 100%)
    const progressPercent = Math.min((currentTime / duration) * 100, 100);

    // Get the progress element
    const progressElement = document.getElementById('progress');
    if (progressElement) {
      // Update progress bar width
      progressElement.style.width = `${progressPercent}%`;
    }

    // Update time displays
    const currentTimeElement = document.getElementById('current-time');
    const totalTimeElement = document.getElementById('total-time');

    if (currentTimeElement) {
      currentTimeElement.textContent = formatTime(currentTime);
    }

    if (totalTimeElement) {
      // Only update total time if we have a valid duration
      if (!isNaN(duration) && isFinite(duration)) {
        totalTimeElement.textContent = formatTime(duration);
      }
    }

    // Log progress for debugging
    if (progressPercent % 10 < 0.5) { // Log approximately every 10%
      console.log(`Progress: ${progressPercent.toFixed(1)}%, Time: ${formatTime(currentTime)}/${formatTime(duration)}`);
    }

    // Update debug info if visible
    const debugElement = document.getElementById('audio-debug');
    if (debugElement && debugElement.style.display !== 'none') {
      updateDebugInfo();
    }
  } catch (e) {
    console.error('Error updating progress:', e);
  }
}

function formatTime(seconds) {
  // Handle invalid input
  if (isNaN(seconds) || !isFinite(seconds)) {
    return '0:00';
  }

  // Ensure seconds is a positive number
  seconds = Math.max(0, seconds);

  // Calculate minutes and seconds
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  // Format with leading zero for seconds less than 10
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
}

function seekToPosition(event) {
  // Prevent default behavior
  event.preventDefault();
  event.stopPropagation();

  if (!audioPlayer) {
    console.error('Audio player not available');
    return;
  }

  try {
    // Get the progress bar element
    const progressBar = document.querySelector('.progress-bar');
    if (!progressBar) {
      console.error('Progress bar not found');
      return;
    }

    // Calculate the click position as a percentage of the progress bar width
    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickPosition = clickX / rect.width;

    // Ensure clickPosition is between 0 and 1
    const normalizedPosition = Math.max(0, Math.min(1, clickPosition));

    // Calculate the seek time based on the audio duration
    const duration = audioPlayer.duration || 0;
    if (!duration || !isFinite(duration)) {
      console.error('Invalid audio duration:', duration);
      return;
    }

    const seekTime = normalizedPosition * duration;

    console.log(`Seeking to position: ${normalizedPosition.toFixed(2)} (${formatTime(seekTime)})`);

    // Set the current time of the audio player
    if (isFinite(seekTime)) {
      // Directly set the current time
      try {
        audioPlayer.currentTime = seekTime;
        console.log(`Successfully set currentTime to ${seekTime}`);

        // Update the progress bar immediately
        const progressElement = document.getElementById('progress');
        if (progressElement) {
          const progressPercent = (seekTime / duration) * 100;
          progressElement.style.width = `${progressPercent}%`;
        }

        // Update time displays
        const currentTimeElement = document.getElementById('current-time');
        if (currentTimeElement) {
          currentTimeElement.textContent = formatTime(seekTime);
        }

        // If not playing, start playback
        if (!isPlaying) {
          togglePlayPause();
        }

        // Update the status bar
        updateStatusBar(`Seeking to ${formatTime(seekTime)}`, 'Connected');
      } catch (seekError) {
        console.error('Error setting currentTime:', seekError);

        // Try an alternative approach if the first one fails
        setTimeout(() => {
          try {
            audioPlayer.currentTime = seekTime;
            console.log('Retry setting currentTime succeeded');
          } catch (retryError) {
            console.error('Retry setting currentTime failed:', retryError);
          }
        }, 100);
      }
    } else {
      console.error('Invalid seek time:', seekTime);
    }
  } catch (e) {
    console.error('Error in seekToPosition:', e);
  }
}

function changeVolume() {
  if (!audioPlayer) return;

  const volumeSlider = document.getElementById('volume-slider');
  currentVolume = volumeSlider.value;
  audioPlayer.volume = currentVolume / 100;

  // Update volume icon based on level
  const volumeButton = document.getElementById('volume-button');
  if (currentVolume > 50) {
    volumeButton.textContent = '🔊';
  } else if (currentVolume > 0) {
    volumeButton.textContent = '🔉';
  } else {
    volumeButton.textContent = '🔇';
  }
}

function toggleShuffle() {
  isShuffle = !isShuffle;
  const shuffleButton = document.getElementById('shuffle-button');

  if (isShuffle) {
    shuffleButton.style.color = '#0A246A';
    updateStatusBar('Shuffle mode: On', 'Connected');
  } else {
    shuffleButton.style.color = '#666666';
    updateStatusBar('Shuffle mode: Off', 'Connected');
  }
}

function toggleRepeat() {
  isRepeat = !isRepeat;
  const repeatButton = document.getElementById('repeat-button');

  if (isRepeat) {
    repeatButton.style.color = '#0A246A';
    updateStatusBar('Repeat mode: On', 'Connected');
  } else {
    repeatButton.style.color = '#666666';
    updateStatusBar('Repeat mode: Off', 'Connected');
  }
}

function handleTrackEnd() {
  if (isRepeat) {
    // Restart the current track
    audioPlayer.currentTime = 0;
    audioPlayer.play();
  } else if (isShuffle) {
    // Play a random track
    playNext();
  } else {
    // Play the next track in sequence
    playNext();
  }
}

function updateNowPlaying(title, artist, imageUrl) {
  document.getElementById('now-playing-title').textContent = title;
  document.getElementById('now-playing-artist').textContent = artist;
  document.getElementById('now-playing-img').src = imageUrl;
}

// Radio Stations Functions
function initRadioStations() {
  const radioStations = document.querySelectorAll('.radio-station');

  radioStations.forEach(station => {
    station.addEventListener('click', function() {
      const stationName = station.querySelector('.radio-name').textContent;
      const stationGenre = station.querySelector('.radio-genre').textContent;

      // Update now playing info
      updateNowPlaying(stationName, stationGenre, '../img/radio.jpg');

      // Start playback
      startPlayback();

      // Update status bar
      updateStatusBar(`Now playing radio: ${stationName}`, 'Connected');
    });
  });
}

// Browse Categories Functions
function initBrowseCategories() {
  const categoryCards = document.querySelectorAll('.category-card');

  categoryCards.forEach(card => {
    card.addEventListener('click', function() {
      const categoryName = card.querySelector('.category-name').textContent;

      // In a real app, this would load the category
      // For demo purposes, just update the status bar
      updateStatusBar(`Browsing ${categoryName} music`, 'Connected');
    });
  });
}

// Debug Functions
// Show potential seek position when hovering over progress bar
function showSeekPosition(event) {
  if (!audioPlayer) return;

  try {
    // Get the progress bar element
    const progressBar = document.querySelector('.progress-bar');
    if (!progressBar) return;

    // Calculate the hover position as a percentage of the progress bar width
    const rect = progressBar.getBoundingClientRect();
    const hoverX = event.clientX - rect.left;
    const hoverPosition = hoverX / rect.width;

    // Ensure hoverPosition is between 0 and 1
    const normalizedPosition = Math.max(0, Math.min(1, hoverPosition));

    // Calculate the potential seek time
    const duration = audioPlayer.duration || 0;
    const seekTime = normalizedPosition * duration;

    // Create or update the hover indicator
    let hoverIndicator = document.getElementById('hover-indicator');
    if (!hoverIndicator) {
      hoverIndicator = document.createElement('div');
      hoverIndicator.id = 'hover-indicator';
      hoverIndicator.style.position = 'absolute';
      hoverIndicator.style.top = '0';
      hoverIndicator.style.height = '100%';
      hoverIndicator.style.width = '2px';
      hoverIndicator.style.backgroundColor = '#FFFFFF';
      hoverIndicator.style.zIndex = '10';
      hoverIndicator.style.pointerEvents = 'none'; // Ensure it doesn't interfere with clicks
      progressBar.appendChild(hoverIndicator);

      // Create time tooltip
      const timeTooltip = document.createElement('div');
      timeTooltip.id = 'time-tooltip';
      timeTooltip.style.position = 'absolute';
      timeTooltip.style.bottom = '20px';
      timeTooltip.style.backgroundColor = '#0A246A';
      timeTooltip.style.color = '#FFFFFF';
      timeTooltip.style.padding = '2px 5px';
      timeTooltip.style.borderRadius = '3px';
      timeTooltip.style.fontSize = '10px';
      timeTooltip.style.transform = 'translateX(-50%)';
      timeTooltip.style.pointerEvents = 'none'; // Ensure it doesn't interfere with clicks
      hoverIndicator.appendChild(timeTooltip);
    }

    // Update the hover indicator position
    hoverIndicator.style.left = `${normalizedPosition * 100}%`;

    // Update the time tooltip
    const timeTooltip = document.getElementById('time-tooltip');
    if (timeTooltip) {
      timeTooltip.textContent = formatTime(seekTime);
    }
  } catch (e) {
    console.error('Error showing seek position:', e);
  }
}

// Hide the seek position indicator when mouse leaves the progress bar
function hideSeekPosition() {
  const hoverIndicator = document.getElementById('hover-indicator');
  if (hoverIndicator) {
    hoverIndicator.remove();
  }
}

function updateDebugInfo() {
  const debugElement = document.getElementById('audio-debug');
  if (!debugElement || debugElement.style.display === 'none') return;

  let debugInfo = '';

  // Audio player info
  if (audioPlayer) {
    debugInfo += `<strong>Audio Player:</strong><br>`;
    debugInfo += `Source: ${audioPlayer.src}<br>`;
    debugInfo += `Current Time: ${formatTime(audioPlayer.currentTime)}<br>`;
    debugInfo += `Duration: ${formatTime(audioPlayer.duration)}<br>`;
    debugInfo += `Paused: ${audioPlayer.paused}<br>`;
    debugInfo += `Volume: ${audioPlayer.volume}<br>`;
    debugInfo += `Ready State: ${audioPlayer.readyState}<br>`;
    debugInfo += `Network State: ${audioPlayer.networkState}<br>`;
    debugInfo += `Error: ${audioPlayer.error ? audioPlayer.error.code : 'None'}<br>`;
  } else {
    debugInfo += `<strong>Audio Player:</strong> Not available<br>`;
  }

  // Playback state
  debugInfo += `<br><strong>Playback State:</strong><br>`;
  debugInfo += `Is Playing: ${isPlaying}<br>`;
  debugInfo += `Is Shuffle: ${isShuffle}<br>`;
  debugInfo += `Is Repeat: ${isRepeat}<br>`;
  debugInfo += `Current Volume: ${currentVolume}<br>`;
  debugInfo += `Progress Interval: ${window.progressInterval ? 'Active' : 'Inactive'}<br>`;

  // Fallback audio info
  if (window.fallbackAudio) {
    debugInfo += `<br><strong>Fallback Audio:</strong><br>`;
    debugInfo += `Is Playing: ${window.fallbackAudio.isPlaying}<br>`;
    debugInfo += `Has Create Tone: ${typeof window.fallbackAudio.createTone === 'function'}<br>`;
  } else {
    debugInfo += `<br><strong>Fallback Audio:</strong> Not initialized<br>`;
  }

  // Update the debug element
  debugElement.innerHTML = debugInfo;

  // Schedule the next update
  setTimeout(updateDebugInfo, 500);
}
