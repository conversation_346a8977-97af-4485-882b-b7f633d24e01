/* Shopping Page Styles */
.shopping-page {
  font-family: '<PERSON><PERSON><PERSON>', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 30px;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.shopping-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  margin-bottom: 25px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.shopping-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.cart-container {
  position: relative;
}

.cart-icon {
  font-size: 20px;
  cursor: pointer;
  padding: 5px 10px;
  background: linear-gradient(to bottom, #FFFFFF, #F1EFE2);
  border: 1px solid #ACA899;
  border-radius: 3px;
  color: #0A246A;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background 0.2s ease;
}

.cart-icon:hover {
  background: linear-gradient(to bottom, #F1EFE2, #FFFFFF);
}

.cart-count {
  background-color: #CC0000;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
}

.shopping-content {
  display: flex;
  gap: 25px;
  flex: 1;
  overflow: hidden;
}

/* Categories Navigation */
.categories-nav {
  width: 200px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  max-height: 500px;
  overflow-y: auto;
}

.categories-nav h2 {
  font-size: 16px;
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #ACA899;
  color: #0A246A;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item {
  padding: 8px 10px;
  cursor: pointer;
  border-radius: 3px;
  margin-bottom: 5px;
  transition: background 0.2s ease;
}

.category-item:hover {
  background-color: #E3EFFF;
}

.category-item.active {
  background-color: #316AC5;
  color: white;
  font-weight: bold;
}

/* Products Container */
.products-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ACA899;
}

.products-header h2 {
  font-size: 18px;
  margin: 0;
  color: #0A246A;
}

.sort-options {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sort-select {
  padding: 5px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  background-color: #FFFFFF;
  font-size: 12px;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  overflow-y: auto;
  padding-right: 10px;
}

.product-card {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-image {
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  overflow: hidden;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #0A246A;
}

.product-price {
  font-size: 16px;
  font-weight: bold;
  color: #CC0000;
  margin-bottom: 10px;
}

.product-rating {
  color: #F4B400;
  margin-bottom: 15px;
  font-size: 14px;
}

.add-to-cart {
  padding: 8px 15px;
  background: linear-gradient(to bottom, #E5F1FB, #C2E0FF);
  border: 1px solid #0A246A;
  border-radius: 3px;
  color: #0A246A;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s ease;
}

.add-to-cart:hover {
  background: linear-gradient(to bottom, #C2E0FF, #A6CAF0);
}

/* Cart Popup */
.cart-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  max-width: 90%;
  background-color: #FFFFFF;
  border: 1px solid #0A246A;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1001;
  display: none;
  flex-direction: column;
  max-height: 80vh;
}

.cart-popup-header {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 10px 15px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 5px 5px 0 0;
}

.cart-popup-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-cart {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.cart-items {
  padding: 15px;
  overflow-y: auto;
  max-height: 300px;
}

.empty-cart-message {
  text-align: center;
  padding: 20px;
  color: #666666;
  font-style: italic;
}

.cart-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ACA899;
}

.cart-item-image {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cart-item-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.cart-item-details {
  flex: 1;
}

.cart-item-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}

.cart-item-price {
  font-size: 14px;
  color: #CC0000;
  margin-bottom: 5px;
}

.cart-item-quantity {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quantity-btn {
  width: 24px;
  height: 24px;
  background: linear-gradient(to bottom, #FFFFFF, #F1EFE2);
  border: 1px solid #ACA899;
  border-radius: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.quantity-btn:hover {
  background: linear-gradient(to bottom, #F1EFE2, #FFFFFF);
}

.cart-item-remove {
  color: #CC0000;
  cursor: pointer;
  margin-left: auto;
  font-size: 18px;
}

.cart-summary {
  padding: 15px;
  background-color: #F1EFE2;
  border-top: 1px solid #ACA899;
  border-radius: 0 0 5px 5px;
}

.cart-total {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.checkout-button {
  width: 100%;
  padding: 10px;
  background: linear-gradient(to bottom, #0A246A, #3A6EA5);
  color: white;
  border: 1px solid #0A246A;
  border-radius: 3px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s ease;
}

.checkout-button:hover {
  background: linear-gradient(to bottom, #3A6EA5, #0A246A);
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .shopping-content {
    flex-direction: column;
  }

  .categories-nav {
    width: 100%;
    margin-bottom: 20px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 480px) {
  .shopping-page {
    padding: 15px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 10px;
  }

  .product-image {
    height: 120px;
  }
}
