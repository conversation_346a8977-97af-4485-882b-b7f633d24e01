<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Page Not Found - Bingo</title>
  <link rel="stylesheet" href="../css/styles.css">
  <style>
    .error-page {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;
      padding: 2rem;
    }
    
    .error-code {
      font-size: 8rem;
      font-weight: bold;
      color: #4285F4;
      margin-bottom: 1rem;
    }
    
    .error-message {
      font-size: 2rem;
      margin-bottom: 2rem;
    }
    
    .error-description {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      max-width: 600px;
    }
    
    .home-button {
      background-color: #4285F4;
      color: white;
      border: none;
      padding: 0.8rem 1.5rem;
      font-size: 1.1rem;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .home-button:hover {
      background-color: #3367D6;
    }
  </style>
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="error-page">
        <div class="error-code">404</div>
        <div class="error-message">Page Not Found</div>
        <div class="error-description">
          The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </div>
        <button class="home-button" onclick="window.location.href='index.html'">Go to Homepage</button>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
</body>
</html>
