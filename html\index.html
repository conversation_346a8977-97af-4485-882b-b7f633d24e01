<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bingo - Search the Web</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/home.css">
</head>
<body>
  <div class="browser-container">
    <!-- Browser Chrome (Menu Bar, Toolbar, Bookmark Bar) -->
    <div id="browser-chrome-container">
      <!-- This will be populated by browser-chrome-inline.js -->
    </div>

    <!-- Browser Content -->
    <div class="browser-content">
      <div class="search-engine">
        <div class="search-logo">
          <svg width="272" height="92" viewBox="0 0 272 92" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <!-- Filter for just the Drop Shadow effect -->
              <filter id="google-style-filter-shadow-only" x="-20%" y="-20%" width="140%" height="140%">
                <!-- Drop Shadow part -->
                <feGaussianBlur in="SourceAlpha" stdDeviation="2" result="blur"/>
                <feOffset in="blur" dx="2" dy="2" result="offsetBlur"/>
                <feFlood flood-color="#333333" flood-opacity="0.5" result="flood"/>
                <feComposite in="flood" in2="offsetBlur" operator="in" result="shadow"/>
                <!-- Merge the Shadow and the Original Graphic -->
                <feMerge>
                  <feMergeNode in="shadow"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
              <!-- Define Colors and Style -->
              <style type="text/css">
                .google-font {
                  font-family: 'Times New Roman', Times, serif; /* Catull fallback */
                  font-size: 65px;
                  font-weight: bold;
                  letter-spacing: -3px; /* Adjusted to bring letters closer but still readable */
                }
                .b { fill: #4285F4; } /* Blue */
                .i { fill: #DB4437; } /* Red */
                .n { fill: #F4B400; } /* Yellow */
                .g { fill: #0F9D58; } /* Green */
                .o { fill: #4285F4; } /* Blue */
              </style>
            </defs>
            <!-- Apply filter to the text -->
            <text x="25" y="65" class="google-font" filter="url(#google-style-filter-shadow-only)">
              <tspan class="b">B</tspan>
              <tspan class="i">i</tspan>
              <tspan class="n">n</tspan>
              <tspan class="g">g</tspan>
              <tspan class="o">o</tspan>
            </text>
          </svg>
        </div>

        <form class="xp-search-bar">
          <div class="xp-search-icon">🔍</div>
          <input type="text" class="xp-search-input" placeholder="Search the web...">
          <button type="submit" class="xp-search-button">Search</button>
        </form>

        <div class="quick-links">
          <h2>Quick Links</h2>
          <div class="quick-links-container">
            <div class="quick-link" data-url="weather.html">
              <div class="quick-link-icon">🌤️</div>
              <div class="quick-link-text">Weather</div>
            </div>
            <div class="quick-link" data-url="maps.html">
              <div class="quick-link-icon">🗺️</div>
              <div class="quick-link-text">Maps</div>
            </div>
            <div class="quick-link" data-url="calendar.html">
              <div class="quick-link-icon">📅</div>
              <div class="quick-link-text">Calendar</div>
            </div>
            <div class="quick-link" data-url="games.html">
              <div class="quick-link-icon">🎮</div>
              <div class="quick-link-text">Games</div>
            </div>
            <div class="quick-link" data-url="shopping.html">
              <div class="quick-link-icon">🛒</div>
              <div class="quick-link-text">Shopping</div>
            </div>
          </div>
        </div>

        <div class="news-section">
          <h2>Today's Top News</h2>
          <div class="news-grid">
            <div class="news-item">
              <h3>Political News</h3>
              <h4>New Policy Announced</h4>
              <p>Latest updates from the political landscape...</p>
              <button class="read-more">Read More</button>
            </div>
            <div class="news-item">
              <h3>Sports News</h3>
              <h4>Championship Results</h4>
              <p>Latest scores and highlights from the world of sports...</p>
              <button class="read-more">Read More</button>
            </div>
            <div class="news-item">
              <h3>Celebrity News</h3>
              <h4>Star Couple Announcement</h4>
              <p>The latest gossip from Hollywood and beyond...</p>
              <button class="read-more">Read More</button>
            </div>
            <div class="news-item">
              <h3>Technology News</h3>
              <h4>New Gadget Released</h4>
              <p>The latest innovations and tech updates...</p>
              <button class="read-more">Read More</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar-container">
      <!-- This will be populated by status-bar-inline.js -->
    </div>
  </div>

  <script src="../js/browser.js"></script>
  <script src="../js/browser-chrome-inline.js"></script>
  <script src="../js/status-bar-inline.js"></script>
  <script src="../js/home.js"></script>
</body>
</html>
