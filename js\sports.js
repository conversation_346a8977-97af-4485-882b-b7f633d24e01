// Sports page functionality
document.addEventListener('DOMContentLoaded', function() {
  console.log('Sports page loaded');
  // Initialize sports page
  initSportsPage();
});

function initSportsPage() {
  // Initialize sports categories
  initSportsCategories();

  // Initialize stats tabs
  initStatsTabs();

  // Initialize match cards
  initMatchCards();

  // Initialize news items
  initNewsItems();

  // Set date to 2010 (as per user preference)
  updateDatesToYear2010();

  // Filter content for the default sport (cricket)
  filterContentBySport('cricket');

  // Update status bar
  updateStatusBar('Sports page loaded successfully', 'Connected');
}

// Sports Categories Functions
function initSportsCategories() {
  const categoryItems = document.querySelectorAll('.category-item');

  categoryItems.forEach(item => {
    item.addEventListener('click', function() {
      // Remove active class from all items
      categoryItems.forEach(i => i.classList.remove('active'));

      // Add active class to clicked item
      item.classList.add('active');

      // Get the sport type
      const sportType = item.getAttribute('data-category');

      // Update status bar
      updateStatusBar('Loading ' + sportType + ' content...', 'Connected');

      // Filter content based on selected sport
      filterContentBySport(sportType);

      // Update status bar after a short delay
      setTimeout(() => {
        updateStatusBar(sportType + ' content loaded', 'Connected');
      }, 300);
    });
  });
}

function filterContentBySport(sportType) {
  // Hide all sports sections
  const sportsSections = document.querySelectorAll('.sports-section');
  sportsSections.forEach(section => {
    section.classList.remove('active');
  });

  // Show the selected sport section
  const selectedSection = document.getElementById(sportType + '-section');
  if (selectedSection) {
    selectedSection.classList.add('active');
  }

  console.log('Filtering content for sport:', sportType);
}

// Stats Tabs Functions
function initStatsTabs() {
  const statsTabs = document.querySelectorAll('.stats-tab');

  statsTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // Remove active class from all tabs
      statsTabs.forEach(t => t.classList.remove('active'));

      // Add active class to clicked tab
      tab.classList.add('active');

      // Get the stat type
      const statType = tab.getAttribute('data-stat');

      // Hide all stats content
      const statsContents = document.querySelectorAll('.stats-content');
      statsContents.forEach(content => {
        content.classList.remove('active');
      });

      // Show the selected stats content
      const selectedContent = document.getElementById(statType + '-stats');
      if (selectedContent) {
        selectedContent.classList.add('active');
      }

      // Update status bar
      updateStatusBar('Showing ' + statType + ' statistics', 'Connected');
    });
  });
}

// Match Cards Functions
function initMatchCards() {
  const matchCards = document.querySelectorAll('.match-card');

  matchCards.forEach(card => {
    card.addEventListener('click', function() {
      // Get match details
      const matchTournament = card.querySelector('.match-tournament').textContent;
      const team1Name = card.querySelector('.team:first-child .team-name').textContent;
      const team2Name = card.querySelector('.team:last-child .team-name').textContent;

      // Update status bar
      updateStatusBar('Viewing match details: ' + team1Name + ' vs ' + team2Name, 'Connected');

      // In a real application, this would open a detailed match view
      console.log('Match card clicked:', matchTournament, team1Name, 'vs', team2Name);
    });
  });
}

// News Items Functions
function initNewsItems() {
  const newsItems = document.querySelectorAll('.news-item');
  const readMoreLinks = document.querySelectorAll('.read-more');

  newsItems.forEach(item => {
    item.addEventListener('click', function() {
      // Get news title
      const newsTitle = item.querySelector('h4').textContent;

      // Update status bar
      updateStatusBar('Reading news: ' + newsTitle, 'Connected');

      // In a real application, this would open the full news article
      console.log('News item clicked:', newsTitle);
    });
  });

  // Prevent event bubbling for read more links
  readMoreLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.stopPropagation();
      const newsTitle = this.closest('.news-item').querySelector('h4').textContent;
      updateStatusBar('Opening full article: ' + newsTitle, 'Connected');
    });
  });
}

// Update dates to show 2010 instead of current year
function updateDatesToYear2010() {
  // Get all date elements that might contain years
  const dateElements = document.querySelectorAll('.match-time, .match-venue, .match-result');

  dateElements.forEach(element => {
    // Replace any year with 2010
    const text = element.textContent;
    const updatedText = text.replace(/\b20\d\d\b/g, '2010');
    element.textContent = updatedText;
  });
}
