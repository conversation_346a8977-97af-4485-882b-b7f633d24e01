/* Bingopedia Page Styles */
.bingopedia-page {
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 0;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.bingopedia-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bingopedia-header-bar h1 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.bingopedia-subtitle {
  font-size: 14px;
  font-style: italic;
  margin-top: 5px;
}

.bingopedia-content {
  display: flex;
  flex: 1;
  min-height: 0; /* Allows content to scroll properly */
  overflow: hidden;
}

/* Sidebar Styles */
.bingopedia-sidebar {
  width: 230px; /* Increased width to accommodate search button */
  background-color: #F1EFE2;
  border-right: 1px solid #ACA899;
  padding: 15px;
  padding-right: 20px; /* Extra padding on the right side */
  overflow-y: auto;
}

.bingopedia-logo {
  text-align: center;
  margin-bottom: 20px;
}

.bingopedia-logo img {
  width: 120px;
  height: auto;
}

.sidebar-section {
  margin-bottom: 20px;
}

.sidebar-section h3 {
  font-size: 14px;
  margin: 0 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #ACA899;
  color: #0A246A;
}

.sidebar-links {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.sidebar-links li {
  margin-bottom: 5px;
}

.sidebar-links a {
  color: #0000FF;
  text-decoration: none;
  font-size: 12px;
  display: block;
  padding: 3px 0;
}

.sidebar-links a:hover {
  text-decoration: underline;
}

.sidebar-search {
  display: flex;
  margin-top: 5px;
  width: 85%; /* Reduced width to prevent overflow */
}

.sidebar-search input {
  flex: 1; /* Take available space */
  padding: 4px;
  border: 1px solid #7F9DB9;
  font-size: 12px;
  max-width: 120px; /* Limit input width */
}

.sidebar-search button {
  background-color: #ECE9D8;
  border: 1px solid #ACA899;
  border-left: none;
  padding: 0 8px;
  cursor: pointer;
  white-space: nowrap; /* Prevent text wrapping */
  overflow: visible; /* Ensure button content is not clipped */
  /* Button width is now controlled by inline style */
}

.sidebar-search button:hover {
  background-color: #F1EFE2;
}

/* Main Content Styles */
.bingopedia-main {
  flex: 1;
  padding: 15px;
  background-color: white;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.article-header {
  border-bottom: 1px solid #ACA899;
  margin-bottom: 15px;
  padding-bottom: 5px;
}

.article-header h1 {
  font-size: 22px;
  margin: 0 0 10px 0;
  color: #0A246A;
}

.article-tabs {
  display: flex;
  margin-bottom: -1px;
}

.tab {
  padding: 5px 10px;
  margin-right: 5px;
  border: 1px solid #ACA899;
  border-bottom: none;
  background-color: #F1EFE2;
  cursor: pointer;
  font-size: 12px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.tab.active {
  background-color: white;
  border-bottom: 1px solid white;
  font-weight: bold;
}

.article-content {
  flex: 1;
  line-height: 1.5;
  font-size: 13px;
}

.article-content h2 {
  font-size: 18px;
  margin: 20px 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #ACA899;
  color: #0A246A;
}

.article-content h3 {
  font-size: 16px;
  margin: 15px 0 10px 0;
  color: #0A246A;
}

.article-content p {
  margin: 0 0 10px 0;
}

.article-content ul, .article-content ol {
  margin: 10px 0;
  padding-left: 30px;
}

.article-content li {
  margin-bottom: 5px;
}

.article-content a {
  color: #0000FF;
  text-decoration: none;
}

.article-content a:hover {
  text-decoration: underline;
}

.article-content a:visited {
  color: #800080;
}

.article-content img {
  max-width: 100%;
  height: auto;
  border: 1px solid #ACA899;
  padding: 3px;
  background-color: #F1EFE2;
}

.article-content blockquote {
  margin: 10px 0;
  padding: 10px;
  background-color: #F1EFE2;
  border-left: 3px solid #ACA899;
}

.article-content code {
  font-family: 'Courier New', monospace;
  background-color: #F1EFE2;
  padding: 2px 4px;
  border-radius: 3px;
}

.article-content pre {
  background-color: #F1EFE2;
  padding: 10px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  overflow-x: auto;
}

.article-content pre code {
  background-color: transparent;
  padding: 0;
}

.article-footer {
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #ACA899;
  font-size: 12px;
}

.article-categories {
  margin-bottom: 10px;
}

.article-references {
  border-top: 1px solid #ACA899;
  padding-top: 10px;
}

/* Featured Articles */
.featured-articles {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.featured-article {
  border: 1px solid #ACA899;
  padding: 10px;
  background-color: #F1EFE2;
  cursor: pointer;
}

.featured-article:hover {
  background-color: #E6E4D7;
}

.featured-article h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #0000FF;
}

.featured-article p {
  margin: 0;
  font-size: 12px;
}

/* Welcome Message */
.welcome-message {
  padding: 10px;
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  margin-bottom: 20px;
}

/* Loading Indicator */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.spinner {
  font-size: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Documentation Styles */
.documentation {
  padding: 10px;
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
}

.documentation h2 {
  color: #0A246A;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.documentation code {
  background-color: #E6E4D7;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.documentation pre {
  background-color: #E6E4D7;
  padding: 10px;
  border: 1px solid #ACA899;
  border-radius: 3px;
  overflow-x: auto;
  margin: 10px 0;
}

/* All Articles List */
.all-articles-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.all-articles-list li {
  margin-bottom: 5px;
  padding: 5px;
  border-bottom: 1px solid #E6E4D7;
}

.all-articles-list a {
  color: #0000FF;
  text-decoration: none;
}

.all-articles-list a:hover {
  text-decoration: underline;
}

/* Article Popup Styles */
.article-popup {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  overflow: auto;
}

.article-popup.show {
  display: block;
  animation: fadeIn 0.3s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.article-popup-content {
  position: relative;
  background-color: #ECE9D8;
  margin: 50px auto;
  padding: 0;
  width: 80%;
  max-width: 900px;
  border: 1px solid #0A246A;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  border-radius: 3px;
}

.article-popup-header {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 10px 15px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #0A246A;
}

.article-popup-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.popup-close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0 5px;
  line-height: 1;
}

.popup-close-button:hover {
  color: #F1EFE2;
}

.article-popup-body {
  padding: 15px;
  background-color: white;
  overflow-y: auto;
  flex: 1;
  min-height: 200px;
  max-height: calc(80vh - 100px);
  font-family: 'Tahoma', 'Microsoft Sans Serif', 'Arial', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  scroll-behavior: smooth; /* Add smooth scrolling */
}

.article-popup-body h1 {
  font-size: 24px;
  color: #0A246A;
  margin-top: 0;
  margin-bottom: 15px;
}

.article-popup-body h2 {
  font-size: 20px;
  color: #0A246A;
  margin-top: 20px;
  margin-bottom: 10px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 5px;
}

.article-popup-body h3 {
  font-size: 18px;
  color: #0A246A;
  margin-top: 15px;
  margin-bottom: 10px;
}

.article-popup-body p {
  margin-bottom: 12px;
}

.article-popup-body ul,
.article-popup-body ol {
  margin-bottom: 15px;
  padding-left: 25px;
}

.article-popup-body li {
  margin-bottom: 8px;
}

.article-popup-body a {
  color: #0000FF;
  text-decoration: none;
}

.article-popup-body a:hover {
  text-decoration: underline;
}

.article-popup-body blockquote {
  background-color: #F1EFE2;
  border-left: 3px solid #ACA899;
  padding: 10px 15px;
  margin: 15px 0;
}

.article-popup-footer {
  padding: 10px 15px;
  background-color: #F1EFE2;
  border-top: 1px solid #ACA899;
}

.popup-article-categories {
  font-size: 12px;
}
