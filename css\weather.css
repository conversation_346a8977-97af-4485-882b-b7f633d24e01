/* Weather Page Styles */
.weather-page {
  font-family: '<PERSON><PERSON><PERSON>', 'Microsoft Sans Serif', 'Arial', sans-serif;
  padding: 30px;
  background-color: #ECE9D8;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-height: 0; /* Allows children to shrink below content size */
}

.weather-header-bar {
  background: linear-gradient(to right, #0A246A, #3A6EA5);
  padding: 15px 20px;
  border: 1px solid #0A246A;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 25px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.weather-header-bar h1 {
  margin: 0;
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.weather-content {
  display: flex;
  flex: 1;
  gap: 25px;
  min-height: 0; /* Ensures flex items can shrink below their content size */
  height: 100%; /* Fill available height */
}

.popular-cities {
  width: 200px;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.popular-cities h3 {
  margin: 0;
  padding: 10px 15px;
  font-size: 16px;
  background: linear-gradient(to bottom, #0A246A, #3A6EA5);
  color: white;
  border-bottom: 1px solid #ACA899;
}

.city-list {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  flex-grow: 1;
  scrollbar-width: thin;
  scrollbar-color: #ACA899 #F1EFE2;
}

/* Custom scrollbar for WebKit browsers */
.city-list::-webkit-scrollbar {
  width: 8px;
}

.city-list::-webkit-scrollbar-track {
  background: #F1EFE2;
  border-radius: 4px;
}

.city-list::-webkit-scrollbar-thumb {
  background-color: #ACA899;
  border-radius: 4px;
  border: 2px solid #F1EFE2;
}

.city-list::-webkit-scrollbar-thumb:hover {
  background-color: #7F9DB9;
}

.city-item {
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  color: #333333;
}

.city-item:hover {
  background-color: #F0F0F0;
  border-color: #ACA899;
}

.city-item.active {
  background-color: #E1E1E1;
  border: 1px solid #7F9DB9;
  color: #0A246A;
  font-weight: bold;
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);
}

.weather-display {
  flex: 1;
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 40px;
}

.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3A6EA5;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
  margin-bottom: 10px;
}

.error-message {
  color: #cc0000;
  text-align: center;
  padding: 15px;
  background-color: #ffeeee;
  border: 1px solid #ffcccc;
  border-radius: 5px;
  margin: 10px 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 40px;
}

.error-message {
  color: #CC0000;
  margin-bottom: 15px;
  font-size: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  color: #666666;
  font-size: 16px;
  padding: 40px;
}

.weather-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.weather-header {
  display: flex;
  padding: 15px 20px;
  background: linear-gradient(to bottom, #F0F0F0, #E1E1E1);
  border-bottom: 1px solid #ACA899;
  align-items: center;
}

.weather-icon {
  font-size: 50px;
  margin-right: 15px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.weather-info {
  flex: 1;
}

.weather-info h2 {
  margin: 0 0 5px 0;
  font-size: 22px;
  color: #0A246A;
  letter-spacing: 0.5px;
}

.date {
  margin: 0;
  font-size: 12px;
  color: #666666;
}

.weather-main {
  display: flex;
  padding: 15px 20px;
  background-color: #F9F9F9;
  border-bottom: 1px solid #ACA899;
  align-items: center;
  justify-content: space-between;
}

.temperature {
  display: flex;
  align-items: center;
}

.temp-value {
  font-size: 42px;
  font-weight: bold;
  color: #0A246A;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.condition-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.condition {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.unit-toggle {
  background: linear-gradient(to bottom, #FFFFFF, #E1E1E1);
  border: 1px solid #7F9DB9;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  color: #0A246A;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.unit-toggle:hover {
  background: linear-gradient(to bottom, #F0F0F0, #D0D0D0);
}

.unit-toggle:active {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
  transform: translateY(1px);
}

.weather-details {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
}

.weather-stats {
  display: flex;
  gap: 10px;
  width: 100%;
  flex-wrap: wrap;
}

.stat {
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  padding: 8px;
  font-size: 12px;
  flex: 1;
  min-width: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.stat:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.stat-label {
  font-weight: bold;
  color: #0A246A;
  font-size: 12px;
}

.stat-value {
  color: #333333;
  font-size: 16px;
  font-weight: bold;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.detail-icon {
  font-size: 20px;
  margin-right: 15px;
  color: #0A246A;
}

.detail-info {
  flex: 1;
}

.detail-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 5px;
}

.detail-value {
  font-size: 16px;
  font-weight: bold;
  color: #0A246A;
}

.forecast-section {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 12px 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.forecast-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #0A246A;
  font-size: 16px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 6px;
}

.forecast-container {
  display: flex;
  justify-content: space-between;
  gap: 5px;
}

.forecast-day {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #F1EFE2;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 8px;
  transition: all 0.2s ease;
}

.forecast-day:hover {
  background-color: #E1E1E1;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.forecast-date {
  font-weight: bold;
  color: #0A246A;
  margin-bottom: 2px;
  font-size: 14px;
}

.forecast-icon {
  font-size: 24px;
  margin: 3px 0;
}

.forecast-temp {
  font-weight: bold;
  font-size: 16px;
  margin: 3px 0;
}

.forecast-condition {
  font-size: 12px;
  color: #666666;
  text-align: center;
}

.city-description {
  background-color: #FFFFFF;
  border: 1px solid #ACA899;
  border-radius: 5px;
  padding: 10px 15px;
  margin-top: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.city-description h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #0A246A;
  font-size: 16px;
  border-bottom: 1px solid #ACA899;
  padding-bottom: 6px;
}

.city-description p {
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  color: #333333;
  text-align: justify;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .weather-content {
    flex-direction: column;
  }

  .popular-cities {
    width: 100%;
    margin-right: 0;
    margin-bottom: 15px;
    height: 350px; /* Fixed height for mobile view */
  }

  .city-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
    overflow-y: auto;
    max-height: 300px; /* Increased to fill the container */
  }

  .forecast-items {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .city-list {
    grid-template-columns: repeat(2, 1fr);
  }

  .weather-main {
    flex-direction: column;
    align-items: flex-start;
  }

  .temperature {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .weather-details {
    grid-template-columns: 1fr;
  }

  .forecast-items {
    grid-template-columns: repeat(2, 1fr);
  }
}
