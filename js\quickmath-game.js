// Quick Math Game functionality
let qmCurrentProblem = '';
let qmCurrentAnswer = 0;
let qmScore = 0;
let qmStreak = 0;
let qmTimer = 30;
let qmTimerInterval;
let qmGameRunning = false;
let qmGameOver = false;
let qmDifficulty = 'easy'; // Default difficulty

// Initialize the Quick Math game
function initQuickMathGame() {
  // Get DOM elements
  const mathAnswerInput = document.getElementById('math-answer');
  const submitAnswerButton = document.getElementById('submit-answer');
  const restartButton = document.getElementById('restart-quickmath-game');
  
  // Add event listeners
  if (submitAnswerButton) {
    submitAnswerButton.addEventListener('click', checkAnswer);
  }
  
  if (restartButton) {
    restartButton.addEventListener('click', restartQuickMathGame);
  }
  
  if (mathAnswerInput) {
    mathAnswerInput.addEventListener('keypress', function(event) {
      if (event.key === 'Enter') {
        checkAnswer();
      }
    });
  }
  
  // Initialize difficulty buttons
  initQuickMathDifficultyButtons();
  
  // Reset game state
  resetQuickMathGame();
}

// Initialize difficulty buttons
function initQuickMathDifficultyButtons() {
  const easyButton = document.getElementById('qm-easy-mode');
  const mediumButton = document.getElementById('qm-medium-mode');
  const hardButton = document.getElementById('qm-hard-mode');
  const expertButton = document.getElementById('qm-expert-mode');
  
  // Function to set active button
  function setActiveButton(button) {
    // Remove active class from all buttons
    document.querySelectorAll('.difficulty-button').forEach(btn => {
      btn.classList.remove('active');
    });
    
    // Add active class to selected button
    button.classList.add('active');
  }
  
  // Add event listeners
  if (easyButton) {
    easyButton.addEventListener('click', function() {
      setActiveButton(this);
      setDifficulty('easy');
    });
  }
  
  if (mediumButton) {
    mediumButton.addEventListener('click', function() {
      setActiveButton(this);
      setDifficulty('medium');
    });
  }
  
  if (hardButton) {
    hardButton.addEventListener('click', function() {
      setActiveButton(this);
      setDifficulty('hard');
    });
  }
  
  if (expertButton) {
    expertButton.addEventListener('click', function() {
      setActiveButton(this);
      setDifficulty('expert');
    });
  }
}

// Set difficulty level
function setDifficulty(difficulty) {
  // Always allow changing difficulty and restart the game
  qmDifficulty = difficulty;
  
  // Stop current game if running
  if (qmGameRunning) {
    pauseQuickMathGame();
  }
  
  // Reset and restart the game
  resetQuickMathGame();
  
  // Start the game with the new difficulty
  startQuickMathGame();
}

// Start the Quick Math game
function startQuickMathGame() {
  if (!qmGameRunning && !qmGameOver) {
    qmGameRunning = true;
    
    // Start timer
    startQuickMathTimer();
    
    // Generate first problem
    generateProblem();
    
    // Enable input
    const mathAnswerInput = document.getElementById('math-answer');
    if (mathAnswerInput) {
      mathAnswerInput.disabled = false;
      mathAnswerInput.value = '';
      mathAnswerInput.focus();
    }
  } else if (qmGameOver) {
    restartQuickMathGame();
  }
}

// Pause the Quick Math game
function pauseQuickMathGame() {
  qmGameRunning = false;
  clearInterval(qmTimerInterval);
}

// Restart the Quick Math game
function restartQuickMathGame() {
  pauseQuickMathGame();
  resetQuickMathGame();
  startQuickMathGame();
}

// Reset the game state
function resetQuickMathGame() {
  qmScore = 0;
  qmStreak = 0;
  qmGameRunning = false;
  qmGameOver = false;
  qmCurrentProblem = '';
  qmCurrentAnswer = 0;
  
  // Set timer based on difficulty
  switch (qmDifficulty) {
    case 'easy':
      qmTimer = 30;
      break;
    case 'medium':
      qmTimer = 45;
      break;
    case 'hard':
      qmTimer = 60;
      break;
    case 'expert':
      qmTimer = 90;
      break;
    default:
      qmTimer = 30;
  }
  
  // Update displays
  updateQuickMathScoreDisplay();
  updateQuickMathStreakDisplay();
  updateQuickMathTimerDisplay();
  
  // Clear problem display
  document.getElementById('math-problem').textContent = '';
  document.getElementById('math-result').textContent = '';
  
  // Clear input
  const mathAnswerInput = document.getElementById('math-answer');
  if (mathAnswerInput) {
    mathAnswerInput.value = '';
    mathAnswerInput.disabled = true;
  }
  
  // Update active difficulty button
  updateActiveDifficultyButton();
}

// Update active difficulty button
function updateActiveDifficultyButton() {
  // Remove active class from all buttons
  document.querySelectorAll('.difficulty-button').forEach(btn => {
    btn.classList.remove('active');
  });
  
  // Add active class to the appropriate button
  let buttonId = 'qm-easy-mode';
  switch (qmDifficulty) {
    case 'medium':
      buttonId = 'qm-medium-mode';
      break;
    case 'hard':
      buttonId = 'qm-hard-mode';
      break;
    case 'expert':
      buttonId = 'qm-expert-mode';
      break;
  }
  
  const activeButton = document.getElementById(buttonId);
  if (activeButton) {
    activeButton.classList.add('active');
  }
}

// Start the timer
function startQuickMathTimer() {
  clearInterval(qmTimerInterval);
  
  qmTimerInterval = setInterval(function() {
    qmTimer--;
    updateQuickMathTimerDisplay();
    
    if (qmTimer <= 0) {
      handleQuickMathGameOver();
    }
  }, 1000);
}

// Generate a math problem based on difficulty
function generateProblem() {
  let num1, num2, operator, problem, answer;
  
  // Clear previous result
  document.getElementById('math-result').textContent = '';
  
  switch (qmDifficulty) {
    case 'easy':
      // Simple addition and subtraction with small numbers
      num1 = Math.floor(Math.random() * 10) + 1; // 1-10
      num2 = Math.floor(Math.random() * 10) + 1; // 1-10
      operator = Math.random() < 0.5 ? '+' : '-';
      
      // Ensure subtraction doesn't result in negative numbers
      if (operator === '-' && num2 > num1) {
        [num1, num2] = [num2, num1]; // Swap numbers
      }
      
      problem = `${num1} ${operator} ${num2}`;
      answer = operator === '+' ? num1 + num2 : num1 - num2;
      break;
      
    case 'medium':
      // Addition, subtraction, and multiplication with medium numbers
      num1 = Math.floor(Math.random() * 20) + 1; // 1-20
      num2 = Math.floor(Math.random() * 10) + 1; // 1-10
      
      // Random operator (addition, subtraction, multiplication)
      const mediumOperators = ['+', '-', '×'];
      operator = mediumOperators[Math.floor(Math.random() * mediumOperators.length)];
      
      // Ensure subtraction doesn't result in negative numbers
      if (operator === '-' && num2 > num1) {
        [num1, num2] = [num2, num1]; // Swap numbers
      }
      
      problem = `${num1} ${operator} ${num2}`;
      
      if (operator === '+') answer = num1 + num2;
      else if (operator === '-') answer = num1 - num2;
      else answer = num1 * num2;
      break;
      
    case 'hard':
      // All operations including division with larger numbers
      num1 = Math.floor(Math.random() * 50) + 10; // 10-59
      num2 = Math.floor(Math.random() * 15) + 2; // 2-16
      
      // Random operator (all operations)
      const hardOperators = ['+', '-', '×', '÷'];
      operator = hardOperators[Math.floor(Math.random() * hardOperators.length)];
      
      // Handle special cases for division and subtraction
      if (operator === '÷') {
        // Ensure division results in an integer
        num1 = num2 * (Math.floor(Math.random() * 10) + 1);
      } else if (operator === '-' && num2 > num1) {
        [num1, num2] = [num2, num1]; // Swap numbers
      }
      
      problem = `${num1} ${operator} ${num2}`;
      
      if (operator === '+') answer = num1 + num2;
      else if (operator === '-') answer = num1 - num2;
      else if (operator === '×') answer = num1 * num2;
      else answer = num1 / num2;
      break;
      
    case 'expert':
      // Complex problems with multiple operations
      const operations = Math.floor(Math.random() * 2) + 2; // 2-3 operations
      let expression = [];
      let numbers = [];
      let operators = [];
      
      // Generate first number
      numbers.push(Math.floor(Math.random() * 20) + 1);
      
      // Generate operations
      for (let i = 0; i < operations; i++) {
        const expertOperators = ['+', '-', '×', '÷'];
        const op = expertOperators[Math.floor(Math.random() * expertOperators.length)];
        operators.push(op);
        
        let nextNum;
        if (op === '÷') {
          // For division, ensure clean division
          nextNum = Math.floor(Math.random() * 9) + 2; // 2-10
          // Adjust previous number to ensure clean division
          if (i === 0) {
            numbers[0] = nextNum * (Math.floor(Math.random() * 10) + 1);
          }
        } else if (op === '-') {
          // For subtraction, keep numbers manageable
          nextNum = Math.floor(Math.random() * 20) + 1; // 1-20
        } else {
          // For addition and multiplication
          nextNum = Math.floor(Math.random() * 15) + 1; // 1-15
        }
        
        numbers.push(nextNum);
      }
      
      // Build the expression
      for (let i = 0; i < operations; i++) {
        if (i === 0) {
          expression.push(numbers[0]);
        }
        expression.push(operators[i]);
        expression.push(numbers[i + 1]);
      }
      
      problem = expression.join(' ');
      
      // Calculate the answer (using Function constructor to evaluate the expression)
      // Replace × with * and ÷ with / for JavaScript evaluation
      const evalExpression = problem.replace(/×/g, '*').replace(/÷/g, '/');
      answer = eval(evalExpression);
      break;
      
    default:
      // Default to easy
      num1 = Math.floor(Math.random() * 10) + 1;
      num2 = Math.floor(Math.random() * 10) + 1;
      problem = `${num1} + ${num2}`;
      answer = num1 + num2;
  }
  
  // Update display
  document.getElementById('math-problem').textContent = problem;
  
  // Store current problem and answer
  qmCurrentProblem = problem;
  qmCurrentAnswer = answer;
}

// Check the player's answer
function checkAnswer() {
  if (!qmGameRunning) return;
  
  const mathAnswerInput = document.getElementById('math-answer');
  if (!mathAnswerInput) return;
  
  const userAnswer = parseFloat(mathAnswerInput.value);
  
  if (!isNaN(userAnswer)) {
    // Check if answer is correct
    if (userAnswer === qmCurrentAnswer) {
      // Correct answer
      qmStreak++;
      const pointsEarned = calculatePoints();
      qmScore += pointsEarned;
      
      updateQuickMathScoreDisplay();
      updateQuickMathStreakDisplay();
      
      // Show success message
      document.getElementById('math-result').textContent = `Correct! +${pointsEarned} points`;
      document.getElementById('math-result').style.color = '#008000';
      
      // Add time bonus for streak
      if (qmStreak % 5 === 0) {
        const timeBonus = 5;
        qmTimer += timeBonus;
        updateQuickMathTimerDisplay();
        
        // Show bonus message
        setTimeout(() => {
          document.getElementById('math-result').textContent = `Streak bonus! +${timeBonus} seconds`;
        }, 1000);
      }
      
      // Generate new problem after a short delay
      setTimeout(() => {
        generateProblem();
        mathAnswerInput.value = '';
        mathAnswerInput.focus();
      }, 1500);
    } else {
      // Wrong answer
      qmStreak = 0;
      updateQuickMathStreakDisplay();
      
      // Show error message
      document.getElementById('math-result').textContent = `Wrong! The answer is ${qmCurrentAnswer}`;
      document.getElementById('math-result').style.color = '#CC0000';
      
      // Generate new problem after a short delay
      setTimeout(() => {
        generateProblem();
        mathAnswerInput.value = '';
        mathAnswerInput.focus();
      }, 2000);
    }
  }
}

// Calculate points based on difficulty and streak
function calculatePoints() {
  let basePoints;
  
  // Base points by difficulty
  switch (qmDifficulty) {
    case 'easy':
      basePoints = 10;
      break;
    case 'medium':
      basePoints = 20;
      break;
    case 'hard':
      basePoints = 30;
      break;
    case 'expert':
      basePoints = 50;
      break;
    default:
      basePoints = 10;
  }
  
  // Streak multiplier (max 3x)
  const streakMultiplier = Math.min(1 + (qmStreak * 0.1), 3);
  
  return Math.round(basePoints * streakMultiplier);
}

// Handle game over
function handleQuickMathGameOver() {
  qmGameRunning = false;
  qmGameOver = true;
  clearInterval(qmTimerInterval);
  
  // Disable input
  const mathAnswerInput = document.getElementById('math-answer');
  if (mathAnswerInput) {
    mathAnswerInput.disabled = true;
  }
  
  // Show game over message
  document.getElementById('math-problem').textContent = 'GAME OVER';
  document.getElementById('math-result').textContent = `Final Score: ${qmScore} (${qmDifficulty})`;
  document.getElementById('math-result').style.color = '#0A246A';
  
  // Save high score
  const isHighScore = saveQuickMathHighScore(qmScore);
  if (isHighScore) {
    // If it's a high score, update the message
    setTimeout(function() {
      document.getElementById('math-result').textContent = `NEW HIGH SCORE: ${qmScore} (${qmDifficulty})`;
    }, 1000);
  }
}

// Update score display
function updateQuickMathScoreDisplay() {
  const scoreElement = document.getElementById('quickmath-score');
  if (scoreElement) {
    scoreElement.textContent = qmScore;
  }
}

// Update streak display
function updateQuickMathStreakDisplay() {
  const streakElement = document.getElementById('quickmath-streak');
  if (streakElement) {
    streakElement.textContent = qmStreak;
  }
}

// Update timer display
function updateQuickMathTimerDisplay() {
  const timerElement = document.getElementById('quickmath-timer');
  if (timerElement) {
    timerElement.textContent = qmTimer;
  }
}

// Save high score
function saveQuickMathHighScore(score) {
  const highScoreKey = `quickmath-high-score-${qmDifficulty}`;
  const currentHighScore = localStorage.getItem(highScoreKey) || 0;
  
  if (score > currentHighScore) {
    localStorage.setItem(highScoreKey, score);
    return true;
  }
  return false;
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the game
  initQuickMathGame();
});
